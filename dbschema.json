[{"table_name": "brands", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()", "foreign_key": "brands_pkey", "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": "brands_name_key", "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "slug", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": "brands_slug_key", "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "logo_url", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "sponsored", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "updated_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "version", "data_type": "bigint", "is_nullable": "YES", "column_default": "1", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "brands", "column_name": "search_vector", "data_type": "tsvector", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "categories", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()", "foreign_key": "categories_pkey", "referenced_table": null, "referenced_column": null}, {"table_name": "categories", "column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "categories", "column_name": "slug", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": "categories_slug_key", "referenced_table": null, "referenced_column": null}, {"table_name": "categories", "column_name": "parent_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null, "foreign_key": "categories_parent_id_fkey", "referenced_table": "categories", "referenced_column": "id"}, {"table_name": "categories", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "categories", "column_name": "sponsored", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "categories", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "categories", "column_name": "version", "data_type": "bigint", "is_nullable": "YES", "column_default": "1", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "product_retailer_offers", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()", "foreign_key": "product_retailer_offers_pkey", "referenced_table": null, "referenced_column": null}, {"table_name": "product_retailer_offers", "column_name": "product_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null, "foreign_key": "product_retailer_offers_product_fk", "referenced_table": "products", "referenced_column": "id"}, {"table_name": "product_retailer_offers", "column_name": "retailer_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null, "foreign_key": "product_retailer_offers_retailer_fk", "referenced_table": "retailers", "referenced_column": "id"}, {"table_name": "product_retailer_offers", "column_name": "price", "data_type": "numeric", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "product_retailer_offers", "column_name": "stock_status", "data_type": "text", "is_nullable": "YES", "column_default": "'in_stock'::text", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "product_retailer_offers", "column_name": "url", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "product_retailer_offers", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "product_retailer_offers", "column_name": "updated_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "product_retailer_offers", "column_name": "version", "data_type": "bigint", "is_nullable": "YES", "column_default": "1", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()", "foreign_key": "products_pkey", "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "slug", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": "products_slug_key", "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "brand_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null, "foreign_key": "products_brand_id_fkey", "referenced_table": "brands", "referenced_column": "id"}, {"table_name": "products", "column_name": "category_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null, "foreign_key": "products_category_id_fkey", "referenced_table": "categories", "referenced_column": "id"}, {"table_name": "products", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "specifications", "data_type": "jsonb", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "images", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": "'active'::character varying", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "is_featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "is_sponsored", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "updated_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "version", "data_type": "bigint", "is_nullable": "YES", "column_default": "1", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "search_vector", "data_type": "tsvector", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "cashback_amount", "data_type": "numeric", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "products", "column_name": "promotion_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null, "foreign_key": "products_promotion_fk", "referenced_table": "promotions", "referenced_column": "id"}, {"table_name": "products", "column_name": "model_number", "data_type": "text", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()", "foreign_key": "promotions_pkey", "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "brand_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null, "foreign_key": "promotions_brand_id_fkey", "referenced_table": "brands", "referenced_column": "id"}, {"table_name": "promotions", "column_name": "category_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null, "foreign_key": "promotions_category_id_fkey", "referenced_table": "categories", "referenced_column": "id"}, {"table_name": "promotions", "column_name": "title", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "max_cashback_amount", "data_type": "numeric", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "purchase_start_date", "data_type": "date", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "purchase_end_date", "data_type": "date", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "claim_start_offset_days", "data_type": "integer", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "claim_window_days", "data_type": "integer", "is_nullable": "NO", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "terms_url", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "terms_description", "data_type": "text", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'draft'::promotion_status_type", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "last_validated_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "version", "data_type": "bigint", "is_nullable": "YES", "column_default": "1", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "promotions", "column_name": "is_featured", "data_type": "boolean", "is_nullable": "NO", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()", "foreign_key": "retailers_pkey", "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": "retailers_name_key", "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "slug", "data_type": "character varying", "is_nullable": "NO", "column_default": null, "foreign_key": "retailers_slug_key", "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "logo_url", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": "'active'::character varying", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "sponsored", "data_type": "boolean", "is_nullable": "YES", "column_default": "false", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "api_key_hash", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "api_secret_hash", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "created_at", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "version", "data_type": "bigint", "is_nullable": "YES", "column_default": "1", "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "claim_period", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}, {"table_name": "retailers", "column_name": "website_url", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "foreign_key": null, "referenced_table": null, "referenced_column": null}]