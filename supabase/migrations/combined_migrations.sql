begin;

-- Brand category summary function
CREATE OR REPLACE FUNCTION get_brand_category_summary(brand_id_param UUID)
RETURNS TABLE (
	category_name TEXT,
	max_cashback DECIMAL
) AS $$
BEGIN
	RETURN QUERY
	SELECT 
		c.name,
		MAX(p.cashback_rate) AS max_cashback
	FROM categories c
	JOIN promotions p ON c.id = p.category_id
	WHERE p.status = 'active' 
	AND p.brand_id = brand_id_param
	GROUP BY c.name;
END;
$$ LANGUAGE plpgsql;

-- Add search vector to brands table
alter table public.brands add column if not exists search_vector tsvector
	generated always as (
		setweight(to_tsvector('english', name), 'A') ||
		setweight(to_tsvector('english', coalesce(description, '')), 'B')
	) stored;

-- Create indexes for brand search
create index if not exists brands_search_idx on public.brands using gin (search_vector);
create index if not exists idx_brands_name_search on public.brands using gin (to_tsvector('english', name));
create index if not exists idx_brands_name_ilike on public.brands (name text_pattern_ops);

commit;