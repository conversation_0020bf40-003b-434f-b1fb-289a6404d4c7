// generate-retailers-migration.js
const fs = require('fs');
const Papa = require('papaparse');

// Function to generate a base slug
function generateBaseSlug(name) {
    return name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

// Function to generate timestamp
function getTimestamp() {
    const now = new Date("2024-02-15T12:00:00Z");
    return now.toISOString()
        .replace(/[^0-9]/g, '')
        .slice(0, 14);
}

async function generateMigration() {
    try {
        const csvContent = fs.readFileSync('samsung qualiftying partners  Sheet1.csv', 'utf-8');

        const { data } = Papa.parse(csvContent, {
            header: true,
            skipEmptyLines: true,
            transformHeader: header => header.trim()
        });

        // Handle slug generation with deduplication
        const slugCounts = new Map();
        const retailers = data.map(row => {
            const name = row.Name;
            const baseSlug = generateBaseSlug(name);

            // Track slug usage and append number if needed
            const count = slugCounts.get(baseSlug) || 0;
            slugCounts.set(baseSlug, count + 1);

            // Generate final slug
            const finalSlug = count === 0 ? baseSlug : `${baseSlug}-${count}`;

            return {
                name: name.replace(/'/g, "''"),
                slug: finalSlug,
                claimPeriod: row['Claim Period (Days)'].replace(/\s/g, '')
            };
        });

        // Generate SQL VALUES
        const values = retailers.map(r =>
            `    ('${r.name}', '${r.slug}', '${r.claimPeriod}', 'active', false, false, CURRENT_TIMESTAMP, 1)`
        ).join(',\n');

        const migrationContent = `-- Migration file: Create and populate retailers table
--
-- Description: 
-- Creates retailers table and populates with Samsung promotion data
-- Handles duplicate slugs by appending numbers
-- 
-- Generated at: ${new Date().toISOString()}

-- Create retailers table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.retailers (
    id uuid not null default gen_random_uuid(),
    name character varying(150) not null,
    slug character varying(150) not null,
    logo_url character varying(255) null,
    status character varying(20) null default 'active'::character varying,
    featured boolean null default false,
    sponsored boolean null default false,
    api_key_hash character varying(255) null,
    api_secret_hash character varying(255) null,
    created_at timestamp without time zone null default CURRENT_TIMESTAMP,
    version bigint null default 1,
    claim_period character varying(50) null,
    constraint retailers_pkey primary key (id),
    constraint retailers_name_key unique (name),
    constraint retailers_slug_key unique (slug)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_retailers_claim_period ON public.retailers(claim_period);
CREATE INDEX IF NOT EXISTS idx_retailers_status ON public.retailers(status);

-- Insert retailers with deduplicated slugs
INSERT INTO public.retailers 
    (name, slug, claim_period, status, featured, sponsored, created_at, version)
VALUES
${values}
ON CONFLICT (slug) 
DO NOTHING;

-- Create version update trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_version()
RETURNS TRIGGER AS $$
BEGIN
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create cleanup trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION cleanup_retailer_skus()
RETURNS TRIGGER AS $$
BEGIN
    -- Add your cleanup logic here if needed
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS update_retailer_version ON retailers;
CREATE TRIGGER update_retailer_version
    BEFORE UPDATE ON retailers
    FOR EACH ROW
    EXECUTE FUNCTION update_version();

DROP TRIGGER IF EXISTS cleanup_retailer_skus_trigger ON retailers;
CREATE TRIGGER cleanup_retailer_skus_trigger
    BEFORE DELETE ON retailers
    FOR EACH ROW
    EXECUTE FUNCTION cleanup_retailer_skus();
`;

        const timestamp = getTimestamp();
        const filename = `${timestamp}_create_and_populate_retailers.sql`;

        fs.writeFileSync(filename, migrationContent);

        console.log(`✅ Migration file generated successfully as: ${filename}`);
        console.log(`📊 Total retailers processed: ${retailers.length}`);
        console.log('Duplicate slugs handled:');
        Array.from(slugCounts.entries())
            .filter(([_, count]) => count > 1)
            .forEach(([slug, count]) => {
                console.log(`  ${slug}: ${count} occurrences`);
            });

    } catch (error) {
        console.error('❌ Error generating migration:', error);
    }
}

generateMigration();