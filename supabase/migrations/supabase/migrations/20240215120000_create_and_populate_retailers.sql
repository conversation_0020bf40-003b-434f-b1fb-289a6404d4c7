-- Migration file: Create and populate retailers table
--
-- Description: 
-- Creates retailers table and populates with Samsung promotion data
-- <PERSON><PERSON> duplicate slugs by appending numbers
-- 
-- Generated at: 2025-02-15T16:26:26.521Z

-- Create retailers table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.retailers (
    id uuid not null default gen_random_uuid(),
    name character varying(150) not null,
    slug character varying(150) not null,
    logo_url character varying(255) null,
    status character varying(20) null default 'active'::character varying,
    featured boolean null default false,
    sponsored boolean null default false,
    api_key_hash character varying(255) null,
    api_secret_hash character varying(255) null,
    created_at timestamp without time zone null default CURRENT_TIMESTAMP,
    version bigint null default 1,
    claim_period character varying(50) null,
    constraint retailers_pkey primary key (id),
    constraint retailers_name_key unique (name),
    constraint retailers_slug_key unique (slug)
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_retailers_claim_period ON public.retailers(claim_period);
CREATE INDEX IF NOT EXISTS idx_retailers_status ON public.retailers(status);

-- Insert retailers with deduplicated slugs
INSERT INTO public.retailers 
    (name, slug, claim_period, status, featured, sponsored, created_at, version)
VALUES
    ('1two2 Kitchen Design Ltd', '1two2-kitchen-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('3 Step Designs Ltd', '3-step-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('3D Supplies (MCR) Ltd', '3d-supplies-mcr-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('4 Homes Ltd', '4-homes-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A & B Waters Ltd', 'a-b-waters-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A & H Kitchens Ltd', 'a-h-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A & J Kitchens & Bathrooms Ltd', 'a-j-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A & S Home Design Ltd', 'a-s-home-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A & W Pounds Ltd', 'a-w-pounds-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A B Joinery Kitchens & Bedrooms', 'a-b-joinery-kitchens-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A G B Narib Ltd', 'a-g-b-narib-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A Gater Ltd', 'a-gater-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A M Kitchens & Interiors Ltd', 'a-m-kitchens-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A Martens Kitchens and Bathrooms', 'a-martens-kitchens-and-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A McKenzie & Sons', 'a-mckenzie-sons', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A N Dodds Electrical', 'a-n-dodds-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A Shanks', 'a-shanks', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A W Myhill & Son Ltd', 'a-w-myhill-son-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A.B.L (North West) Ltd', 'abl-north-west-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A.Gough Kitchen Supplies Ltd', 'agough-kitchen-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A.K.Fitted Interiors Ltd', 'akfitted-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A.R.Jolly Limited', 'arjolly-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('A1 Spares', 'a1-spares', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Abbey Kitchens', 'abbey-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Aber Gas Ltd', 'aber-gas-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Aberdeenshire Kitchens Ltd', 'aberdeenshire-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ability Kitchens Ltd', 'ability-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ABK Manufacturing Ltd', 'abk-manufacturing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ablewood Ltd', 'ablewood-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ABM Building Services Ltd', 'abm-building-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Accurate Professional Ltd', 'accurate-professional-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Aclands Radio & TV Ltd', 'aclands-radio-tv-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Acorn Kitchens & Bedrooms Ltd', 'acorn-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Acquastone Ltd', 'acquastone-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ACR Makeovers Ltd', 'acr-makeovers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Acrosskeep Ltd', 'acrosskeep-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Active Plumbing Supplies (Calne)', 'active-plumbing-supplies-calne', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AD3 Design Ltd', 'ad3-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adam Davis', 'adam-davis', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adam Elliot Ltd', 'adam-elliot-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adam Jelfs T/a', 'adam-jelfs-ta', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adams & Jarrett (Retail) Ltd', 'adams-jarrett-retail-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adams Cook & Pearce Ltd', 'adams-cook-pearce-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adams Supreme Kitchen', 'adams-supreme-kitchen', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adcock & Sons Ltd', 'adcock-sons-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adina Developments Ltd', 'adina-developments-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adrian Cundle', 'adrian-cundle', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Adrian Greenley', 'adrian-greenley', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Advanced Interior Solutions', 'advanced-interior-solutions', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Affordable & Stylish Ltd', 'affordable-stylish-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Affordable Kitchens & Bathrooms', 'affordable-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AGC Retail Ltd', 'agc-retail-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AJ Groundworks Limited', 'aj-groundworks-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AJC Group', 'ajc-group', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AJD Construction', 'ajd-construction', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AL Kitchens and Bedrooms', 'al-kitchens-and-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Alan Mackie', 'alan-mackie', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Alan McCormick', 'alan-mccormick', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Alan Morrice', 'alan-morrice', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Alan Mummery', 'alan-mummery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Alastair Booth Ltd', 'alastair-booth-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Albany Radio (Enfield) Ltd', 'albany-radio-enfield-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Aldershot Kitchen Designs Ltd', 'aldershot-kitchen-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Allfix (UK) Limited', 'allfix-uk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Allspares (Wigan) Ltd', 'allspares-wigan-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Alma Sage Ventures Ltd', 'alma-sage-ventures-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Almar Construction Ltd', 'almar-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Amazon EU S.a.r.L.', 'amazon-eu-sarl', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Amethyst Kitchens Ltd', 'amethyst-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Amwell Kitchens Ltd', 'amwell-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andersen''s Kitchen Design Limited', 'andersens-kitchen-design-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Carter', 'andrew-carter', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Davis', 'andrew-davis', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Edwards', 'andrew-edwards', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Fitzpatrick', 'andrew-fitzpatrick', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Harris', 'andrew-harris', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Mitchell', 'andrew-mitchell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Norris Kitchen & Bathroom', 'andrew-norris-kitchen-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Stanway', 'andrew-stanway', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Tate', 'andrew-tate', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrew Thomson', 'andrew-thomson', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrews Home Appliances Ltd', 'andrews-home-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andrzej Szczygiel', 'andrzej-szczygiel', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andy Latimer', 'andy-latimer', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Andy Magin', 'andy-magin', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Angelos KBB Ltd', 'angelos-kbb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Anglia Interiors Ltd', 'anglia-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Anglian Electrics', 'anglian-electrics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Anil and Vimmi Passi', 'anil-and-vimmi-passi', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Anteus Consulting Ltd', 'anteus-consulting-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Anthony Foster', 'anthony-foster', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Anthony L Young Ltd', 'anthony-l-young-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AO', 'ao', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('AP Kitchen Solutions Limited', 'ap-kitchen-solutions-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Apollo Appliances', 'apollo-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliance City', 'appliance-city', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliance Direct (Morecambe) Ltd', 'appliance-direct-morecambe-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliance Electronics UK Ltd', 'appliance-electronics-uk-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliance Express Ltd', 'appliance-express-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliance Monkeys Limited', 'appliance-monkeys-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliance People Ltd', 'appliance-people-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliance World', 'appliance-world', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Appliances Direct', 'appliances-direct', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Arcadia Home and Living Ltd', 'arcadia-home-and-living-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Arcelo Limited', 'arcelo-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Archers Television Service Ltd', 'archers-television-service-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Arcot Grange Developments Ltd', 'arcot-grange-developments-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ard Kitchens & Bathrooms Ltd', 'ard-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Argos', 'argos', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Armada Kitchens & Bathrooms LLP', 'armada-kitchens-bathrooms-llp', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Armstrong Construction (Hull) Limit', 'armstrong-construction-hull-limit', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Arnold Interiors Ltd', 'arnold-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Arran Domestic Service Centre', 'arran-domestic-service-centre', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Arrol Kitchen Co Ltd', 'arrol-kitchen-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Arysto Ltd', 'arysto-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ashbrooke Manufacturing Ltd', 'ashbrooke-manufacturing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ashley Ann Ltd', 'ashley-ann-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ashoak Construction Ltd', 'ashoak-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ashton Domestic Appliances Ltd', 'ashton-domestic-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ashvale Contracting Ltd', 'ashvale-contracting-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ashwood Carpentry & Joinery Ltd', 'ashwood-carpentry-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ask (GB) Ltd', 'ask-gb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Aspire Park & Leisure Homes Ltd', 'aspire-park-leisure-homes-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ATD Developments Ltd', 'atd-developments-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Atelier Bespoke Cabinetry Ltd', 'atelier-bespoke-cabinetry-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Atelier Joinery Ltd', 'atelier-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Atkinson Group Ltd', 'atkinson-group-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Atlantic Developments Devon Ltd', 'atlantic-developments-devon-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Atlantic Electronics Ltd', 'atlantic-electronics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ATO Industries Limited', 'ato-industries-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Audus Interiors Ltd', 'audus-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Augustus Stickland Furniture', 'augustus-stickland-furniture', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Avanti Fitted Kitchens Ltd', 'avanti-fitted-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Avanti Kitchens & Bedrooms Ltd', 'avanti-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Avensys Ltd', 'avensys-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Aztec Design Ltd', 'aztec-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Aztec Interiors (Leicester) Limited', 'aztec-interiors-leicester-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('B A Yeomans & Son Ltd', 'b-a-yeomans-son-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('B B Trade Kitchens & Bedrooms Limit', 'b-b-trade-kitchens-bedrooms-limit', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('B H Interiors', 'b-h-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('B H Remodelling Kitchens &', 'b-h-remodelling-kitchens-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('B. Finch', 'b-finch', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bagshots Limited', 'bagshots-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Barker Fleming Ltd', 'barker-fleming-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Barmol Ltd', 'barmol-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Barrett & Swan', 'barrett-swan', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Barrie McCarthy', 'barrie-mccarthy', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Barrmuir Kitchen & Bathroom', 'barrmuir-kitchen-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Barry D Palmer Joinery & Carpentry', 'barry-d-palmer-joinery-carpentry', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Basil Knipe Electrics', 'basil-knipe-electrics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bathroom Installers Ltd', 'bathroom-installers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bathroom Warehouse (Midlands)', 'bathroom-warehouse-midlands', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bathrooms Direct & Bathroom', 'bathrooms-direct-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Baytree Kitchens UK Ltd', 'baytree-kitchens-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('BB Fencing Ltd', 'bb-fencing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('BCE & A Ltd', 'bce-a-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Be Creative Solutions Limited', 'be-creative-solutions-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beacon South West Ltd', 'beacon-south-west-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beagle Leisure Ltd', 'beagle-leisure-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beam Kitchens Ltd', 'beam-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beaumont Home Centre Limited', 'beaumont-home-centre-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beaupierre Joinery', 'beaupierre-joinery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bee Cabinets Ltd', 'bee-cabinets-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beechwood Builders', 'beechwood-builders', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Belhaven KBB Limited', 'belhaven-kbb-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Belhaven KBB Ltd', 'belhaven-kbb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bellevue Bespoke Ltd', 'bellevue-bespoke-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bells Domestics', 'bells-domestics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ben Jordan', 'ben-jordan', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bentley & Rowe Ltd', 'bentley-rowe-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bergari Ltd', 'bergari-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beric Appliances', 'beric-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Berjen Limited', 'berjen-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Berkshire Kitchen Design Ltd', 'berkshire-kitchen-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bernsteins Direct Ltd', 'bernsteins-direct-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bespoke Kitchens & Home', 'bespoke-kitchens-home', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bespoke Trade Kitchens Ltd', 'bespoke-trade-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('BESPOKE-UK', 'bespoke-uk', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beyond Bathrooms Ltd', 'beyond-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Beyond Television', 'beyond-television', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Billy Walker Joinery Services', 'billy-walker-joinery-services', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Birkbecks Electrical Ltd', 'birkbecks-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('BJ''S (Mid Wales) Ltd', 'bjs-mid-wales-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Blackbrook Interiors Ltd', 'blackbrook-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Blackwood Bespoke Ltd', 'blackwood-bespoke-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Blos Development Ltd', 'blos-development-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Blossom Hill Homes Ltd', 'blossom-hill-homes-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Blue Arches Ltd', 'blue-arches-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Blue Badger Wholesale Ltd', 'blue-badger-wholesale-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bluewater Bathrooms Ltd', 'bluewater-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Boiler and Bath Ltd', 'boiler-and-bath-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Boiler Friendly Heating Services', 'boiler-friendly-heating-services', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Booth Scotland Ltd', 'booth-scotland-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Borsch ltd', 'borsch-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Boundary DIY Ltd', 'boundary-diy-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bower Interiors', 'bower-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Box Ltd', 'box-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Boxhill Partnership Ltd', 'boxhill-partnership-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Boylans TV Ltd', 'boylans-tv-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Boys & Boden Ltd', 'boys-boden-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('BPC Interiors Ltd', 'bpc-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('BPM Bathrooms', 'bpm-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bradfords Building Supplies Ltd', 'bradfords-building-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Brandt Kitchens Ltd', 'brandt-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bransons Home Garden & Aquatic', 'bransons-home-garden-aquatic', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Braverman Kitchens Ltd', 'braverman-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Brian Pugh', 'brian-pugh', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Brian Ramwell Classic Interiors', 'brian-ramwell-classic-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Brian Tough', 'brian-tough', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Brightmore & Sons (Burbage) Ltd', 'brightmore-sons-burbage-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Broadheath Construction Ltd', 'broadheath-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Broadway Music & Vision', 'broadway-music-vision', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bromley Cross Kitchens &', 'bromley-cross-kitchens-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Brownhirst Joinery Ltd', 'brownhirst-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Brynmor Ltd', 'brynmor-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bryson (Construction) Ltd', 'bryson-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('BTM Trading Ltd', 'btm-trading-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Buckle Invest Ltd', 'buckle-invest-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Buildbase', 'buildbase', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Builder Depot Ltd', 'builder-depot-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Buildstop Ltd', 'buildstop-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Built In Appliances Ltd', 'built-in-appliances-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Built in Kitchen Appliances', 'built-in-kitchen-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Built In Kitchen Appliances Ltd', 'built-in-kitchen-appliances-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Built-In Appliances Ltd', 'built-in-appliances-ltd-1', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bullys Wood Works Ltd', 'bullys-wood-works-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Bunny Appliance Warehouse', 'bunny-appliance-warehouse', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Burley Interiors Manufacturing Ltd', 'burley-interiors-manufacturing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Burton & Sherwood Ltd', 'burton-sherwood-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Burwoods Domestic Appl. Ltd', 'burwoods-domestic-appl-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Buun Ltd', 'buun-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Buxton Woodworks (2014) Limited', 'buxton-woodworks-2014-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Buy it Direct (Appliances Direct)', 'buy-it-direct-appliances-direct', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('By Balneo Ltd', 'by-balneo-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Byles Of Hyde Ltd', 'byles-of-hyde-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C & C Kitchens Cardiff Ltd', 'c-c-kitchens-cardiff-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C & G Interiors Ltd', 'c-g-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C B Property Renovations Ltd', 'c-b-property-renovations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C Brewer & Sons Ltd', 'c-brewer-sons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C F Hall & Co Ltd', 'c-f-hall-co-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C J Hampshire Ltd', 'c-j-hampshire-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C S Kitchen Solutions Ltd', 'c-s-kitchen-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C S Suppliers Ltd', 'c-s-suppliers-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C T Bell (Crowthorne) Ltd', 'c-t-bell-crowthorne-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C W Collier & Sons (1935) Ltd', 'c-w-collier-sons-1935-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C W Strickland', 'c-w-strickland', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('C.Mitchell & P.Mitchell', 'cmitchell-pmitchell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cabinets Direct Ltd', 'cabinets-direct-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cadbury Kitchens Ltd', 'cadbury-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cader Kitchens Ltd', 'cader-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cadwells Kitchens Ltd', 'cadwells-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Caldicot Kitchen & Bathroom', 'caldicot-kitchen-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cambabest Ltd', 'cambabest-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cannadines Ltd', 'cannadines-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cannock Kitchens Ltd', 'cannock-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Capital Appliance Centre Ltd', 'capital-appliance-centre-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Capital Kitchens & Bedrooms Ltd', 'capital-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Capital Repairs', 'capital-repairs', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Carander Construction Ltd', 'carander-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cardigan Kitchen & Tile Co Ltd', 'cardigan-kitchen-tile-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Carl Graham Interiors Ltd', 'carl-graham-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Caro Design Ltd', 'caro-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Casa Kitchens & Bathrooms Ltd', 'casa-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cask Construction Ltd', 'cask-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Castleford Domestic Appliances Ltd', 'castleford-domestic-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Castlemead Homecraft Ltd', 'castlemead-homecraft-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('CD Electrics & Home Furnishings', 'cd-electrics-home-furnishings', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('CDH Contractors Ltd', 'cdh-contractors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cee-Jay Electronics', 'cee-jay-electronics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ceginau Aspire Kitchens', 'ceginau-aspire-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Celtic Granite Solutions Ltd', 'celtic-granite-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cerkini & Co Ltd', 'cerkini-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chace Kendrick Ltd', 'chace-kendrick-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chameleon Bedrooms & Kitchens', 'chameleon-bedrooms-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Channel Island Ceramics Ltd', 'channel-island-ceramics-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chapman & Son', 'chapman-son', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Charles Gray Kitchens Ltd', 'charles-gray-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Charlestown Bathrooms Ltd', 'charlestown-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Checkers Discounts DIY Ltd', 'checkers-discounts-diy-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cherrywood Interiors Design and', 'cherrywood-interiors-design-and', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cheshire Kitchen Projects Ltd', 'cheshire-kitchen-projects-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chesilton Design Ltd', 'chesilton-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chiselwood Ltd', 'chiselwood-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chris Knall', 'chris-knall', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chris Lockwood', 'chris-lockwood', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chris Looker', 'chris-looker', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chris Peers', 'chris-peers', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chris Sharp Cabinets Ltd', 'chris-sharp-cabinets-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chrisdan Building Services Ltd', 'chrisdan-building-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Chrislin Kitchens Ltd', 'chrislin-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Christopher Carroll', 'christopher-carroll', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Christopher Howard', 'christopher-howard', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Christopher Jones', 'christopher-jones', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Christopher Lawrence Designs Ltd', 'christopher-lawrence-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Churchill Interiors Ltd', 'churchill-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('CIH', 'cih', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('CK Home Appliances Ltd', 'ck-home-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Claris Housewares & Stationery Solu', 'claris-housewares-stationery-solu', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Classic Kitchens (Whitland) Ltd', 'classic-kitchens-whitland-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Classic Shaker Design Ltd', 'classic-shaker-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Classique Bedrooms Ltd.', 'classique-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cleary and Hall Limited', 'cleary-and-hall-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cleethorpes Kitchen Centre Ltd', 'cleethorpes-kitchen-centre-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cmd Design Ltd', 'cmd-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Coalville Kitchens & Bedrooms', 'coalville-kitchens-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Coastal Kitchens Ltd', 'coastal-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Coastline Bathroom & Kitchens', 'coastline-bathroom-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cocteau Ltd', 'cocteau-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colemans of Dorset Ltd', 'colemans-of-dorset-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colin M Smith', 'colin-m-smith', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colin Torode Ltd', 'colin-torode-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colman Bros Ltd', 'colman-bros-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colour House Interior Design Ltd', 'colour-house-interior-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colour Supplies Ltd', 'colour-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colourcare Home Technology Ltd', 'colourcare-home-technology-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Colourhill Ltd', 'colourhill-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Commercial Wholesale Trading Ltd', 'commercial-wholesale-trading-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Complete Bathroom and Tiling', 'complete-bathroom-and-tiling', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Complete Kitchens Ltd', 'complete-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Concept Cupboards Ltd', 'concept-cupboards-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Concept Sliding Solutions Ltd', 'concept-sliding-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Conels Furniture Ltd', 'conels-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Connect', 'connect', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Connect Property Improvements', 'connect-property-improvements', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Consumer Buyers Ltd', 'consumer-buyers-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Contessa Kitchens Ltd', 'contessa-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cook & Sleep Kitchens & Bedrooms', 'cook-sleep-kitchens-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Coomo UK Limited', 'coomo-uk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Coopers Stores', 'coopers-stores', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cornwall Appl Services Ltd', 'cornwall-appl-services-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Corrie Paul Kitchens Ltd', 'corrie-paul-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cosi Living Ltd', 'cosi-living-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Costco', 'costco', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Coughtrey BDK Ltd', 'coughtrey-bdk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('County Kitchens (Leyburn) Ltd', 'county-kitchens-leyburn-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Craig Barrell', 'craig-barrell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Craig Byars', 'craig-byars', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Craig Forward', 'craig-forward', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crampsie Electrical Ltd', 'crampsie-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crampton and Moore', 'crampton-and-moore', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cre8 Kitchens, Bathrooms and', 'cre8-kitchens-bathrooms-and', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cre8tive Kitchen Co Ltd', 'cre8tive-kitchen-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Createch Kiosks Ltd', 'createch-kiosks-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Creating Better Homes Ltd', 'creating-better-homes-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Creating Spaces Group Ltd', 'creating-spaces-group-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Creative Design & Build', 'creative-design-build', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Creative Kitchen & Bathroom', 'creative-kitchen-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crest Bathrooms Ltd', 'crest-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crestwood (Southern) Ltd', 'crestwood-southern-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Criss Supplies Ltd', 'criss-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crown Homeworks Ltd', 'crown-homeworks-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crown Kitchens Direct Ltd', 'crown-kitchens-direct-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crown Products (Kent) Ltd', 'crown-products-kent-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Crownlea Ltd', 'crownlea-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('CRP Carpentry', 'crp-carpentry', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cucina Kitchens Ltd', 'cucina-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Culina Ltd', 'culina-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cullinan Interiors Ltd', 'cullinan-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Curiel Kitchen Designs Ltd', 'curiel-kitchen-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Currys', 'currys', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cutsburn Holdings Ltd', 'cutsburn-holdings-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Cyril Campbell (Electrics) Ltd', 'cyril-campbell-electrics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D & A Ingram Kitchens &', 'd-a-ingram-kitchens-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D & M Kitchens', 'd-m-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D & R Kitchens Ltd', 'd-r-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D B Domestics Appliances Ltd', 'd-b-domestics-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D C Kitchens Ltd', 'd-c-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D J Clue & Son', 'd-j-clue-son', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D J P Domestic Appliances Ltd', 'd-j-p-domestic-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D M Joinery Strathblane Ltd', 'd-m-joinery-strathblane-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D W F Developments Ltd', 'd-w-f-developments-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D Wilson & Sons Ltd', 'd-wilson-sons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D.B. Domestic Appliances Limited', 'db-domestic-appliances-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D&C Electrics t/a Hadwins', 'dc-electrics-ta-hadwins', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('D&G Ayre Electrical Ltd', 'dg-ayre-electrical-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('DAC Appliances Ltd', 'dac-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dacombes & Renaut Ltd', 'dacombes-renaut-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Daia Ltd', 'daia-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dale Interiors of Jacksdale Ltd', 'dale-interiors-of-jacksdale-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dale Jones', 'dale-jones', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dalzells', 'dalzells', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dalzells of Markethill', 'dalzells-of-markethill', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Daniel Smith', 'daniel-smith', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Darren Adamson', 'darren-adamson', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Darren Seager', 'darren-seager', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dartmouth Kitchens & Interiors', 'dartmouth-kitchens-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dave Stone Design Chesterfield', 'dave-stone-design-chesterfield', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('David Hewitt', 'david-hewitt', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('David J Martin Joinery Ltd', 'david-j-martin-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('David McCarron', 'david-mccarron', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('David Pyatt', 'david-pyatt', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('David Taylor', 'david-taylor', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('David Wall', 'david-wall', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('David Wood', 'david-wood', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Davies Shaw Ltd', 'davies-shaw-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Davonport Furniture Ltd', 'davonport-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dawlish Kitchens & Bedrooms Ltd', 'dawlish-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('DCS Universal Carpentry &', 'dcs-universal-carpentry-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Debra Carroll', 'debra-carroll', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dekor Kitchens Ltd', 'dekor-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Delamere Bespoke Interiors Ltd T/a', 'delamere-bespoke-interiors-ltd-ta', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Delta House & Home', 'delta-house-home', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Delta Ireland Ltd', 'delta-ireland-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dennis Coward', 'dennis-coward', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Derby Kitchens Limited', 'derby-kitchens-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Derek Massey Kitchens and Baths', 'derek-massey-kitchens-and-baths', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Derek Morris', 'derek-morris', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Design House Yorkshire Ltd', 'design-house-yorkshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Design Solution Projects Ltd', 'design-solution-projects-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Designa of Weymouth Ltd', 'designa-of-weymouth-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Designerkitchendirect Limited', 'designerkitchendirect-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Desology Ltd', 'desology-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('DFB Joinery Ltd', 'dfb-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Direct Factory Kitchens Ltd', 'direct-factory-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Discount Appliance Centre Ltd', 'discount-appliance-centre-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dixon & Grey Kitchens Ltd', 'dixon-grey-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dolcia Distribution Ltd', 'dolcia-distribution-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Domestic App Maint (D''Set) Ltd', 'domestic-app-maint-dset-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Domestic Apps (Sudbury) Ltd', 'domestic-apps-sudbury-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dominic Ash Ltd', 'dominic-ash-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dominic Smith', 'dominic-smith', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Don Macisaac', 'don-macisaac', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Donaghy Bros Limavady', 'donaghy-bros-limavady', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Donaghy Brothers', 'donaghy-brothers', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Donald Baugh', 'donald-baugh', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Door Designs Ltd', 'door-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dorset Street Holdings Ltd', 'dorset-street-holdings-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Downend Kitchen Company Ltd', 'downend-kitchen-company-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Doxford Home Improvements Ltd', 'doxford-home-improvements-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dragonfly Kitchens Ltd', 'dragonfly-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Dream Doors Ltd', 'dream-doors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Duncans of Grantown Ltd', 'duncans-of-grantown-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Durham Kitchens & Bathrooms Ltd', 'durham-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('E B Marsh & Son Ltd', 'e-b-marsh-son-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('E H Smith (Builders Merchants)', 'e-h-smith-builders-merchants', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('E&H Services', 'eh-services', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('East Berkshire Trading Ltd', 'east-berkshire-trading-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('East Coast Designed Kitchens &', 'east-coast-designed-kitchens-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('EBKB Ltd', 'ebkb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Eclipse Carpentry', 'eclipse-carpentry', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ECO Interiors Winchester Ltd', 'eco-interiors-winchester-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Eddie Preston Building & Joinery', 'eddie-preston-building-joinery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Edgar Joseph Furniture Limited', 'edgar-joseph-furniture-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('EGI & UKB Ltd', 'egi-ukb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Electrical Sales Ltd', 'electrical-sales-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Elegant Kitchens & Bedrooms Ltd', 'elegant-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Elite Kitchens & Bedrooms Ltd', 'elite-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Elite Woodwork Limited', 'elite-woodwork-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ella Austin Kitchens Ltd', 'ella-austin-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Elliott Brothers Ltd', 'elliott-brothers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Elliott Installations Ltd', 'elliott-installations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ellis Interiors', 'ellis-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ellis Interiors (Nottingham) Ltd', 'ellis-interiors-nottingham-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Elysee Kitchens & Interiors Ltd', 'elysee-kitchens-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Empire Builders (London) Ltd', 'empire-builders-london-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Enigma Joinery Limited', 'enigma-joinery-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Enterprise Direct (Swansea) Ltd', 'enterprise-direct-swansea-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ESC (TV) Ltd', 'esc-tv-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Espresso Design Ltd', 'espresso-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Essex Kitchen Studio', 'essex-kitchen-studio', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Esther & Viny Ltd', 'esther-viny-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Eternal Kitchen Ltd', 'eternal-kitchen-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Euronics', 'euronics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Everfine Installations Ltd', 'everfine-installations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Evie Willow Ltd', 'evie-willow-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('EWB Cotton Limited T/As', 'ewb-cotton-limited-tas', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Expert', 'expert', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('F A North (Carlton) Ltd', 'f-a-north-carlton-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('F C Bailey (UK) Ltd', 'f-c-bailey-uk-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('F J Fullick Ltd', 'f-j-fullick-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('F W & D Wain', 'f-w-d-wain', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fairbright Property Services Ltd', 'fairbright-property-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Falconwood Joinery', 'falconwood-joinery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Falstaff Home Design Limited', 'falstaff-home-design-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Family Kitchens Ltd', 'family-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fantasy Makeovers Ltd', 'fantasy-makeovers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Farnham Furnishers Ltd', 'farnham-furnishers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Farr & Harris Ltd', 'farr-harris-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Faulkner Hayes Services Ltd', 'faulkner-hayes-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Faulkners Louth Ltd', 'faulkners-louth-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('FE Electronics Ltd;', 'fe-electronics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Feldman Design Co.Ltd', 'feldman-design-coltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Femme Ferriers Ltd', 'femme-ferriers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fenwicks', 'fenwicks', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ferries Kitchens Ltd', 'ferries-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ffler Ltd', 'ffler-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fiks Trading Ltd', 'fiks-trading-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Finewood Interiors Ltd', 'finewood-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fiona Lewis', 'fiona-lewis', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Firenza Ltd', 'firenza-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('First Choice Domestic Appliances Lt', 'first-choice-domestic-appliances-lt', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('First Choice Kitchens', 'first-choice-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fitzgeralds Interiors', 'fitzgeralds-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fivestar Services (Fylde)', 'fivestar-services-fylde', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Flair Interiors Ltd', 'flair-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Flawless Kitchens Ltd', 'flawless-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Flawless UK Group Ltd', 'flawless-uk-group-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('FloCoe Interiors Ltd', 'flocoe-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('FMC Finbar Michael Cochrane', 'fmc-finbar-michael-cochrane', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fordbrook Interiors Ltd', 'fordbrook-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Forever Home Design Ltd', 'forever-home-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Formosa Kitchens Ltd', 'formosa-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Forth Kitchens & Bathrooms Ltd', 'forth-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Foulkes Electrical', 'foulkes-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Four Corners Properties Ltd', 'four-corners-properties-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Four Woods Ltd', 'four-woods-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Foxon & Foxon Ltd', 'foxon-foxon-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Francesca Quansah', 'francesca-quansah', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Frank Craig Limited', 'frank-craig-limited', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Frank Hadley Design Ltd', 'frank-hadley-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Fry Maintenance Ltd', 'fry-maintenance-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('FT Refrigeration', 'ft-refrigeration', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Funktional Kitchens Limited', 'funktional-kitchens-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Furniche Home Limited', 'furniche-home-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Furniture & Design Ltd', 'furniture-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('G Craggs Ltd', 'g-craggs-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('G D Evans Interiors Ltd', 'g-d-evans-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('G H Chambers & Son', 'g-h-chambers-son', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('G J & B B Bennett', 'g-j-b-b-bennett', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('G S Interiors', 'g-s-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('G.B.Farrar & Co. Ltd', 'gbfarrar-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Galeri Redi Cyf Ltd', 'galeri-redi-cyf-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gambron Ltd', 'gambron-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Garden House Collections Ltd', 'garden-house-collections-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gardiner Sons & Co Ltd', 'gardiner-sons-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Garland Electrical', 'garland-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Garrett Appliances Ltd', 'garrett-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Garry Smale', 'garry-smale', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gary Etheridge Domestic Apps Ltd', 'gary-etheridge-domestic-apps-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gary Shedden', 'gary-shedden', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Geo Donald (Warehouse Man) Ltd', 'geo-donald-warehouse-man-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Geoff Hill Ltd', 'geoff-hill-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Geoff Hodgson', 'geoff-hodgson', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('George & George KBB Ltd', 'george-george-kbb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('George Bence & Sons (Cheltenham)', 'george-bence-sons-cheltenham', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('George Robertson LTD', 'george-robertson-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('George Wallace Marshall', 'george-wallace-marshall', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gerry Bermingham', 'gerry-bermingham', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gift Direct Ltd', 'gift-direct-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gilbert And Vanstone', 'gilbert-and-vanstone', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Glendales Ltd', 'glendales-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Glens', 'glens', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('GLM Building Services Ltd', 'glm-building-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Globe & De Blasio Limited', 'globe-de-blasio-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gloucestershire Kitchen', 'gloucestershire-kitchen', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('GMS Plumbing & Heating Supplies', 'gms-plumbing-heating-supplies', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Golden Pines Ltd', 'golden-pines-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Goodwins Kitchens Bedrooms &', 'goodwins-kitchens-bedrooms-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gotch Of Selby Ltd', 'gotch-of-selby-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gough & Gamble Kitchens Ltd', 'gough-gamble-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gower Coast Kitchens', 'gower-coast-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grabex Windows Ltd', 'grabex-windows-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grafton Merchanting GB Ltd', 'grafton-merchanting-gb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Graham Mayes T/A G S M Joinery', 'graham-mayes-ta-g-s-m-joinery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grange Kitchen and Bedrooms Ltd', 'grange-kitchen-and-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Granite Options Ltd', 'granite-options-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Granite Style Ltd', 'granite-style-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grant & Stone', 'grant-stone', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grant & Stone Ltd', 'grant-stone-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grantham Home Interiors Ltd', 'grantham-home-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grappenhall Kitchen Company Ltd', 'grappenhall-kitchen-company-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grays Fitted Furniture Ltd', 'grays-fitted-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Greatlook Design Studio Ltd', 'greatlook-design-studio-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Greatwood Jenkins Ltd', 'greatwood-jenkins-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Green Sheen Construction Ltd', 'green-sheen-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Greenacre Property Limited', 'greenacre-property-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gregor Watson', 'gregor-watson', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Gregory John Kits Ltd', 'gregory-john-kits-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Grey''s Electrical Ltd', 'greys-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Guernsey Electricity', 'guernsey-electricity', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Guernsey Gas Ltd', 'guernsey-gas-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H & S Kitchen And Bedrooms', 'h-s-kitchen-and-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H B H Woolacotts Ltd', 'h-b-h-woolacotts-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H F Sheffield Ltd', 'h-f-sheffield-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H I S Design Ltd', 'h-i-s-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H J Knee Ltd', 'h-j-knee-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H Robertshaw And Sons Ltd', 'h-robertshaw-and-sons-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H Telecentre & Co', 'h-telecentre-co', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('H.A.C. Domestic Services Ltd', 'hac-domestic-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Haagensen Wardrobes & Kitchens Ltd', 'haagensen-wardrobes-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Haden Design (Kitchen & Bedrooms)', 'haden-design-kitchen-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hadlington Brothers Ltd', 'hadlington-brothers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hafele UK Limited', 'hafele-uk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hale & Co. (Drybrook) Ltd.', 'hale-co-drybrook-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hale & McCloy Design Ltd', 'hale-mccloy-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hale & Murray Ltd', 'hale-murray-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hall Green Kitchens & Bathrooms', 'hall-green-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hallmark Kitchen Designs Ltd', 'hallmark-kitchen-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Halls of Ibstock Ltd', 'halls-of-ibstock-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Handcraft Manufacturing Ltd', 'handcraft-manufacturing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Handcrafted By Woodworks Limited', 'handcrafted-by-woodworks-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Handmade Shaker Kitchens Ltd', 'handmade-shaker-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hanson Electrical Ltd', 'hanson-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harborough and Cornish Limited', 'harborough-and-cornish-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hardy''s of Kilkeel Ltd', 'hardys-of-kilkeel-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hardys', 'hardys', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harness & Harness', 'harness-harness', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harper Kitchens Ltd', 'harper-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harris Electrical Ltd', 'harris-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harrison James (Shropshire) Ltd', 'harrison-james-shropshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harrison Smith Interiors Ltd', 'harrison-smith-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harrods', 'harrods', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harry Garlick', 'harry-garlick', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harry Parry Ltd', 'harry-parry-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harthorpe Ltd', 'harthorpe-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harvest Interiors', 'harvest-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harvey Jones Ltd', 'harvey-jones-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harvey Norman', 'harvey-norman', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harvey Properties', 'harvey-properties', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Harvey Services', 'harvey-services', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Haygate Developments Ltd', 'haygate-developments-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('HBH Woolacotts Ltd', 'hbh-woolacotts-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('HDM Kitchens Ltd', 'hdm-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hearnebay Domestics', 'hearnebay-domestics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Heath Bathrooms Ltd', 'heath-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Heathcote Interiors Ltd', 'heathcote-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hebdons Ltd', 'hebdons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Helen Duggleby', 'helen-duggleby', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Helix Interiors Ltd', 'helix-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Helloproducts Limited', 'helloproducts-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Helmanis & Howell Ltd', 'helmanis-howell-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Herbert Todd & Son', 'herbert-todd-son', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Herts Bathroom Centre Ltd', 'herts-bathroom-centre-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hiddlestone & Son Ltd', 'hiddlestone-son-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('High Chase Interiors Ltd', 'high-chase-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Higham Furniture Ltd', 'higham-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Highleys Of Yorkshire Ltd', 'highleys-of-yorkshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hill Farm Joinery Ltd', 'hill-farm-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hitchings & Thomas Ltd', 'hitchings-thomas-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('HJ & D Webb & Sons Ltd', 'hj-d-webb-sons-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hockridge & Stacey Ltd', 'hockridge-stacey-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Holborough Interiors Limited', 'holborough-interiors-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Holmewood Interiors Ltd', 'holmewood-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Holz Design Ltd', 'holz-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Home & Kitchen Solutions', 'home-kitchen-solutions', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Home Appliances Ltd T/A DID Electrical', 'home-appliances-ltd-ta-did-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Home Design (Yorkshire) Ltd', 'home-design-yorkshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Home Design Yorkshire Ltd', 'home-design-yorkshire-ltd-1', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Home Kitchens and Bedrooms Limited', 'home-kitchens-and-bedrooms-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Home Works (Boston) Ltd', 'home-works-boston-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Homebase', 'homebase', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Homecare Heating Supplies Ltd', 'homecare-heating-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Homecraft LLP', 'homecraft-llp', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Homemaker (South) Ltd', 'homemaker-south-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Homestyle (wtc) Limited', 'homestyle-wtc-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Homestyle Design Ltd', 'homestyle-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Honest By Design Ltd', 'honest-by-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hooper Home Improvements Ltd', 'hooper-home-improvements-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Horizon Burnham Ltd', 'horizon-burnham-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Horizon Kitchens Ltd', 'horizon-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('House of Harper Design Limited', 'house-of-harper-design-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hove Bathroom Centre Ltd', 'hove-bathroom-centre-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Howards Electricals', 'howards-electricals', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Howarth Timber & Building Supplies', 'howarth-timber-building-supplies', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Howden Joinery Limited', 'howden-joinery-limited', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Howdens Joinery', 'howdens-joinery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('HSA Plumbing and Heating Limited', 'hsa-plumbing-and-heating-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('HTJ Ltd', 'htj-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hubble Constantine Ltd', 'hubble-constantine-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hughes TV And Audio Ltd', 'hughes-tv-and-audio-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hull Kitchen & Bedroom Studio Ltd', 'hull-kitchen-bedroom-studio-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hunt Bespoke Kitchens & Interiors L', 'hunt-bespoke-kitchens-interiors-l', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hurrells Electrical Ltd', 'hurrells-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Huws Gray Ltd', 'huws-gray-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hyatt Interiors Ltd', 'hyatt-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Hylands Ltd', 'hylands-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('I - Home Interiors Ltd', 'i-home-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('I Build (Wales) Limited', 'i-build-wales-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ian Price', 'ian-price', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ian Singh', 'ian-singh', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ian Witham', 'ian-witham', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ideal Kitchen & Bathrooms Ltd', 'ideal-kitchen-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ideas Studios', 'ideas-studios', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ikon Kitchens Ltd', 'ikon-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Illya Kitchens Ltd', 'illya-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Image Interiors Ltd', 'image-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Images Of Derbyshire Ltd', 'images-of-derbyshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Imagine Transformations Ltd', 'imagine-transformations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Imbue Furniture', 'imbue-furniture', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Impress Kitchens Ltd', 'impress-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('In Design Showroom London Ltd', 'in-design-showroom-london-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('In House kitchen Designs Limited', 'in-house-kitchen-designs-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('In-Home Installations Ltd', 'in-home-installations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('IN2 Group Limited', 'in2-group-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Independent Furniture & Interiors', 'independent-furniture-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Infinite Bespoke Design Ltd', 'infinite-bespoke-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Infinity Kitchen Designs Ltd', 'infinity-kitchen-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Innovative Bathrooms Ltd', 'innovative-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inova Kitchens Ltd', 'inova-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inside Story', 'inside-story', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inside Transformations Ltd', 'inside-transformations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inspire Kitchens Ltd', 'inspire-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inspired Installations Southern Ltd', 'inspired-installations-southern-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inspired Interiors Kent Ltd', 'inspired-interiors-kent-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inspired Living Renovations Ltd', 'inspired-living-renovations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Instil Design Ltd', 'instil-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Inter Ceramica Ltd', 'inter-ceramica-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Interface Contract', 'interface-contract', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Interior Choice Ltd', 'interior-choice-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Interior Design Matters', 'interior-design-matters', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Interior Mode Ltd', 'interior-mode-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Interiors By Penelope Jane Ltd', 'interiors-by-penelope-jane-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Interiors of Harrogate', 'interiors-of-harrogate', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Interphase Interiors Ltd', 'interphase-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Intrafit Limited', 'intrafit-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ioulia Interior Ltd', 'ioulia-interior-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ironbridge Interiors Ltd', 'ironbridge-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J & E Designs Ltd', 'j-e-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J & F Installations', 'j-f-installations', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J & P Electric', 'j-p-electric', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J & W Campbell Ltd', 'j-w-campbell-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J A Ridley & Sons Ltd', 'j-a-ridley-sons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J B Postle & Son Ltd', 'j-b-postle-son-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J D Bathrooms & Kitchens Ltd', 'j-d-bathrooms-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J D R Thomas', 'j-d-r-thomas', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J H B Kitchens Ltd', 'j-h-b-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J H Carpentry', 'j-h-carpentry', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J Jenkins Electrical Ltd', 'j-jenkins-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J M Builders Merchants Ltd', 'j-m-builders-merchants-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J N K Kitchen Studios Ltd', 'j-n-k-kitchen-studios-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J S Designs & Interiors Ltd', 'j-s-designs-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J Sheppard & Son Ltd', 'j-sheppard-son-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J Swinscoe Ltd', 'j-swinscoe-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J T Dove', 'j-t-dove', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J T Ward Kitchens & Bedrooms Ltd', 'j-t-ward-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J W Brownley Ltd', 'j-w-brownley-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J. Arthur Electrical LTD', 'j-arthur-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J.J.Quinn Electrical', 'jjquinn-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J.N.C. Interior Design Ltd', 'jnc-interior-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('J.P.L Designs Ltd', 'jpl-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jackson Buildbase', 'jackson-buildbase', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jacksons of Petersfield', 'jacksons-of-petersfield', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JAF Joinery Ltd', 'jaf-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jamek (Scotland) Ltd', 'jamek-scotland-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('James Booth', 'james-booth', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('James Cornwell Interiors Ltd', 'james-cornwell-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('James Neal', 'james-neal', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('James Neal T/A', 'james-neal-ta', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('James Oliver Kitchens Ltd', 'james-oliver-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('James Riches Ltd', 'james-riches-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('James Stewart & Son Ltd', 'james-stewart-son-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jane Cheel Furniture Limited', 'jane-cheel-furniture-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Janus Interiors Bingley Ltd', 'janus-interiors-bingley-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Japonica Interior Ltd', 'japonica-interior-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jason Dacey', 'jason-dacey', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jason Witherspoon', 'jason-witherspoon', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jay Furniture Fittings Limited', 'jay-furniture-fittings-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jaystone Construction Ltd', 'jaystone-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JC Campbell Electrics', 'jc-campbell-electrics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JCT Property Services Ltd', 'jct-property-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jeans Electrical', 'jeans-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jeff Allen Designer Kitchens Ltd', 'jeff-allen-designer-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jeff Hewitson', 'jeff-hewitson', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jeffrey Knight', 'jeffrey-knight', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jeremy Snell', 'jeremy-snell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jeremy Wilson Kitchen Company', 'jeremy-wilson-kitchen-company', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jerry''s Electrical Ltd', 'jerrys-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jersey Electrical PLC', 'jersey-electrical-plc', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jewson Ltd', 'jewson-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jigsaw Interiors Ltd', 'jigsaw-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JMG Homestores Ltd', 'jmg-homestores-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JNB Services Ltd', 'jnb-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jo-Anne Shaw', 'jo-anne-shaw', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Joanne Skillicorn', 'joanne-skillicorn', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Joaw Ltd', 'joaw-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Calvert (Elec) Ltd', 'john-calvert-elec-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Cocks', 'john-cocks', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Eccleston', 'john-eccleston', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Gillman & Sons (Elec.) Ltd', 'john-gillman-sons-elec-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Graham', 'john-graham', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Hubbard Antiques & Restoration', 'john-hubbard-antiques-restoration', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Kerry & Sons Ltd', 'john-kerry-sons-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Lewis', 'john-lewis', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Place', 'john-place', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Revell', 'john-revell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Scarratt', 'john-scarratt', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Sheppard', 'john-sheppard', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('John Turner Kitchens & Bathrooms', 'john-turner-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Johns Electrical Ltd', 'johns-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Johnsons Buildbase', 'johnsons-buildbase', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Johnstons Domestic Appliances', 'johnstons-domestic-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jonathan Clifford', 'jonathan-clifford', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jonathan Taylor', 'jonathan-taylor', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jonpye Bathrooms + Kitchens Ltd', 'jonpye-bathrooms-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jordan Ventures Ltd', 'jordan-ventures-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Joseph Whittaker', 'joseph-whittaker', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Joseph Williams Ltd', 'joseph-williams-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Josling TV & Electrical', 'josling-tv-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JPFG Solutions Ltd', 'jpfg-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JT Atkinson & Sons Ltd', 'jt-atkinson-sons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('JTS Kitchens Ltd', 'jts-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Juan Almond', 'juan-almond', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Julian Narborough', 'julian-narborough', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Just Beautiful Kitchens', 'just-beautiful-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Jyotsna Electrical Ltd', 'jyotsna-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K & B Collection Ltd', 'k-b-collection-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K & I Kitchen Installations Ltd', 'k-i-kitchen-installations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K B Installations Ltd', 'k-b-installations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K F Kitchens Bedrooms And Bathrooms', 'k-f-kitchens-bedrooms-and-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K Hunter Retail Ltd', 'k-hunter-retail-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K.B.B.(East Anglia ) Ltd', 'kbbeast-anglia-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K2 Kitchens & Interiors Ltd', 'k2-kitchens-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('K4 Kitchens Ltd', 'k4-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kaaj Bedrooms & Kitchens Ltd', 'kaaj-bedrooms-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KAD Design Projects Ltd', 'kad-design-projects-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kandu Interiors Limited', 'kandu-interiors-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Katie Bennett Ltd', 'katie-bennett-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kavam Ltd', 'kavam-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KBK Studio Limited', 'kbk-studio-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KCA Kitchens Ltd', 'kca-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KD Kitchen Design Services Ltd', 'kd-kitchen-design-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KDH Ltd', 'kdh-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KDS Appliances Ltd', 'kds-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KDS Kitchens and Bedrooms Ltd', 'kds-kitchens-and-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kearneys', 'kearneys', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Keith Prescott', 'keith-prescott', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Keith Smith (Anstey) Ltd', 'keith-smith-anstey-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kelly Marie Kitchen Interiors', 'kelly-marie-kitchen-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kenberne Holdings Ltd', 'kenberne-holdings-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kensington Domestic Apps Ltd', 'kensington-domestic-apps-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kensington Studio (Coventry) Limite', 'kensington-studio-coventry-limite', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kent Domestic Services Ltd', 'kent-domestic-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kenton Jones Ltd', 'kenton-jones-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kevin Farish Ltd', 'kevin-farish-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kevin Rust Kitchen Design Centre', 'kevin-rust-kitchen-design-centre', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Keymex Ltd', 'keymex-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kings Norton Kitchens Ltd', 'kings-norton-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kingsbury Electronics Ltd', 'kingsbury-electronics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kingsey Furniture & Interiors Ltd', 'kingsey-furniture-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kingston By Design Ltd', 'kingston-by-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kingston Cabinets Ltd', 'kingston-cabinets-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kingswood Kitchens', 'kingswood-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kiplings Kitchens Ltd', 'kiplings-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kirti Durelle Architects', 'kirti-durelle-architects', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitbed Warwick Ltd', 'kitbed-warwick-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen & Bed Workshop Ltd', 'kitchen-bed-workshop-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen & Bedroom Creations', 'kitchen-bedroom-creations', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen and Bedroom Revivals Ltd', 'kitchen-and-bedroom-revivals-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Craft (UK) Ltd', 'kitchen-craft-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Culture (Cambridgeshire)', 'kitchen-culture-cambridgeshire', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Culture (Kent) Ltd', 'kitchen-culture-kent-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Design Concepts Ltd', 'kitchen-design-concepts-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Design Solutions Ltd', 'kitchen-design-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Design Studio (Bridgend)', 'kitchen-design-studio-bridgend', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Discount (Grimsby) Ltd', 'kitchen-discount-grimsby-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Doors & More Ltd', 'kitchen-doors-more-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Economy Ltd', 'kitchen-economy-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Effects Ltd', 'kitchen-effects-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Elegance Ltd', 'kitchen-elegance-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen face lifts Ltd', 'kitchen-face-lifts-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Flair Ltd', 'kitchen-flair-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen George (Dover) Ltd', 'kitchen-george-dover-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen House Cambridge Ltd', 'kitchen-house-cambridge-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Kind Ltd', 'kitchen-kind-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Love Ltd', 'kitchen-love-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Makeover Expert Ltd', 'kitchen-makeover-expert-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Options', 'kitchen-options', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Project Solutions Ltd', 'kitchen-project-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Renovate Ltd', 'kitchen-renovate-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Restorations North East', 'kitchen-restorations-north-east', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Restore Ltd', 'kitchen-restore-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Restorers Limited', 'kitchen-restorers-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Restyle Ltd', 'kitchen-restyle-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Revolutions Ltd', 'kitchen-revolutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Specialists (all UK independent Kitchen retail stores)', 'kitchen-specialists-all-uk-independent-kitchen-retail-stores', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Style of Dorchester Ltd', 'kitchen-style-of-dorchester-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchen Warehouse UK Limited', 'kitchen-warehouse-uk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchenco Ltd', 'kitchenco-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchener & Thomas Ltd', 'kitchener-thomas-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens & Appliances Ltd', 'kitchens-appliances-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens & Bathrooms by C and C', 'kitchens-bathrooms-by-c-and-c', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens & Bathrooms MK Limited', 'kitchens-bathrooms-mk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens & Worktops Ltd', 'kitchens-worktops-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens By Design (Hull) Ltd', 'kitchens-by-design-hull-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens by Kemp Ltd', 'kitchens-by-kemp-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens by Leanne Ltd', 'kitchens-by-leanne-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens Continental Ltd', 'kitchens-continental-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens Northeast Ltd', 'kitchens-northeast-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens of Cheshire Ltd', 'kitchens-of-cheshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens To Love Ltd', 'kitchens-to-love-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kitchens With Elegance Ltd', 'kitchens-with-elegance-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Klaus Ecohomes Ltd', 'klaus-ecohomes-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Klynstone Ltd', 'klynstone-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KNB Creations Ltd', 'knb-creations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Knebworth Kitchens', 'knebworth-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Knight''s Interiors (KBB) Ltd', 'knights-interiors-kbb-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Knights Of Oakham Ltd', 'knights-of-oakham-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Koncept Rooms Ltd', 'koncept-rooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kreative Kitchen Designs', 'kreative-kitchen-designs', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KSS KITCHENS SOUTH WEST LTD', 'kss-kitchens-south-west-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('KTE Kitchens ltd', 'kte-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kuchehaus Ltd', 'kuchehaus-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Kutchenhaus Chichester Ltd', 'kutchenhaus-chichester-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('L & C Transformations Ltd', 'l-c-transformations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('L & P Kitchens', 'l-p-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('L Interieur (Wolverhampton) Ltd', 'l-interieur-wolverhampton-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('L J Home Solutions Ltd', 'l-j-home-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('L L Kitchens Ltd', 'l-l-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('L W Foxall', 'l-w-foxall', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('L''unico Bespoke Design Ltd', 'lunico-bespoke-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('La Vita Showroom Limited', 'la-vita-showroom-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lacuna Design', 'lacuna-design', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lakeland Fells Furniture Ltd', 'lakeland-fells-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lancashire Developments Ltd', 'lancashire-developments-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lancaster Contracting Ltd', 'lancaster-contracting-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lane & Wenden Ltd', 'lane-wenden-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lark Valley Woodcraft Ltd', 'lark-valley-woodcraft-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lazerian', 'lazerian', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Leaf Kitchens and Bedrooms Ltd', 'leaf-kitchens-and-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lee Carr Design Ltd', 'lee-carr-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lee Crossley', 'lee-crossley', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lee Edwards', 'lee-edwards', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lee Powell', 'lee-powell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Leekes (Llantrisant)', 'leekes-llantrisant', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Leger Interiors Ltd', 'leger-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Les Sheppard D.I.Y Limited', 'les-sheppard-diy-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lethenty Cabinetmakers Ltd', 'lethenty-cabinetmakers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lewis Charles Oxted Ltd', 'lewis-charles-oxted-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lewis Price', 'lewis-price', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lewis''s', 'lewiss', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Liberty Home Design Ltd', 'liberty-home-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('LifeisArt Visuals', 'lifeisart-visuals', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lifetime Installations Limited', 'lifetime-installations-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lighting Electrical Ltd', 'lighting-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lima Kitchens Ltd', 'lima-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lime Designs Kitchens & Joinery', 'lime-designs-kitchens-joinery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Limefrog Ltd', 'limefrog-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Limitless Interiorz Ltd', 'limitless-interiorz-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lindsey Relay Co Ltd', 'lindsey-relay-co-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Linkup Kitchens & Bedrooms Ltd', 'linkup-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Listed Window Refurbishment Limited', 'listed-window-refurbishment-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Littlewoods', 'littlewoods', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Liverpool North Ltd', 'liverpool-north-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Living In Vogue Ltd', 'living-in-vogue-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Livn Manufacturing Ltd', 'livn-manufacturing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('LLM Harrogate Ltd', 'llm-harrogate-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lol Summers Joinery Limited', 'lol-summers-joinery-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('London Design Haus Ltd', 'london-design-haus-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('London Domestic App', 'london-domestic-app', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('London Homestead Property', 'london-homestead-property', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('London Plane Projects Ltd', 'london-plane-projects-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Long Eaton Appliance Co. Ltd', 'long-eaton-appliance-co-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lorenzo Iaquaniello', 'lorenzo-iaquaniello', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Love & Robinson Kitchens &', 'love-robinson-kitchens-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lovell Kitchens Ltd', 'lovell-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lund & Law Interiors Ltd', 'lund-law-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lunney''s Elec Discount Store', 'lunneys-elec-discount-store', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lunts Domestic Appliances', 'lunts-domestic-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lusso Living Ltd', 'lusso-living-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lutterworth Sound & Vision Ltd', 'lutterworth-sound-vision-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lux Group Holdings Ltd', 'lux-group-holdings-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Luxmoore & Co Ltd', 'luxmoore-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Luxor Kitchens Ltd', 'luxor-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lynch Home Improvements Ltd', 'lynch-home-improvements-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Lynne Radio Ltd', 'lynne-radio-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M & D Components Limited', 'm-d-components-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M A Design (Chester) Ltd', 'm-a-design-chester-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M B Interiors (Shropshire) Ltd', 'm-b-interiors-shropshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M B S Bathrooms & Kitchens', 'm-b-s-bathrooms-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M B Woodcraft', 'm-b-woodcraft', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M D Home Improvements Hereford', 'm-d-home-improvements-hereford', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M J & E M Services Ltd', 'm-j-e-m-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M J Kitchens Ltd', 'm-j-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M J Pipe Ltd', 'm-j-pipe-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M M Ross The Design House Ltd', 'm-m-ross-the-design-house-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M P Livingstone', 'm-p-livingstone', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M P Moran & Sons Ltd', 'm-p-moran-sons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('M.T. Lawrence', 'mt-lawrence', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MAF Bedrooms & Kitchens Ltd', 'maf-bedrooms-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Magnet Kitchens', 'magnet-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Magnet Limited', 'magnet-limited', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Makadams (Baughurst) Ltd', 'makadams-baughurst-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Malcolm Allan Housebuilders Ltd', 'malcolm-allan-housebuilders-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Malibu Bathrooms & Kitchens Ltd', 'malibu-bathrooms-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Manchester Board & Fittings Ltd', 'manchester-board-fittings-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Manning & Woods Ltd', 'manning-woods-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MAP Electrical Ltd', 'map-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Maple & Stone Ltd', 'maple-stone-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Maple Kitchens & Bathrooms Ltd', 'maple-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Marabese Ceramics Ltd', 'marabese-ceramics-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Maraca Ltd', 'maraca-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('March Electrical Supplies Ltd', 'march-electrical-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mark Bridges Carvers & Gilders Ltd', 'mark-bridges-carvers-gilders-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mark Hudson', 'mark-hudson', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mark Lunn', 'mark-lunn', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mark Mills', 'mark-mills', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mark Plant Ltd', 'mark-plant-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Marked Home Improvements Ltd', 'marked-home-improvements-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Marks Electrical Ltd', 'marks-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Marown TV Ltd', 'marown-tv-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Martin Dawes', 'martin-dawes', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Martins of Hawkhurst', 'martins-of-hawkhurst', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mary O''Shea', 'mary-oshea', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MAS Kuchen Ltd', 'mas-kuchen-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mason Carpentry Innovations', 'mason-carpentry-innovations', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mason Projects Limited', 'mason-projects-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Masterpiece Interiors Ltd', 'masterpiece-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mayoral Bathrooms Ltd', 'mayoral-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MBF Property Services Ltd', 'mbf-property-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MBS Bathrooms & Kitchens UK Ltd', 'mbs-bathrooms-kitchens-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MBS Interiors (SOT) Ltd', 'mbs-interiors-sot-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('McAdam Design Innovations Ltd', 'mcadam-design-innovations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('McCoubreys Elec Superstore', 'mccoubreys-elec-superstore', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('McFarlane Kitchens & Joinery Ltd', 'mcfarlane-kitchens-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('McLaughlins Kitchens Ltd', 'mclaughlins-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('McMichael Bros.', 'mcmichael-bros', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MCS Design & Build Ltd', 'mcs-design-build-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mechanical Services Inst ltd', 'mechanical-services-inst-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mehdi Samyani', 'mehdi-samyani', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mehul Gohil', 'mehul-gohil', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Meirion Evans', 'meirion-evans', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Melbros Ltd', 'melbros-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Melton Kitchen, Bedroom &', 'melton-kitchen-bedroom-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MG Domestics', 'mg-domestics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MGM Timber (Scotland) Limited', 'mgm-timber-scotland-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Michael Bere', 'michael-bere', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Michael Carlin', 'michael-carlin', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Michael Dale', 'michael-dale', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Michael Maiden T/a', 'michael-maiden-ta', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Michael R Peters', 'michael-r-peters', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mick Spooner', 'mick-spooner', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Microwave Service Centre', 'microwave-service-centre', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mike Gardiner', 'mike-gardiner', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mike Simpson', 'mike-simpson', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Milo7 Limited', 'milo7-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Milo7 Ltd', 'milo7-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Milton Kitchens & Renovations', 'milton-kitchens-renovations', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Minerva of Hereford Limited', 'minerva-of-hereford-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Minhas Trading Company Ltd', 'minhas-trading-company-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mitre Pro Limited', 'mitre-pro-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MJS Interiors (Midlands) Ltd', 'mjs-interiors-midlands-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MKM Building Supplies Ltd', 'mkm-building-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('ML Lake (Electrical Servs) Ltd', 'ml-lake-electrical-servs-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MLRSG Ltd', 'mlrsg-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mo''s Designs Ltd', 'mos-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mobalpa UK Ltd', 'mobalpa-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mobile TV Serv', 'mobile-tv-serv', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Modern Vision Furniture Ltd', 'modern-vision-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Molesey Refrigeration', 'molesey-refrigeration', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Montague Fox Limited', 'montague-fox-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Montague Fox Ltd', 'montague-fox-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Montrose Home Appliance Company Ltd', 'montrose-home-appliance-company-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Moore Electrics', 'moore-electrics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Moores Appliances Ltd', 'moores-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mor Interiors Ltd', 'mor-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('More-Build Ltd', 'more-build-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Moston Lane Appliances', 'moston-lane-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mountain Property Services Ltd', 'mountain-property-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mowlem & Co London Ltd', 'mowlem-co-london-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mr Damiam Lee', 'mr-damiam-lee', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mr Dipak Patel', 'mr-dipak-patel', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mr Nigel Malacrida', 'mr-nigel-malacrida', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mr P.& Mrs J.Buckland', 'mr-p-mrs-jbuckland', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('MSM Home Interiors Ltd', 'msm-home-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mucklow Hill Interiors Ltd', 'mucklow-hill-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Mullen Domestic', 'mullen-domestic', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('My Dream Kitchen (Pinner) Limited', 'my-dream-kitchen-pinner-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Myers Building Supplies', 'myers-building-supplies', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('N G Bathrooms, Kitchens & Bedrooms', 'n-g-bathrooms-kitchens-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('N P Renovations Ltd', 'n-p-renovations-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('N Tideswell Maintenance', 'n-tideswell-maintenance', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nailsea Electrical (NE Appliances)', 'nailsea-electrical-ne-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Naked Manufacturing Group Holidings', 'naked-manufacturing-group-holidings', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nathaniel Oliver& Associates Ltd', 'nathaniel-oliver-associates-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Neil Porter', 'neil-porter', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Neil Sumner', 'neil-sumner', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Neo Kit Ltd', 'neo-kit-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Neptune (Europe) Ltd', 'neptune-europe-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nest Design & Build Ltd', 'nest-design-build-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('New Direction (Home Improvements', 'new-direction-home-improvements', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('New Life Kitchens Ltd', 'new-life-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('New Milton Electrical Centre Ltd', 'new-milton-electrical-centre-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('New Vision Industries Ltd', 'new-vision-industries-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Newage Electrical Ltd', 'newage-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Newbury Radio Co Ltd', 'newbury-radio-co-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Newcheshire Cabinets Limited', 'newcheshire-cabinets-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Newlook DIY Ltd', 'newlook-diy-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('NGUK Ltd', 'nguk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nic Dumine', 'nic-dumine', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nick Blake T/a The Little Common', 'nick-blake-ta-the-little-common', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nick Ellis', 'nick-ellis', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nick Williams Bespoke Interior', 'nick-williams-bespoke-interior', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nigel Jenkins', 'nigel-jenkins', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nisbets PLC', 'nisbets-plc', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('NJS Joinery Limited', 'njs-joinery-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('NKB Kitchens & Bedrooms Ltd', 'nkb-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Noble Kitchens, Bathrrooms and', 'noble-kitchens-bathrrooms-and', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Noel Apperley', 'noel-apperley', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Noel Grimley Electrics Ltd', 'noel-grimley-electrics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Norden Heating & Plumbing', 'norden-heating-plumbing', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nordhus Furniture Limited', 'nordhus-furniture-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Norfolk Oak Ltd', 'norfolk-oak-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Norman Hargreaves (Berwick) Ltd', 'norman-hargreaves-berwick-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('North Wales Dom App Centre', 'north-wales-dom-app-centre', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Northampton Kitchen & Bathroom', 'northampton-kitchen-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Northern Domestics Ltd', 'northern-domestics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Northxsouth Ltd', 'northxsouth-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Novax Limited', 'novax-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Nulux Group Ltd', 'nulux-group-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Number 10 Interiors Ltd', 'number-10-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Number Eighty One Ltd', 'number-eighty-one-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('O B G Developments', 'o-b-g-developments', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oakwood Student Accommodation Limit', 'oakwood-student-accommodation-limit', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oatenvale Ltd', 'oatenvale-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ocean Kitchens Ltd', 'ocean-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Odell Electrics', 'odell-electrics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oldham Premier Plumbing Supplies', 'oldham-premier-plumbing-supplies', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oldrid And Co Ltd', 'oldrid-and-co-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oliver James Furniture Ltd', 'oliver-james-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Olivia Mead', 'olivia-mead', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ollys Kitchens Ltd', 'ollys-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Omar Park Homes Limited', 'omar-park-homes-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Online Kitchen Store Ltd', 'online-kitchen-store-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('OPM Furniture Limited', 'opm-furniture-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Optiplan Kitchens Ltd', 'optiplan-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Orchard Crest Developments Limited', 'orchard-crest-developments-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Orkney Television Enterprise', 'orkney-television-enterprise', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oswestry Electrical & Plumbing', 'oswestry-electrical-plumbing', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oswestry Kitchens & Bathrooms', 'oswestry-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Owen''s TV & Domestic Appliances', 'owens-tv-domestic-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Oxted Kitchens Ltd', 'oxted-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('P A Haw Ltd', 'p-a-haw-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('P.H.I', 'phi', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('P&S Domestics Ltd', 'ps-domestics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Packhorse Steel Ltd', 'packhorse-steel-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Packwise Motivity Sales Ltd', 'packwise-motivity-sales-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Palmers Kitchens Bedrooms &', 'palmers-kitchens-bedrooms-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Panacea Kitchen Interiors Ltd', 'panacea-kitchen-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Panorama Furnishing Mart Ltd', 'panorama-furnishing-mart-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paramount Engineering Ltd', 'paramount-engineering-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Parkerrose Interiors Ltd', 'parkerrose-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Parlour Farm Kitchens Ltd', 'parlour-farm-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Patrick O''Brien', 'patrick-obrien', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Patrick Rowan', 'patrick-rowan', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Patstan Ltd', 'patstan-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Bishop', 'paul-bishop', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Brazier Kits & Beds Ltd', 'paul-brazier-kits-beds-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Brown Interim & Consulting', 'paul-brown-interim-consulting', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Golf Audio Visual', 'paul-golf-audio-visual', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Hall', 'paul-hall', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Mansell', 'paul-mansell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Mark Kitchens Ltd', 'paul-mark-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Morgan Furniture Ltd', 'paul-morgan-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Stevenson', 'paul-stevenson', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paul Waring T/a Ultimate Interiors', 'paul-waring-ta-ultimate-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Paula Mellors', 'paula-mellors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pavillion Ltd', 'pavillion-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('PB Home Solutions Ltd', 'pb-home-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pelican Bay Ltd', 'pelican-bay-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pembrokeshire Kitchens Ltd', 'pembrokeshire-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pendleton Bathroom Design Ltd', 'pendleton-bathroom-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pennywise Appliances Ltd', 'pennywise-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pentagon (Jersey) Wholesale Limited', 'pentagon-jersey-wholesale-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pentland Kitchens Ltd', 'pentland-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Percy Lord & Son Ltd', 'percy-lord-son-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peregrine Knight', 'peregrine-knight', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Perfect Fit Solutions Ltd', 'perfect-fit-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Perfect For the Home Ltd', 'perfect-for-the-home-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Perfect Holmes Kitchens Ltd', 'perfect-holmes-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Perth Kitchen Centre Limited', 'perth-kitchen-centre-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peter Gerald Kitchens', 'peter-gerald-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peter Malowany', 'peter-malowany', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peter Pennington (Worksop) Ltd', 'peter-pennington-worksop-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peter Rhodes', 'peter-rhodes', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peter Sloan', 'peter-sloan', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peter Thompson Of York Ltd', 'peter-thompson-of-york-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Peter Tyson', 'peter-tyson', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Philip Dell', 'philip-dell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Philip Goodridge', 'philip-goodridge', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Phipps & Son Ltd', 'phipps-son-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Phoenix Kitchens Ltd', 'phoenix-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pickups Of Stafford Ltd', 'pickups-of-stafford-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pine Marten Ltd', 'pine-marten-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pink Zebra Copywriting', 'pink-zebra-copywriting', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('PJW Electrics Ltd', 'pjw-electrics-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Planitt Ltd', 'planitt-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Plumb Interiors Ltd', 'plumb-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Plumbco Midlands Ltd', 'plumbco-midlands-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Plymstock Kitchens & Bathrooms', 'plymstock-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pontrilas Merchants', 'pontrilas-merchants', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pooles Domestics', 'pooles-domestics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Portfolio Kitchens & Bathrooms', 'portfolio-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Power City', 'power-city', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Power EC (PowerDirect)', 'power-ec-powerdirect', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Powerhouse', 'powerhouse', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('PPC3 Ltd', 'ppc3-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('PRC', 'prc', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('PRC HiFi & Video Ltd', 'prc-hifi-video-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Precision Wood Services', 'precision-wood-services', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Premier Appliances', 'premier-appliances', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Premier Kitchens Ltd', 'premier-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Premier Kitchenworks Ltd', 'premier-kitchenworks-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Premiere Klasse Ltd', 'premiere-klasse-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Prescott & Joule Kitchens Ltd', 'prescott-joule-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Prestige Kitchens & Bedrooms', 'prestige-kitchens-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Prestige Worktops Ltd', 'prestige-worktops-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Prime Wholesale UK Ltd', 'prime-wholesale-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Primrose Interiors (UK) Ltd', 'primrose-interiors-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Priory Kitchens Ltd', 'priory-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Profound Kitchens and Bedrooms', 'profound-kitchens-and-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Project Room Ltd', 'project-room-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Property Projects (SW) Ltd', 'property-projects-sw-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Pure Home Solutions Ltd', 'pure-home-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Purely Kitchens & Appliances', 'purely-kitchens-appliances', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Purewell', 'purewell', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Q.D.A.', 'qda', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('QI Refurbs and Contracts Ltd', 'qi-refurbs-and-contracts-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Quantas Kitchens & Bathrooms Ltd', 'quantas-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Queensbury Kitchens Ltd', 'queensbury-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Quinns Group', 'quinns-group', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('R & S Design Bathroom Specialist', 'r-s-design-bathroom-specialist', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('R C Snelling Ltd', 'r-c-snelling-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('R G B Hifi & Video Ltd', 'r-g-b-hifi-video-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('R L Brooks Ltd', 'r-l-brooks-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('R M R Joinery Services Ltd', 'r-m-r-joinery-services-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('R.D. & J.R. Greenfield', 'rd-jr-greenfield', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Radio Music Store LTD', 'radio-music-store-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Raff Electrical', 'raff-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Raison Home UK Ltd', 'raison-home-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ralph Devare', 'ralph-devare', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ramunas Demenis', 'ramunas-demenis', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Range Interiors (NW) Ltd', 'range-interiors-nw-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ranton Trading Ltd', 'ranton-trading-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Raphael Property Investment Co', 'raphael-property-investment-co', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ratford Bridge Kitchens &', 'ratford-bridge-kitchens-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ray Khan', 'ray-khan', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RDKI Ltd', 'rdki-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RDO Reigate', 'rdo-reigate', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Re-Nu Kitchens Ltd', 're-nu-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Reckon Interiors Ltd', 'reckon-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Recolonize Ltd', 'recolonize-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Red Dot Kitchens and Bathrooms', 'red-dot-kitchens-and-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Redbridge Domestic App', 'redbridge-domestic-app', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Redline Interiors', 'redline-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Redscar Kitchens (Preston) Ltd', 'redscar-kitchens-preston-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Reface Scotland Limited', 'reface-scotland-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Refrigeration North West', 'refrigeration-north-west', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Regal Interiors (UK) Ltd', 'regal-interiors-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Reilly''s of Enniskillen Ltd', 'reillys-of-enniskillen-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Reliant TV', 'reliant-tv', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Renov8 Kitchens Ltd', 'renov8-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Retail Furniture Limited', 'retail-furniture-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Retallack Kitchens', 'retallack-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Retreat Homes & Lodges Ltd', 'retreat-homes-lodges-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rey and Sons Ltd', 'rey-and-sons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RGB Ilford', 'rgb-ilford', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RGB Loughton', 'rgb-loughton', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Richard Brown', 'richard-brown', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Richard Davies', 'richard-davies', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Richard Ford', 'richard-ford', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Richard Still', 'richard-still', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Right Choice Kitchens Ltd', 'right-choice-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Riley James Ltd', 'riley-james-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rileys TV Services Ltd', 'rileys-tv-services-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rivenell 4 Ltd', 'rivenell-4-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Riverside Kitchens (Elgin) Ltd', 'riverside-kitchens-elgin-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RJS Builders Ltd', 'rjs-builders-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RL Brooks', 'rl-brooks', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RNS Construction Ltd', 'rns-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert Banbury', 'robert-banbury', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert Chapman Ltd', 'robert-chapman-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert Creasey', 'robert-creasey', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert G Gordon', 'robert-g-gordon', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert Hale', 'robert-hale', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert Holt Kitchens Ltd', 'robert-holt-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert James Interiors Ltd', 'robert-james-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert Kemp', 'robert-kemp', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robert Timmons Furniture Ltd', 'robert-timmons-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Roberts Radio', 'roberts-radio', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robertson Smith Ltd', 'robertson-smith-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Robson and Cowan', 'robson-and-cowan', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rocksolid Granit (Europe) Ltd', 'rocksolid-granit-europe-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rockwells SWS Ltd', 'rockwells-sws-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rod George', 'rod-george', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rodgers Fraserburgh Ltd', 'rodgers-fraserburgh-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Roger Hawkins', 'roger-hawkins', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rohan Woodworking Ltd', 'rohan-woodworking-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Roma Interiors Limited', 'roma-interiors-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Romerils', 'romerils', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ron + Liz Goult', 'ron-liz-goult', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ronald Beresford', 'ronald-beresford', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ronald Legacy Ltd', 'ronald-legacy-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ronalds Legacy Ltd', 'ronalds-legacy-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rose & Co Development Ltd', 'rose-co-development-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rosewood Home Improvements', 'rosewood-home-improvements', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Round Oak Interiors Limited', 'round-oak-interiors-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Roy Tucker', 'roy-tucker', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Royale Cuisines Ltd', 'royale-cuisines-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Royston Kitchens Limited', 'royston-kitchens-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RSM Domestic Appliances Ltd', 'rsm-domestic-appliances-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rubix Kitchens', 'rubix-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Russell J Milligan Ltd', 'russell-j-milligan-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('RWR Plus Ltd', 'rwr-plus-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ryburn Valley Furniture Ltd', 'ryburn-valley-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Rybuy Ltd', 'rybuy-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S & A Fitted Furniture Ltd', 's-a-fitted-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S & B Trade Services Ltd', 's-b-trade-services-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S & D Ireland Ltd', 's-d-ireland-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S & G Interiors Ltd', 's-g-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S B Kitchens & Bedrooms Ltd', 's-b-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S B Neal Ltd', 's-b-neal-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S K Colourhill Ltd', 's-k-colourhill-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S K Interior Solutions Limited', 's-k-interior-solutions-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S L Fittings Ltd', 's-l-fittings-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S P Chambers Ltd', 's-p-chambers-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S S Ubhi, S S Ubhi, Baltit Kaur Ubh', 's-s-ubhi-s-s-ubhi-baltit-kaur-ubh', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S W Watson And Son Ltd', 's-w-watson-and-son-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S.K.B Shepherds Kits & Beds Ltd', 'skb-shepherds-kits-beds-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S.M.S Kitchens Ltd', 'sms-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S.P.F.S Kitchens & Bedrooms Ltd', 'spfs-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('S&R Interiors Limited', 'sr-interiors-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Saarinen Kitchens Ltd', 'saarinen-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Salcey Pine Ltd', 'salcey-pine-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Samsung IE', 'samsung-ie', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Samsung UK', 'samsung-uk', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sandra Robinson Kitchens &', 'sandra-robinson-kitchens-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sandygate Enterprises Limited', 'sandygate-enterprises-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Savvas Marneros', 'savvas-marneros', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Saxon Designs Ltd', 'saxon-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Scawton Sawmill Limited', 'scawton-sawmill-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Scissor Wood Limited', 'scissor-wood-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Scissorwood Ltd', 'scissorwood-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Scott Bower', 'scott-bower', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Scott Gray', 'scott-gray', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Scribes Kitchens Limited', 'scribes-kitchens-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('SDK Somerset & Dorset Ltd', 'sdk-somerset-dorset-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sean Keeler', 'sean-keeler', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Selco Builders Warehouse (SO71)', 'selco-builders-warehouse-so71', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Selco Trade Centres Ltd', 'selco-trade-centres-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Select Interiors (Southern) Ltd', 'select-interiors-southern-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Select Kitchens Yorkshire Ltd', 'select-kitchens-yorkshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Select Line Kitchen & Flooring', 'select-line-kitchen-flooring', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Selfridges', 'selfridges', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sentinall Trading Ltd', 'sentinall-trading-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Serenity Kitchens Ltd', 'serenity-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sergiy Ivashchenko', 'sergiy-ivashchenko', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Seventy-One Group Ltd', 'seventy-one-group-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sharon Kitchens', 'sharon-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Shaun Emery', 'shaun-emery', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Shaun Harvey', 'shaun-harvey', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Shaun Perks', 'shaun-perks', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Shazi Design & Build Ltd', 'shazi-design-build-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sheaf Valley Kitchens Ltd', 'sheaf-valley-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sheffield Kitchen Interiors Ltd', 'sheffield-kitchen-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sheffield Solid Surfaces Ltd', 'sheffield-solid-surfaces-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sheraton Interiors Ltd', 'sheraton-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sherwood Cabinets Ltd', 'sherwood-cabinets-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sherwood Ltd', 'sherwood-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sheths Interiors Ltd', 'sheths-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Shine Food Machinery Ltd', 'shine-food-machinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Shop Direct', 'shop-direct', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Showcase Kitchens Ltd', 'showcase-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Showcom Limited T/A County Kitchens', 'showcom-limited-ta-county-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Shropshire Homes Ltd', 'shropshire-homes-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('SIA Kitchens Ltd', 'sia-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sidney Bakewell', 'sidney-bakewell', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Signature Interiors Ltd', 'signature-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Silke Design Ltd', 'silke-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simkal Ltd', 'simkal-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simon Dewey Woodworking', 'simon-dewey-woodworking', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simon Gent Ltd', 'simon-gent-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simon Jennings', 'simon-jennings', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simon Moore', 'simon-moore', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simpaul Designs Ltd', 'simpaul-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simple Kitchens (Thame) Ltd', 'simple-kitchens-thame-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Simply Kitchens Uk Ltd', 'simply-kitchens-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sion Jones', 'sion-jones', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Siros Design Ltd', 'siros-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Skylark Homes Ltd', 'skylark-homes-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Skyline Kitchen Components Ltd', 'skyline-kitchen-components-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Slide-IT Company Limited', 'slide-it-company-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Smart Home Projects (Cheshire) Ltd', 'smart-home-projects-cheshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Smart Management By Design Ltd', 'smart-management-by-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Smit&Sons Joinery Ltd', 'smitsons-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Smyth Patterson Ltd', 'smyth-patterson-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Snowdonia Interiors Ltd', 'snowdonia-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('SO Kitchens & Bathrooms Ltd', 'so-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Solo Spaces Ltd', 'solo-spaces-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Son Of Choam Ltd', 'son-of-choam-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sonic', 'sonic', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sonic Megastore Ltd', 'sonic-megastore-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sonna Kitchens Ltd', 'sonna-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sors Interiors Limited', 'sors-interiors-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Soundstore', 'soundstore', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('South Coast Industrices Portland (Combens)', 'south-coast-industrices-portland-combens', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('South East Property', 'south-east-property', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Southpoint Electrical Ltd', 'southpoint-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Southwest Interiors Ltd', 'southwest-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Spaces', 'spaces', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Spaces CI Ltd', 'spaces-ci-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sparkworld Ltd', 'sparkworld-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sperrin Electronics', 'sperrin-electronics', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Spoke Interior LTD', 'spoke-interior-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Spoons Kitchens Ltd', 'spoons-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('SPR Builders Ltd', 'spr-builders-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Square Deal TV Rentals', 'square-deal-tv-rentals', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Squarepeg Solutions Ltd', 'squarepeg-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('St Albans Interiors Limited', 'st-albans-interiors-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stable Kitchens Ltd', 'stable-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Staines & Brights Ltd', 'staines-brights-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stanmore Kitchens Ltd', 'stanmore-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Star Bedrooms Limited', 'star-bedrooms-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stark Building Materials UK Limited', 'stark-building-materials-uk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stead Kitchen Design Ltd', 'stead-kitchen-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stearn', 'stearn', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Steepal Of Henley', 'steepal-of-henley', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stellison Ltd', 'stellison-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stephan Halford', 'stephan-halford', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stephen & Cavalli Ltd', 'stephen-cavalli-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stephen Biggs', 'stephen-biggs', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stephen Campbell', 'stephen-campbell', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stephen Greenfield', 'stephen-greenfield', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stephen Lightfoot', 'stephen-lightfoot', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Steve David & Sons Ltd', 'steve-david-sons-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Steve Ensor', 'steve-ensor', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Steven Rushton', 'steven-rushton', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stewart & Young Ltd', 'stewart-young-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sticks and Stones Interiors', 'sticks-and-stones-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stonehouse Furniture Design Ltd', 'stonehouse-furniture-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stoneywell Midlands Ltd', 'stoneywell-midlands-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stortford Kitchens Ltd', 'stortford-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stourbridge Kitchens & Bathrooms', 'stourbridge-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stowmarket Plumbing & Bathroom', 'stowmarket-plumbing-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Strawberry Kitchens Ltd', 'strawberry-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Strickley Homeworx Ltd', 'strickley-homeworx-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stuart Gale', 'stuart-gale', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stuart Henry Kitchens Ltd', 'stuart-henry-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stuart TV', 'stuart-tv', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Stuart Westmoreland (Holdings) Ltd', 'stuart-westmoreland-holdings-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Studio 30 Interiors Ltd', 'studio-30-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Studio Four (SW) Ltd', 'studio-four-sw-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Style Bathroom Specialists', 'style-bathroom-specialists', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Style Home Solutions Ltd', 'style-home-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Supafit Ltd', 'supafit-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Supasink Ltd', 'supasink-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Superdeal', 'superdeal', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Superior Cabinets Ltd', 'superior-cabinets-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Superior Interiors & More Ltd', 'superior-interiors-more-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sussex Kitchen Designs Ltd', 'sussex-kitchen-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sussex Tile Centre Ltd', 'sussex-tile-centre-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Swift Kitchens & Bedrooms', 'swift-kitchens-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Swiftcut Automation Ltd', 'swiftcut-automation-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sydenhams Ltd', 'sydenhams-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Sydenhams Ltd (Warminster)', 'sydenhams-ltd-warminster', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Symphony Properties Ltd', 'symphony-properties-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('System Bedrooms Ltd', 'system-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('T G Builders Merchants Ltd', 't-g-builders-merchants-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('T G Taylor Plumbing & Heating', 't-g-taylor-plumbing-heating', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('T H Colebourn Ltd', 't-h-colebourn-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('T N McKay', 't-n-mckay', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('T P D Interiors Ltd', 't-p-d-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tailor Made Sussex Ltd', 'tailor-made-sussex-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tanner Furniture Designs Ltd', 'tanner-furniture-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tara Neil Ltd', 'tara-neil-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tarleton Kitchens & Bathrooms', 'tarleton-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Taylor Made (NE) Ltd', 'taylor-made-ne-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Taylors of Giffnock LTD', 'taylors-of-giffnock-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TC Kitchens Limited', 'tc-kitchens-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TC Kitchens Ltd', 'tc-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tc, Dl & CF Nowlan, P Haynes', 'tc-dl-cf-nowlan-p-haynes', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Team Ashe Ltd', 'team-ashe-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tecaz Limited', 'tecaz-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tefore Ltd', 'tefore-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tempo Kitchens & Bedrooms Ltd', 'tempo-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ten & Sen Ltd', 'ten-sen-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Terence Cocking', 'terence-cocking', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Terence Goodwin', 'terence-goodwin', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TFI (Concepts) Ltd', 'tfi-concepts-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thain Electrical Ltd', 'thain-electrical-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thames Times Group Ltd', 'thames-times-group-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thanetwide Domestics (Kent) Ltd', 'thanetwide-domestics-kent-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Aldridge Kitchen Company Ltd', 'the-aldridge-kitchen-company-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Alpha Studio Ltd', 'the-alpha-studio-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Annex (Norfolk) Limited', 'the-annex-norfolk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Appliance Store', 'the-appliance-store', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Appliance Store Ltd', 'the-appliance-store-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Art of Living (Hamble) Ltd', 'the-art-of-living-hamble-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Brownlow Furniture Cabinet', 'the-brownlow-furniture-cabinet', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Design Studio (Ayr) Ltd', 'the-design-studio-ayr-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Ewan House Furniture Company', 'the-ewan-house-furniture-company', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Fridge & Freezer Company Ltd', 'the-fridge-freezer-company-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Gallery (Runcorn) Ltd', 'the-gallery-runcorn-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Home Transformation Company', 'the-home-transformation-company', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Joinery Mill Ltd', 'the-joinery-mill-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen & Bedroom Warehouse', 'the-kitchen-bedroom-warehouse', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen And Bedroom Workshop', 'the-kitchen-and-bedroom-workshop', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Bros Ltd', 'the-kitchen-bros-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Concept (Redditch)', 'the-kitchen-concept-redditch', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Den', 'the-kitchen-den', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Factory Ltd', 'the-kitchen-factory-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Spot Ltd', 'the-kitchen-spot-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Station Hillington', 'the-kitchen-station-hillington', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Studio of Devon Ltd', 'the-kitchen-studio-of-devon-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchen Stylist Ltd', 'the-kitchen-stylist-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Kitchens Inclusive Ltd', 'the-kitchens-inclusive-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Lincoln Kitchen Co Ltd', 'the-lincoln-kitchen-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Little Kitchen and Bathroom', 'the-little-kitchen-and-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Local Kitchen Company Ltd', 'the-local-kitchen-company-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Marlow Partnership Ltd', 'the-marlow-partnership-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Nowlan Project Ltd', 'the-nowlan-project-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Painted Kitchen Company Ltd', 'the-painted-kitchen-company-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Ratio Interiors Ltd', 'the-ratio-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Regency Kitchen & Bathroom', 'the-regency-kitchen-bathroom', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Rivers Project Ltd', 'the-rivers-project-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Tiling Emporium Limited', 'the-tiling-emporium-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Umbrella Project', 'the-umbrella-project', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('The Wash House Ltd -Admin Only', 'the-wash-house-ltd-admin-only', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TheDevonKitchenCompany Limited', 'thedevonkitchencompany-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Think Doors Ltd', 'think-doors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Think Granite Ltd', 'think-granite-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('This Is My Kitchen Limited', 'this-is-my-kitchen-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thomas Abbey', 'thomas-abbey', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thomas Charles Grant Ltd', 'thomas-charles-grant-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thomas Kitchens & Bedrooms Ltd', 'thomas-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thomson Interiors Ltd', 'thomson-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Thoroughly Wood Furniture Ltd', 'thoroughly-wood-furniture-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tiger Kitchens Ltd', 'tiger-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tiles R Us Ltd', 'tiles-r-us-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tilestyle Lancashire Limited', 'tilestyle-lancashire-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tim Price', 'tim-price', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Timeless Cabinetry Ltd', 'timeless-cabinetry-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Timeless Kitchens & Bathrooms', 'timeless-kitchens-bathrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Timothy David Smith', 'timothy-david-smith', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tina Hogarth', 'tina-hogarth', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tiree Design 59 Ltd', 'tiree-design-59-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tisdalls Electrical Ltd', 'tisdalls-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TKS Cholsey Ltd', 'tks-cholsey-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TMA Construction Ltd', 'tma-construction-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Toby Haward', 'toby-haward', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Todays Kitchens & Bedrooms Ltd', 'todays-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tony Hewitt', 'tony-hewitt', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tony Sargeant', 'tony-sargeant', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tony Trott', 'tony-trott', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Top Discount Electrical Stores', 'top-discount-electrical-stores', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Top Notch Interior Designs', 'top-notch-interior-designs', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Top Quality Kitchens And Bedrooms', 'top-quality-kitchens-and-bedrooms', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Toplex Ltd', 'toplex-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Total Carpets & Flooring Ltd', 'total-carpets-flooring-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Total Interiors Worcester Ltd', 'total-interiors-worcester-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Totnes Tile and Bath Ltd', 'totnes-tile-and-bath-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tower Kitchens Ltd', 'tower-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trade Forward', 'trade-forward', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trade Kitchens (NWL) Ltd', 'trade-kitchens-nwl-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trade Mark Kitchens Ltd', 'trade-mark-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trade Secret (Home Improvement)', 'trade-secret-home-improvement', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TradeMark K & B Design', 'trademark-k-b-design', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Traditional Joinery (Mids) Ltd', 'traditional-joinery-mids-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trainer Game Limited', 'trainer-game-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Transat Home Technologies Ltd', 'transat-home-technologies-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trevi Home Ltd', 'trevi-home-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trish Peter', 'trish-peter', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Trybass Ltd', 'trybass-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tudors Bldg Supp. (Hereford) Ltd', 'tudors-bldg-supp-hereford-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tula Tables Ltd', 'tula-tables-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Turnbull & Co Ltd', 'turnbull-co-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Turnbull and Company Limited', 'turnbull-and-company-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('TV World', 'tv-world', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Twenty 5 Design Ltd', 'twenty-5-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Tylers Of Bilston Ltd', 'tylers-of-bilston-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Uber Kitchens & Bedrooms Ltd', 'uber-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ubhi Kitchens', 'ubhi-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('UK Kitchens Ltd', 'uk-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('UK Plumbing Supplies Ltd', 'uk-plumbing-supplies-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('UK-Instal Ltd', 'uk-instal-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Unique Bedrooms Direct Limited', 'unique-bedrooms-direct-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Unique Interior Solutions Ltd', 'unique-interior-solutions-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('United Furniture (AB) Ltd', 'united-furniture-ab-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Unitrade 2015 Ltd', 'unitrade-2015-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Uplands Kitchens Ltd', 'uplands-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Urban Haus Design Studio Ltd', 'urban-haus-design-studio-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('URNU Kitchens UK Ltd', 'urnu-kitchens-uk-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Used Kitchen Exchange Ltd', 'used-kitchen-exchange-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Utopia Kitchens Ltd', 'utopia-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('V R Design & Manufacturing Ltd', 'v-r-design-manufacturing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Van Haaren Appliance World Ltd', 'van-haaren-appliance-world-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Varcurn Marble Ltd', 'varcurn-marble-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Varker Appliance Services', 'varker-appliance-services', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Vartid Holdings Ltd', 'vartid-holdings-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Vaughans', 'vaughans', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Vaughans Radio Ltd', 'vaughans-radio-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Ventura Kitchens Ltd', 'ventura-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Very', 'very', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Victoria Robert Ltd', 'victoria-robert-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Vintage Services (North West)', 'vintage-services-north-west', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Virdi''s (North) Ltd', 'virdis-north-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Vision Kitchens & Bedrooms Ltd', 'vision-kitchens-bedrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Vision Sales Direct', 'vision-sales-direct', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Volcania Holdings Ltd', 'volcania-holdings-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('W & H S Emery Company Ltd', 'w-h-s-emery-company-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('W M Budd & Sons Group Ltd', 'w-m-budd-sons-group-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wades', 'wades', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wall 2 Wall Carpentry', 'wall-2-wall-carpentry', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Walters & Co Yorkshire Ltd', 'walters-co-yorkshire-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Waltons Direct', 'waltons-direct', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Washroom-Washroom Limited', 'washroom-washroom-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Watermark P H S Ltd', 'watermark-p-h-s-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Watters Electrical', 'watters-electrical', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wayne Adamson', 'wayne-adamson', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wayne Dodd', 'wayne-dodd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('WBC Ltd', 'wbc-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Webbs of Cannock Ltd', 'webbs-of-cannock-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wellingtons (Erith) Ltd', 'wellingtons-erith-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Welton H.I.C. Limited', 'welton-hic-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('West Coast Kitchen Studios Ltd', 'west-coast-kitchen-studios-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('West Country Kitchens Ltd', 'west-country-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('West End Interiors Ltd', 'west-end-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('West London Kitchen', 'west-london-kitchen', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Westwood Ltd', 'westwood-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wharfedale Interiors Ltd', 'wharfedale-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wharfedale Kitchens & Fitted', 'wharfedale-kitchens-fitted', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Whitakers of Shipley', 'whitakers-of-shipley', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('White Feather Kitchen Design Ltd', 'white-feather-kitchen-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('WHQS Ltd', 'whqs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wickes Building Supplies', 'wickes-building-supplies', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Willett Homemaker Ltd', 'willett-homemaker-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('William Cope Interior Design Ltd', 'william-cope-interior-design-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('William John Galliford Ltd', 'william-john-galliford-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('William John Graham', 'william-john-graham', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Williams Homes & Developments', 'williams-homes-developments', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Willoughby Ltd', 'willoughby-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Willow Luxury Kitchens Ltd', 'willow-luxury-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Willow Tree Interiors Ltd', 'willow-tree-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wilmslow Electrical Ltd', 'wilmslow-electrical-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wilmslow Kitchen Interiors Ltd', 'wilmslow-kitchen-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wilvan Int Design & Install Ltd', 'wilvan-int-design-install-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Winwick Park Kitchens Ltd', 'winwick-park-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wisteria Kitchens Ltd', 'wisteria-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wood Bros. Heanor Ltd', 'wood-bros-heanor-ltd', '30-90', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Woodbridge Kitchens', 'woodbridge-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Woodhouse Installations', 'woodhouse-installations', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Woodland Bespoke Joinery Limited', 'woodland-bespoke-joinery-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Woodland Interiors Ltd', 'woodland-interiors-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Woods & Sons Kent Ltd', 'woods-sons-kent-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Woodworkshop Design Limited', 'woodworkshop-design-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Woolley Kitchens & Bathrooms Ltd', 'woolley-kitchens-bathrooms-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Worcestershire Designs Ltd', 'worcestershire-designs-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Workflow Options Ltd', 'workflow-options-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('World of Tiles Kitchen &', 'world-of-tiles-kitchen-', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Worldwide Kitchens Ltd', 'worldwide-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wren Kitchens', 'wren-kitchens', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wrigglesworth Interiors', 'wrigglesworth-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Wycombe Housing Ltd', 'wycombe-housing-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Xcel Kitchens UK Limited', 'xcel-kitchens-uk-limited', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Y K Carpentry & Joinery Ltd', 'y-k-carpentry-joinery-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Yealm Kitchens Ltd', 'yealm-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Yorkshire Kitchens and Interiors', 'yorkshire-kitchens-and-interiors', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Z S Kitchen & Carpentry Ltd', 'z-s-kitchen-carpentry-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1),
    ('Zentrum Fitted Kitchens Ltd', 'zentrum-fitted-kitchens-ltd', '30-180', 'active', false, false, CURRENT_TIMESTAMP, 1)
ON CONFLICT (slug) 
DO NOTHING;

-- Create version update trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_version()
RETURNS TRIGGER AS $$
BEGIN
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create cleanup trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION cleanup_retailer_skus()
RETURNS TRIGGER AS $$
BEGIN
    -- Add your cleanup logic here if needed
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS update_retailer_version ON retailers;
CREATE TRIGGER update_retailer_version
    BEFORE UPDATE ON retailers
    FOR EACH ROW
    EXECUTE FUNCTION update_version();

DROP TRIGGER IF EXISTS cleanup_retailer_skus_trigger ON retailers;
CREATE TRIGGER cleanup_retailer_skus_trigger
    BEFORE DELETE ON retailers
    FOR EACH ROW
    EXECUTE FUNCTION cleanup_retailer_skus();
