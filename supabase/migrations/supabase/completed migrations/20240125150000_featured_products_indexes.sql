begin;

-- Add index for featured products filtering
create index if not exists idx_products_featured on public.products(is_featured, status)
where is_featured = true and status = 'active';

-- Add index for valid promotions
create index if not exists idx_product_retailer_promotions_valid on public.product_retailer_promotions(valid_until, product_id)
where valid_until > '2024-01-01'::date;

-- Add composite index for product retailer promotions
create index if not exists idx_prp_product_valid on public.product_retailer_promotions(product_id, valid_until);

commit;
