CREATE OR REPLACE FUNCTION get_brand_category_summary(brand_id_param UUID)
RETURNS TABLE (
    category_name TEXT,
    max_cashback DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.name,
        MAX(p.cashback_rate) AS max_cashback
    FROM categories c
    JOIN promotions p ON c.id = p.category_id
    WHERE p.status = 'active' 
    AND p.brand_id = brand_id_param
    GROUP BY c.name;
END;
$$ LANGUAGE plpgsql; 