begin;

-- Migration: Combined Cashback Discovery Hub Schema
-- Description: Merged schema with version annotations and change explanations
-- Author: Database Administrator
-- Date: 2025-01-15

-- Enable required extensions
create extension if not exists "uuid-ossp";
create extension if not exists "pg_trgm";  -- For text search operations
create extension if not exists "unaccent"; --a text search dictionary which removes diacritics
create extension if not exists "fuzzystrmatch"; -- determine string similarities and distance

 

-- ===============================
-- [v2] Custom Types
-- Description: ENUMs for better data integrity and consistency
-- Reason for change: Prevent invalid status values and ensure consistency
-- ===============================
create type public.stock_status_type as enum (
    'in_stock',
    'low_stock',
    'out_of_stock',
    'discontinued',
    'pre_order'
);
create type public.promotion_status_type as enum (
    'draft',
    'scheduled',
    'active',
    'paused',
    'expired',
    'cancelled'
);

-- ===============================
-- [v2] Audit Log Table
-- Description: Centralized audit log for tracking changes
-- Reason for change: Enhanced data monitoring and security
-- ===============================
create table public.audit_log (
    id uuid primary key default gen_random_uuid(),
    table_name text not null,
    record_id uuid not null,
    operation text not null,
    old_data jsonb,
    new_data jsonb,
    changed_by uuid references auth.users(id),
    changed_at timestamp default current_timestamp
);
comment on table public.audit_log is 'Tracks all changes to important tables';

-- ===============================
-- [v2] Cache Invalidation Log
-- Description: Tracks cache refresh events
-- Reason for change: Improved cache management and debugging
-- ===============================
create table public.cache_invalidation_log (
    id uuid primary key default gen_random_uuid(),
    cache_key text not null,
    invalidated_at timestamp default current_timestamp,
    reason text,
    triggered_by uuid references auth.users(id)
);
comment on table public.cache_invalidation_log is 'Tracks cache refresh events';



-- ===============================
-- [v2] Categories Management
-- Description: Product categories hierarchy
-- Includes: Base table, versioning, RLS policies
-- Reason for change: Added versioning for optimistic locking
-- ===============================
create table public.categories (
    id uuid primary key default gen_random_uuid(),
    name varchar(100) not null,
    slug varchar(100) unique not null,
    parent_id uuid references public.categories(id),
    featured boolean default false,
    sponsored boolean default false,
    created_at timestamp default current_timestamp,
    version bigint default 1
);
comment on table public.categories is 'Product categories hierarchy';

alter table public.categories enable row level security;

create policy "Categories are viewable by everyone"
    on public.categories for select
    to anon
    using (true);

-- ===============================
-- [v2] Brands Management
-- Description: Product brands information
-- Includes: Base table, versioning, RLS policies
-- Reason for change: Added versioning for optimistic locking
-- ===============================
create table public.brands (
    id uuid primary key default gen_random_uuid(),
    name varchar(100) unique not null,
    slug varchar(100) unique not null,
    logo_url varchar(255),
    description text,
    featured boolean default false,
    sponsored boolean default false,
    created_at timestamp default current_timestamp,
    updated_at timestamp default current_timestamp,
    version bigint default 1
);
comment on table public.brands is 'Product brands information';

alter table public.brands enable row level security;

create policy "Brands are viewable by everyone"
    on public.brands for select
    to anon
    using (true);

-- ===============================
-- [v2] Retailers Management
-- Description: Retailer information and API credentials
-- Includes: Base table, versioning, RLS policies
-- Reason for change: Added API credentials and versioning
-- ===============================
create table public.retailers (
    id uuid primary key default gen_random_uuid(),
    name varchar(150) unique not null,
    slug varchar(150) unique not null,
    logo_url varchar(255),
    status varchar(20) default 'active',
    featured boolean default false,
    sponsored boolean default false,
    api_key_hash varchar(255),
    api_secret_hash varchar(255),
    created_at timestamp default current_timestamp,
    version bigint default 1
);
comment on table public.retailers is 'Retailer information and API credentials';

alter table public.retailers enable row level security;

create policy "Retailers are viewable by everyone"
    on public.retailers for select
    to anon
    using (true);

-- ===============================
-- [v2] Products Management
-- Description: Main product catalog
-- Includes: Base table, constraints, versioning, RLS policies
-- Reason for change: Added constraints for data integrity, versioning
-- ===============================
create table public.products (
    id uuid primary key default gen_random_uuid(),
    name varchar(150) not null,
    slug varchar(150) unique not null,
    brand_id uuid references public.brands(id) on delete restrict,
    category_id uuid references public.categories(id) on delete restrict,
    description text,
    specifications jsonb,
    images text[],
    status varchar(20) default 'active',
    is_featured boolean default false,
    is_sponsored boolean default false,
    created_at timestamp default current_timestamp,
    updated_at timestamp default current_timestamp,
    version bigint default 1,
    constraint valid_status check (status in ('active', 'inactive', 'discontinued'))
);
comment on table public.products is 'Main product catalog';

alter table public.products enable row level security;

create policy "Products are viewable by everyone"
    on public.products for select
    to anon
    using (status = 'active');





-- ===============================
-- [v1] Users Management
-- Description: User information and access control
-- Includes: Base table, RLS policies
-- ===============================
create table public.users (
  id uuid primary key default gen_random_uuid(),
  full_name varchar(100),
  email varchar(150) unique not null,
  password_hash varchar(255),
  profile_picture_url varchar(255),
  country_code varchar(5),
  preferred_language varchar(5),
  created_at timestamp default current_timestamp,
  updated_at timestamp default current_timestamp
);
comment on table public.users is 'Stores user profile information and authentication details';

alter table public.users enable row level security;

create policy "Users can view their own profile"
  on public.users for select
  to authenticated
  using (auth.uid() = id);
create policy "Users can update their own profile"
  on public.users for update
  to authenticated
  using (auth.uid() = id);

-- ===============================
-- [v1] User Favorites
-- Description: Manages user's favorite products
-- Includes: Base table, RLS policies
-- ===============================
create table public.user_favorites (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references public.users(id) on delete cascade,
  product_id uuid references public.products(id) on delete cascade,
  created_at timestamp default current_timestamp
);
comment on table public.user_favorites is 'User-saved favorite products';

alter table public.user_favorites enable row level security;

create policy "Users can view their own favorites"
  on public.user_favorites for select
  to authenticated
  using (user_id = auth.uid());
create policy "Users can add their own favorites"
  on public.user_favorites for insert
  to authenticated
  with check (user_id = auth.uid());
create policy "Users can delete their own favorites"
  on public.user_favorites for delete
  to authenticated
  using (user_id = auth.uid());




-- ===============================
-- [v2] SKUs Management
-- Description: Product SKUs and variants
-- Includes: Base table, constraints, versioning, RLS policies
-- Reason for change: Introduced SKUs for better variant management
-- ===============================
create table public.skus (
    id uuid primary key default gen_random_uuid(),
    product_id uuid references public.products(id) on delete restrict,
    retailer_id uuid references public.retailers(id),
    manufacturer_sku varchar(50) not null,
    retailer_sku varchar(50),
    gtin varchar(14),
    mpn varchar(50),
    color varchar(50),
    size varchar(50),
    variant_name varchar(100),
    is_default boolean default false,
    status varchar(20) default 'active',
    metadata jsonb not null,
    created_at timestamp default current_timestamp,
    updated_at timestamp default current_timestamp,
    version bigint default 1,
    constraint valid_manufacturer_sku check (length(manufacturer_sku) between 3 and 50),
    constraint valid_retailer_sku check (retailer_sku is null or length(retailer_sku) between 3 and 50),
    constraint valid_gtin check (gtin ~ '^[0-9]{13,14}$'),
    constraint valid_status check (status in ('active', 'inactive', 'discontinued'))
);

-- ✅ Add the unique index to enforce only one default SKU per product
create unique index idx_unique_default_sku_per_product
on public.skus (product_id)
where is_default = true;


comment on table public.skus is 'Product SKUs and variants';

alter table public.skus enable row level security;

create policy "Limited SKU info viewable by everyone"
    on public.skus for select
    to anon
    using (status = 'active' and retailer_id is null);
create policy "Authenticated users can view active SKUs"
    on public.skus for select
    to authenticated
    using (status = 'active');

-- ===============================
-- [v2] Promotions Management
-- Description: Cashback promotions with specific purchase and claim windows
-- Includes: Base table, constraints, versioning, RLS policies
-- Reason for change: Improved promotion mechanics and validation
-- ===============================
create table public.promotions (
    id uuid primary key default gen_random_uuid(),
    brand_id uuid references public.brands(id),
    category_id uuid references public.categories(id),
    title varchar(150) not null,
    description text,
    cashback_amount decimal(10,2) not null, 
    purchase_start_date date not null,
    purchase_end_date date not null,
    claim_start_offset_days int not null,
    claim_window_days int not null,
    terms_url varchar(255),
    terms_description text,
    status promotion_status_type default 'draft',
    last_validated_at timestamp,
    created_at timestamp default current_timestamp,
    version bigint default 1,
    constraint valid_dates check (purchase_start_date < purchase_end_date),
    constraint valid_claim_window check (claim_window_days > 0),
    constraint valid_offset check (claim_start_offset_days >= 0)
);

comment on table public.promotions is 'Cashback promotions with specific purchase and claim windows';

alter table public.promotions enable row level security;

-- Drop existing policy and create new one that allows viewing all promotions
drop policy if exists "Active promotions are viewable by everyone" on public.promotions;

create policy "All promotions are viewable by everyone"
    on public.promotions for select
    to anon
    using (true);




-- ===============================
-- [v2] Product Retailer Promotions
-- Description: Links products, retailers, and promotions with pricing
-- Includes: Base table, constraints, versioning, RLS policies
-- Reason for change: Added constraints, versioning, and SKU linking
-- ===============================
create table public.product_retailer_promotions (
    id uuid primary key default gen_random_uuid(),
    product_id uuid references public.products(id) on delete cascade,
    retailer_id uuid references public.retailers(id) on delete cascade,
    promotion_id uuid references public.promotions(id) on delete set null,
    sku_id uuid references public.skus(id) on delete cascade,
    price decimal(10,2) not null,
    stock_status stock_status_type default 'in_stock',
    url varchar(255),
    valid_from date not null,
    valid_until date not null,
    created_at timestamp default current_timestamp,
    updated_at timestamp default current_timestamp,
    version bigint default 1,
    constraint unique_product_retailer_promotion unique (product_id, retailer_id, promotion_id, valid_from),
    constraint valid_dates check (valid_from < valid_until),
    constraint positive_price check (price > 0),
    );
comment on table public.product_retailer_promotions is 'Links products, retailers, and promotions with pricing information';

alter table public.product_retailer_promotions enable row level security;

-- RLS Policies for product retailer promotions
create policy "Product retailer promotions are viewable by everyone"
  on public.product_retailer_promotions for select
  to anon
  using (true);

create policy "Product retailer promotions are viewable by authenticated users"
  on public.product_retailer_promotions for select
  to authenticated
  using (true);

-- ===============================
-- [v1] Cashback Reminders
-- Description: Handles cashback reminders for users
-- Includes: Base table, RLS policies
-- ===============================
create table public.cashback_reminders (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references public.users(id) on delete cascade,
  promotion_id uuid references public.promotions(id) on delete cascade,
  reminder_date timestamp,
  is_sent boolean default false,
  created_at timestamp default current_timestamp
);
comment on table public.cashback_reminders is 'User notifications for cashback claim deadlines';

alter table public.cashback_reminders enable row level security;

create policy "Users can view their own reminders"
  on public.cashback_reminders for select
  to authenticated
  using (user_id = auth.uid());
create policy "Users can create their own reminders"
  on public.cashback_reminders for insert
  to authenticated
  with check (user_id = auth.uid());

-- ===============================
-- [v2] Create user purchases table

-- ===============================


create table public.user_purchases (
    id uuid primary key default gen_random_uuid(),
    user_id uuid references auth.users(id) on delete restrict,
    product_id uuid references public.products(id) on delete restrict,
    retailer_id uuid references public.retailers(id) on delete restrict,
    promotion_id uuid references public.promotions(id) on delete restrict,
    sku_id uuid references public.skus(id) on delete restrict,
    purchase_date timestamp default current_timestamp,
    purchase_price decimal(10,2) not null,
    affiliate_click_url varchar(255),
    claim_start_date timestamp,
    claim_end_date timestamp,
    claim_submitted_date timestamp,
    claim_status varchar(20) default 'pending',
    created_at timestamp default current_timestamp,
    version bigint default 1,
    constraint valid_claim_status check (claim_status in ('pending', 'submitted', 'approved', 'rejected')),
    constraint positive_purchase_price check (purchase_price > 0)
);

comment on table public.user_purchases is 'Tracks user purchases and cashback claims';

-- ===============================
-- Create SKU audit log table
-- ===============================


create table public.sku_audit_log (
    id uuid primary key default gen_random_uuid(),
    sku_id uuid references public.skus(id),
    field_name varchar(50),
    old_value text,
    new_value text,
    changed_at timestamp default current_timestamp,
    changed_by uuid references auth.users(id)
);

comment on table public.sku_audit_log is 'Tracks changes to SKUs';

-- 
-- ===============================
-- [v2] User Cashback Claims
-- Description: Manages user cashback claims
-- Includes: Base table, constraints, versioning, RLS policies
-- Reason for change: Added constraints and versioning
-- ===============================
create table public.user_cashback_claims (
    id uuid primary key default gen_random_uuid(),
    user_id uuid references public.users(id) on delete cascade,
    product_retailer_promotion_id uuid references public.product_retailer_promotions(id) on delete cascade,
    claim_date timestamp default current_timestamp,
    status varchar(20) default 'pending',
    paid_amount decimal(10,2),
    payment_date timestamp,
    created_at timestamp default current_timestamp,
    version bigint default 1,
    constraint valid_status check (status in ('pending', 'approved', 'rejected', 'paid'))
);
comment on table public.user_cashback_claims is 'Manages user cashback claims';

alter table public.user_cashback_claims enable row level security;

create policy "Users can view their own claims"
    on public.user_cashback_claims for select
    to authenticated
    using (user_id = auth.uid());



-- ===============================
-- Translations table
-- ===============================


create table public.translations (
  id uuid primary key default gen_random_uuid(),
  content_type varchar(50),
  content_id uuid,
  language_code varchar(5),
  translated_text jsonb,
  created_at timestamp default current_timestamp
);

comment on table public.translations is 'Stores translations for various content types';

-- Enable RLS
alter table public.translations enable row level security;

-- RLS Policies for translations
create policy "Translations are viewable by everyone"
  on public.translations for select
  to anon
  using (true);

create policy "Translations are viewable by authenticated users"
  on public.translations for select
  to authenticated
  using (true);




-- Create product summary materialized view
create materialized view public.mv_product_summary as
select 
    p.id as product_id,
    min(prp.price) as min_price,
    max(prp.price) as max_price,
    max(prp.cashback_amount) as max_cashback,
    count(distinct prp.retailer_id) as retailer_count,
    array_agg(distinct s.manufacturer_sku) as manufacturer_skus
from public.products p
join public.product_retailer_promotions prp on p.id = prp.product_id
join public.skus s on prp.sku_id = s.id
where prp.valid_until > current_date
group by p.id;

comment on materialized view public.mv_product_summary is 'Cached summary of product information';


--------------------------------------

create index idx_skus_product_status on public.skus(product_id, status);
create index idx_skus_retailer_status on public.skus(retailer_id, status);
create index idx_promotions_dates on public.promotions(purchase_start_date, purchase_end_date);
create index idx_prp_valid_dates on public.product_retailer_promotions(valid_from, valid_until);
create index idx_prp_stock_status on public.product_retailer_promotions(stock_status);

-- Index on brand_id to speed up queries filtering by brand
create index idx_products_brand_id on public.products(brand_id);

-- Index on category_id to enhance performance for category-based queries
create index idx_products_category_id on public.products(category_id);

-- Index on status to quickly filter products by their status
create index idx_products_status on public.products(status);

-- Full-text search index on name and description for efficient search operations
create index idx_products_search on public.products using gin (to_tsvector('english', name || ' ' || coalesce(description, '')));

-- Composite index on product_id and status to optimize queries filtering by product and status
create index idx_skus_product_id_status on public.skus(product_id, status);

-- Composite index on retailer_id and status to enhance performance for retailer-based queries
create index idx_skus_retailer_id_status on public.skus(retailer_id, status);

-- GIN index on metadata for efficient querying of JSONB data
create index idx_skus_metadata on public.skus using gin (metadata);

-- Index on lower(manufacturer_sku) to allow case-insensitive searches
create index idx_skus_manufacturer_sku_lower on public.skus(lower(manufacturer_sku));

-- Index on lower(retailer_sku) to support case-insensitive searches
create index idx_skus_retailer_sku_lower on public.skus(lower(retailer_sku));

-- Index on gtin to speed up searches based on Global Trade Item Number
create index idx_skus_gtin on public.skus(gtin);

-- Composite index on purchase_start_date and purchase_end_date to optimize date range queries
create index idx_promotions_purchase_dates on public.promotions(purchase_start_date, purchase_end_date);

-- Index on status to quickly filter promotions by their status
create index idx_promotions_status on public.promotions(status);

---------------------------------------
-- Enable Row Level Security on all tables


alter table public.user_purchases enable row level security;
alter table public.sku_audit_log enable row level security;
alter table public.audit_log enable row level security;
alter table public.cache_invalidation_log enable row level security;


-----------------------------------------------


-- User purchases policies
create policy "Users can view their own purchases"
    on public.user_purchases for select
    to authenticated
    using (user_id = auth.uid());

create policy "Users can create purchase records"
    on public.user_purchases for insert
    to authenticated
    with check (user_id = auth.uid());

-- Audit log policies
create policy "Staff can view audit logs"
    on public.audit_log for select
    to authenticated
    using (
        exists (
            select 1
            from auth.users
            where auth.uid() = id
            -- and role = 'staff' -- TODO: Uncomment this line to enforce staff role check
        )
    );










-- ===============================
-- [v1] Functions
-- Description: Custom functions for various operations
-- ===============================

-- Create function to calculate claim dates


create or replace function public.calculate_claim_dates()
returns trigger as $$
begin
  -- Calculate claim start date based on promotion offset
  select 
    new.purchase_date + (p.claim_start_offset_days * interval '1 day'),
    new.purchase_date + (p.claim_start_offset_days * interval '1 day') + (p.claim_window_days * interval '1 day')
  into new.claim_start_date, new.claim_end_date
  from public.promotions p
  where p.id = new.promotion_id;
  
  return new;
end;
$$ language plpgsql;



-- Function to validate SKU metadata

-- Function to validate SKU metadata
CREATE OR REPLACE FUNCTION public.validate_sku_metadata()
RETURNS trigger AS $$
DECLARE
    required_fields TEXT[] := ARRAY['specifications', 'dimensions', 'features'];
    field TEXT;
BEGIN
    -- Check if metadata has more than 20 keys
    IF jsonb_object_keys(NEW.metadata) IS NULL OR jsonb_array_length(jsonb_object_keys(NEW.metadata)::jsonb) > 20 THEN
        RAISE EXCEPTION 'Metadata exceeds maximum allowed properties (20)';
    END IF;

    -- Corrected FOREACH syntax
    FOREACH field IN ARRAY required_fields
    LOOP
        IF NOT (NEW.metadata ? field) THEN
            RAISE EXCEPTION 'Required metadata field % is missing', field;
        END IF;
    END LOOP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;



-- Function to update promotion status
create or replace function public.update_promotion_status()
returns trigger as $$
begin
    -- Set status based on dates and current status
    if new.purchase_end_date < current_date then
        new.status = 'expired';
    elsif new.purchase_start_date > current_date then
        new.status = 'scheduled';
    elsif new.status = 'expired' then
        -- Keep expired status if manually set
        new.status = 'expired';
    else
        new.status = 'active';
    end if;
    
    new.last_validated_at = current_timestamp;
    return new;
end;
$$ language plpgsql;



-- Function to handle retailer removal
create or replace function public.cleanup_retailer_skus()
returns trigger as $$
begin
    -- Log the changes
    insert into public.audit_log (
        table_name,
        record_id,
        operation,
        old_data,
        new_data,
        changed_by
    )
    select 
        'retailers',
        old.id,
        'delete',
        row_to_json(old),
        null,
        auth.uid();

    -- Update associated SKUs
    update public.skus
    set 
        status = 'inactive',
        retailer_id = null,
        updated_at = current_timestamp,
        version = version + 1
    where retailer_id = old.id;

    return old;
end;
$$ language plpgsql;


-- Function to refresh materialized view with logging
create or replace function public.refresh_product_summary_with_logging()
returns void as $$
begin
    refresh materialized view concurrently public.mv_product_summary;
    
    insert into public.cache_invalidation_log (
        cache_key,
        reason,
        triggered_by
    )
    values (
        'mv_product_summary',
        'Manual refresh triggered',
        auth.uid()
    );
end;
$$ language plpgsql security definer;


-- Function to validate stock status transitions
create or replace function public.validate_stock_status_transition()
returns trigger as $$
begin
    -- Prevent direct transition from 'out_of_stock' to 'in_stock' without staff review
    if old.stock_status = 'out_of_stock' and new.stock_status = 'in_stock' then
        if not exists (
            select 1 from auth.users 
            where auth.uid() = id 
            -- and role = 'staff' -- TODO: Uncomment this line to enforce staff role check
        ) then
            raise exception 'Stock status transition requires staff review';
        end if;
    end if;
    return new;
end;
$$ language plpgsql security definer;




-- Function to check if a product is a favorite of a user
create or replace function public.is_product_favorite(user_id uuid, product_id uuid)
returns boolean as $$
begin
  return exists (
    select 1
    from public.user_favorites
    where user_id = user_id and product_id = product_id
  );
end;
$$ language plpgsql;

-- Function to get the cashback amount for a product
create or replace function public.get_cashback_amount(product_id uuid)
returns decimal(10,2) as $$
begin
    return (
        select prp.cashback_amount
        from public.product_retailer_promotions prp
        where prp.product_id = product_id
        and prp.valid_until >= current_date
        order by prp.cashback_amount desc
        limit 1
    );
end;
$$ language plpgsql;


-- ===============================
-- [v1] Triggers
-- Description: Database triggers for automated actions
-- ===============================


-- Trigger for SKU metadata validation
create trigger validate_sku_metadata_trigger
    before insert or update of metadata on public.skus
    for each row
    execute function public.validate_sku_metadata();

-- Trigger for promotion status updates
create trigger auto_update_promotion_status
    before insert or update on public.promotions
    for each row
    execute function public.update_promotion_status();
    
-- Create trigger to automatically calculate claim dates on purchase
create trigger set_claim_dates
  before insert on public.user_purchases
  for each row
  execute function public.calculate_claim_dates();

-- Trigger for retailer cleanup
create trigger cleanup_retailer_skus_trigger
    before delete on public.retailers
    for each row
    execute function public.cleanup_retailer_skus();

 -- Trigger for stock status validation
create trigger enforce_stock_status_transition
    before update of stock_status on public.product_retailer_promotions
    for each row
    execute function public.validate_stock_status_transition();
   

-- Create version update triggers for all main tables
create or replace function public.update_version()
returns trigger as $$
begin
    new.version = old.version + 1;
    return new;
end;
$$ language plpgsql;

-- Add version update triggers to all relevant tables
create trigger update_product_version
    before update on public.products
    for each row
    execute function public.update_version();


-- Trigger to update the updated_at column in the users table
create or replace function public.users_updated_at_trigger()
returns trigger as $$
begin
  new.updated_at = current_timestamp;
  return new;
end;
$$ language plpgsql;

create trigger users_updated_at_trigger
before update on public.users
for each row
execute function public.users_updated_at_trigger();

-- Trigger to update the updated_at column in the product_retailer_promotions table
create or replace function public.product_retailer_promotions_updated_at_trigger()
returns trigger as $$
begin
  new.updated_at = current_timestamp;
  return new;
end;
$$ language plpgsql;

create trigger update_sku_version
    before update on public.skus
    for each row
    execute function public.update_version();

create trigger update_promotion_version
    before update on public.promotions
    for each row
    execute function public.update_version();

create trigger update_retailer_version
    before update on public.retailers
    for each row
    execute function public.update_version();

-- Add audit logging trigger function
create or replace function public.audit_changes()
returns trigger as $$
begin
    insert into public.audit_log (
        table_name,
        record_id,
        operation,
        old_data,
        new_data,
        changed_by
    )
    values (
        TG_TABLE_NAME,
        coalesce(new.id, old.id),
        TG_OP,
        case when TG_OP = 'DELETE' then row_to_json(old) else null end,
        case when TG_OP in ('INSERT', 'UPDATE') then row_to_json(new) else null end,
        auth.uid()
    );
    return coalesce(new, old);
end;
$$ language plpgsql security invoker;


create trigger product_retailer_promotions_updated_at_trigger
before update on public.product_retailer_promotions
for each row
execute function public.product_retailer_promotions_updated_at_trigger();


-- Add audit triggers to main tables
create trigger audit_products
    after insert or update or delete on public.products
    for each row execute function public.audit_changes();

create trigger audit_skus
    after insert or update or delete on public.skus
    for each row execute function public.audit_changes();

create trigger audit_promotions
    after insert or update or delete on public.promotions
    for each row execute function public.audit_changes();

-- Create scheduled task for cache refresh
create or replace function public.schedule_cache_refresh()
returns void as $$
begin
    if not exists (
        select 1 
        from public.cache_invalidation_log 
        where cache_key = 'mv_product_summary'
        and invalidated_at > current_timestamp - interval '5 minutes'
    ) then
        perform public.refresh_product_summary_with_logging();
    end if;
end;

$$ language plpgsql security invoker;



-- ===============================
-- [v2] Triggers
-- Description: Database triggers for automated actions and audit logging
-- Reason for change: Added audit log triggers
-- ===============================
-- Trigger to log changes to the users table
create or replace function public.audit_users_changes()
returns trigger as $$
begin
  if tg_op = 'UPDATE' then
    insert into public.audit_log (table_name, record_id, operation, old_data, new_data, changed_by)
    values ('users', old.id, 'UPDATE', row_to_json(old), row_to_json(new), auth.uid());
    return new;
  elsif tg_op = 'DELETE' then
    insert into public.audit_log (table_name, record_id, operation, old_data, changed_by)
    values ('users', old.id, 'DELETE', row_to_json(old), auth.uid());
    return old;
  else
    return new;
  end if;
end;
$$ language plpgsql security invoker;

create trigger audit_users_changes
after insert or update or delete on public.users
for each row
execute function public.audit_users_changes();

-- Trigger to log changes to the products table
create or replace function public.audit_products_changes()
returns trigger as $$
begin
  if tg_op = 'UPDATE' then
    insert into public.audit_log (table_name, record_id, operation, old_data, new_data, changed_by)
    values ('products', old.id, 'UPDATE', row_to_json(old), row_to_json(new), auth.uid());
    return new;
  elsif tg_op = 'DELETE' then
    insert into public.audit_log (table_name, record_id, operation, old_data, changed_by)
    values ('products', old.id, 'DELETE', row_to_json(old), auth.uid());
    return old;
  else
    return new;
  end if;
end;
$$ language plpgsql security invoker;

create trigger audit_products_changes
after insert or update or delete on public.products
for each row
execute function public.audit_products_changes();

-- ===============================
-- [v2] Full Text Search
-- Description: Enables full-text search on products
-- Reason for change: Improved product discoverability
-- ===============================
alter table public.products add column search_vector tsvector
    generated always as (
        setweight(to_tsvector('english', name), 'A') ||
        setweight(to_tsvector('english', description), 'B') ||
        setweight(to_tsvector('english', coalesce(specifications ->> 'features', '')), 'C')
    ) stored;

create index products_search_idx on public.products using gin (search_vector);

commit;
