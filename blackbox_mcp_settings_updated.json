{"mcpServers": {"taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "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"}, "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAgafiH3oFXtZGMSJUiG9UCl_G1ZDE"}}, "github.com/upstash/context7-mcp": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "github.com/supabase-community/supabase-mcp": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}}}