import { test, expect, Page, Request } from '@playwright/test';

test.describe('Products Page Pagination', () => {
  const productsPageUrl = 'http://localhost:3000/products';

  test.beforeEach(async ({ page }: { page: Page }) => {
    // Navigate to the products page before each test
    await page.goto(productsPageUrl);
  });

  test('Pagination UI controls update product list and active page', async ({ page }: { page: Page }) => {
    // Wait for product list to load
    await page.waitForSelector('[data-testid="product-list"]');

    // Click on page 2
    await page.click('button[aria-label="Go to page 2"]');
    // Verify active page indicator updates
    await expect(page.locator('button[aria-current="page"]')).toHaveText('2');
    // Verify product list updates (basic check: product count or presence)
    const productsPage2 = await page.locator('[data-testid="product-item"]').count();
    expect(productsPage2).toBeGreaterThan(0);

    // Click on page 3
    await page.click('button[aria-label="Go to page 3"]');
    await expect(page.locator('button[aria-current="page"]')).toHaveText('3');
    const productsPage3 = await page.locator('[data-testid="product-item"]').count();
    expect(productsPage3).toBeGreaterThan(0);

    // Click Next button if available
    const nextButton = page.locator('button[aria-label="Next page"]');
    if (await nextButton.isVisible() && !(await nextButton.isDisabled())) {
      await nextButton.click();
      // Wait for navigation to complete
      await page.waitForLoadState('networkidle');
      // Active page should increment to 4
      const activePage = await page.locator('button[aria-current="page"]').textContent();
      expect(Number(activePage)).toBe(4);
    }

    // Click Previous button if available
    const prevButton = page.locator('button[aria-label="Previous page"]');
    if (await prevButton.isVisible()) {
      await prevButton.click();
      // Active page should decrement
      const activePage = await page.locator('button[aria-current="page"]').textContent();
      expect(Number(activePage)).toBeGreaterThan(0);
    }
  });

  test('React Query triggers API calls with correct pagination params', async ({ page }: { page: Page }) => {
    // Intercept API calls to products endpoint
    const apiUrlPattern = '**/api/products**';
    let lastRequestUrl = '';

    await page.route(apiUrlPattern, (route: any, request: Request) => {
      lastRequestUrl = request.url();
      route.continue();
    });

    // Click on page 2
    await page.click('button[aria-label="Go to page 2"]');
    await page.waitForResponse(apiUrlPattern);

    // Check that the request URL contains page=2
    expect(lastRequestUrl).toContain('page=2');

    // Click on page 3
    await page.click('button[aria-label="Go to page 3"]');
    await page.waitForResponse(apiUrlPattern);
    expect(lastRequestUrl).toContain('page=3');
  });

  test('API requests are made without requiring cookies (SSG/SSR migration)', async ({ page }: { page: Page }) => {
    // Intercept API calls and check headers
    const apiUrlPattern = '**/api/products**';
    let requestHeaders: Record<string, string> = {};
    let apiCallMade = false;

    await page.route(apiUrlPattern, (route: any, request: Request) => {
      requestHeaders = request.headers();
      apiCallMade = true;
      route.continue();
    });

    // Wait for product list to load first
    await page.waitForSelector('[data-testid="product-list"]');

    // Trigger pagination
    await page.click('button[aria-label="Go to page 2"]');
    await page.waitForResponse(apiUrlPattern);

    // Check that API call was made successfully without requiring cookies
    expect(apiCallMade).toBe(true);
    expect(requestHeaders['user-agent']).toBeDefined(); // Basic header should be present
    // Note: Cookies are not required for SSG/SSR implementation
  });

  test('Error handling on API failure during pagination', async ({ page }: { page: Page }) => {
    const apiUrlPattern = '**/api/products**';

    // Mock API failure for all requests
    await page.route(apiUrlPattern, (route: any) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' }),
      });
    });

    // Navigate to the page (this will trigger the initial API call which will fail)
    await page.goto('http://localhost:3000/products');

    // Expect an error message to be visible
    const errorMessage = page.locator('[data-testid="pagination-error"]');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toHaveText(/error/i);
  });
});
