import { render, screen, waitFor } from '@testing-library/react';
import ProductPage from '@/app/products/[id]/page';
import * as dataLayer from '@/lib/data/products';
import { notFound } from 'next/navigation';
import '@testing-library/jest-dom/extend-expect';

// Add this import to fix toBeInTheDocument type errors
import '@testing-library/jest-dom';

jest.mock('next/navigation', () => ({
  notFound: jest.fn(),
}));

jest.mock('@/components/pages/ProductPageClient', () => ({
  ProductPageClient: () => <div>ProductPageClient Component</div>,
}));

jest.mock('@/components/seo/StructuredData', () => ({
  ProductStructuredData: () => <div>ProductStructuredData Component</div>,
}));

describe('ProductPage', () => {
  const mockProduct = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Test Product',
    slug: 'test-product',
    description: 'Test product description',
    images: ['image1.jpg'],
    brand: { id: 'brand1', name: 'Test Brand', slug: 'test-brand', logoUrl: 'logo.jpg', description: '' },
    retailerOffers: [],
    status: 'active',
    isFeatured: false,
    isSponsored: false,
    cashbackAmount: 0,
    modelNumber: '',
    specifications: null,
    createdAt: '',
    updatedAt: '',
    category: null,
    promotion: null,
    minPrice: 0,
  };

  const mockSimilarProducts = [
    {
      id: '111',
      name: 'Similar Product 1',
      slug: 'similar-product-1',
      description: '',
      images: [],
      brand: null,
      retailerOffers: [],
      status: 'active',
      isFeatured: false,
      isSponsored: false,
      cashbackAmount: 0,
      modelNumber: '',
      specifications: null,
      createdAt: '',
      updatedAt: '',
      category: null,
      promotion: null,
      minPrice: 0,
    },
    {
      id: '222',
      name: 'Similar Product 2',
      slug: 'similar-product-2',
      description: '',
      images: [],
      brand: { id: 'brand2', name: '', slug: '', logoUrl: null, description: null },
      retailerOffers: [],
      status: 'active',
      isFeatured: false,
      isSponsored: false,
      cashbackAmount: 0,
      modelNumber: '',
      specifications: null,
      createdAt: '',
      updatedAt: '',
      category: null,
      promotion: null,
      minPrice: 0,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders product page with product and similar products for UUID', async () => {
    jest.spyOn(dataLayer, 'getProductPageData').mockResolvedValue({
      product: mockProduct,
      similarProducts: mockSimilarProducts,
    });

    const params = Promise.resolve({ id: mockProduct.id });

    render(<ProductPage params={params} />);

    await waitFor(() => {
      expect(screen.getByText('ProductStructuredData Component')).toBeInTheDocument();
      expect(screen.getByText('ProductPageClient Component')).toBeInTheDocument();
    });

    expect(dataLayer.getProductPageData).toHaveBeenCalledWith(mockProduct.id);
  });

  it('renders product page with product and similar products for slug', async () => {
    jest.spyOn(dataLayer, 'getProductPageData').mockResolvedValue({
      product: mockProduct,
      similarProducts: mockSimilarProducts,
    });

    const params = Promise.resolve({ id: mockProduct.slug });

    render(<ProductPage params={params} />);

    await waitFor(() => {
      expect(screen.getByText('ProductStructuredData Component')).toBeInTheDocument();
      expect(screen.getByText('ProductPageClient Component')).toBeInTheDocument();
    });

    expect(dataLayer.getProductPageData).toHaveBeenCalledWith(mockProduct.slug);
  });

  it('calls notFound when product is not found', async () => {
    jest.spyOn(dataLayer, 'getProductPageData').mockResolvedValue(null);

    const params = Promise.resolve({ id: 'non-existent-id' });

    render(<ProductPage params={params} />);

    await waitFor(() => {
      expect(notFound).toHaveBeenCalled();
    });
  });
});
