-Keep your solutions simple, do not overcomplicate your approach and/or solutions.
-Stick to the current scope of the prompt from the user, do not expand beyond what the user's prompt has asked you to do. DO NOT INTRODUCE SCOPE CREEP WIHTOUT PERMISSION.
- DO not go looking for any other impacted pages without my confirmation.

-Think through step by step to analyse any issues before starting work.
-If you have additional suggestions, include them in the final summary under "Suggested Next Steps".
-Once you have resolved some Typescript errors, ask me to check before continuing to make more edits and completing it all.
-Look for the simplest solution first before trying more complex solutions.
-Keep approaches consistent across the code base.
-break down work into small chunks and then let me validate and test specific functionality before going on to progress with further changes.
-before removing any functionality, ask me to confirm if that what i want.

## Technical Requirements

### Frontend
- **Framework:** Next.js with TypeScript
- **Styling:** Tailwind CSS
- **Components:** Custom UI components and shadcn/ui
- **State Management:** Zustand (or alternative lightweight state manager)
- **SEO Optimization:** Server-Side Rendering (SSR) and Static Site Generation (SSG)

### Backend
- **Database:** Supabase (PostgreSQL)
- **Authentication:** Supabase Auth
- **API Integration:** REST API endpoints for product data, reminders, and user profiles
- **Security:** Row-Level Security (RLS) for user data protection

### Performance & Security
- Page load time < 3 seconds
- Time to interactive < 4 seconds
- GDPR compliance and secure data handling
- Encrypted user data storage


## Expanded Non-Functional Requirements

### 1. Performance
- **Page Load Time**: Aim for all main pages (e.g., homepage, product listing pages) to load in under **1 second** on average broadband connections.
- **Time to Interactive (TTI)**: Target **< 1.5 seconds** for primary user flows (e.g., searching for offers, opening product details).
- **Scalability & Concurrency**:
  - The system should handle **up to 10000 concurrent users** without significant performance degradation.
  - Implement caching (at the CDN or application layer) to optimize repeated requests (e.g., frequently accessed product or brand data).
- Currently out of scope: [**Monitoring & Logging** tools (e.g., Google Analytics, Datadog, or similar) are essential to track performance metrics and quickly respond to issues.]


### 2. Reliability & Uptime
- **Uptime Goal**: Strive for a **99.9% uptime** SLA (excluding planned maintenance) for critical services (product listing, user authentication, reminder notifications).
- Currently out of scope: [ **Redundancy & Backups**:
  - Maintain regular database backups (e.g., daily, with weekly offsite archiving) to prevent data loss.
  - Have a rollback strategy in place for major releases or schema changes.]

### 3. Accessibility
- **Compliance Standards**: Follow **WCAG 2.1 AA** guidelines to ensure usability for people with disabilities.
- **Keyboard Navigation**: All interactive elements must be reachable and operable via keyboard.
- **ARIA Labels**: Proper use of ARIA attributes to facilitate screen reader navigation, especially for complex UI components (e.g., modals, dropdown filters).
- **Color Contrast**: Ensure text and interactive elements meet recommended contrast ratios (e.g., 4.5:1 for normal text).

### 4. Security
- **Encryption**:
  - Enforce **HTTPS/TLS** for all user-facing endpoints to protect data in transit.
  - Use **data encryption at rest** for sensitive user information (e.g., personal details, login credentials).
- **Authentication & Authorization**:
  - Implement secure auth flows (e.g., Supabase Auth, OAuth) and **row-level security (RLS)** to isolate user data.
  - Use **role-based access control** for any administrative tools or data management features.
- **Vulnerability Management**:
  - Regularly run automated vulnerability scans or code audits (e.g., using GitHub Dependabot, OWASP ZAP).
  - Have an incident response plan outlining how to handle and communicate security breaches.
- **Privacy**:
  - Comply with **GDPR** / relevant data protection regulations (e.g., provide a way for users to request data deletion, manage cookies properly, etc.).

currently out of scope: [### 5. Data Retention & Compliance
- **User Data Retention**:
  - Define how long you store user data (e.g., 12 months for inactive accounts) before archiving or deleting.
  - Ensure compliance with **local data protection laws** regarding data retention and disposal.
- **Audit Logs**:
  - Keep records of user actions (e.g., login, reminder setup) for **X days** to aid in troubleshooting and compliance.
- **Consent Management**:
  - Provide clear consent notices for data collection (e.g., cookies for analytics, email marketing).
  - Allow users to update their preferences or opt out of newsletters and reminders.]

