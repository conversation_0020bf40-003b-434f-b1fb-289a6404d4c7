#!/usr/bin/env node

const { chromium } = require('playwright');

async function debugPagination() {
  console.log('🔍 Debugging Pagination Issues...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Listen for console messages
  page.on('console', msg => {
    console.log(`🖥️  Console ${msg.type()}: ${msg.text()}`);
  });
  
  // Listen for errors
  page.on('pageerror', error => {
    console.log(`❌ Page Error: ${error.message}`);
  });
  
  try {
    console.log('📋 Loading products page...');
    await page.goto('http://localhost:3000/products');
    await page.waitForLoadState('networkidle');
    
    // Check if pagination buttons exist
    const paginationButtons = await page.locator('button[aria-label*="page"]').count();
    console.log(`✓ Found ${paginationButtons} pagination buttons`);
    
    // Check if page 2 button exists
    const page2Button = page.locator('button[aria-label="Go to page 2"]');
    const page2Exists = await page2Button.count() > 0;
    console.log(`✓ Page 2 button exists: ${page2Exists}`);
    
    if (page2Exists) {
      console.log('📋 Clicking page 2 button...');
      
      // Take screenshot before click
      await page.screenshot({ path: 'before-click.png' });
      
      // Click the button
      await page2Button.click();
      
      // Wait a moment
      await page.waitForTimeout(2000);
      
      // Take screenshot after click
      await page.screenshot({ path: 'after-click.png' });
      
      // Check URL
      const url = page.url();
      console.log(`✓ URL after click: ${url}`);
      
      // Check active page
      const activePage = await page.locator('button[aria-current="page"]').textContent();
      console.log(`✓ Active page after click: ${activePage}`);
      
      // Check if any network requests were made
      console.log('📋 Checking network activity...');
      
      // Wait for any potential network requests
      await page.waitForTimeout(3000);
      
      console.log('✓ Debug complete');
    } else {
      console.log('❌ Page 2 button not found');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
  }
}

debugPagination().catch(console.error);
