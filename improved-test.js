// improved-test.js
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Manually load .env.local file
function loadEnv() {
  const envPath = path.resolve(process.cwd(), '.env.local');
  try {
    const content = fs.readFileSync(envPath, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach(line => {
      const match = line.match(/^([^=]+)=(.*)$/);
      if (match) {
        const key = match[1].trim();
        const value = match[2].trim();
        process.env[key] = value;
      }
    });
    
    console.log('Loaded environment variables from .env.local');
  } catch (err) {
    console.error('Error loading .env.local:', err);
  }
}

// Load env variables
loadEnv();

// Now test Supabase connection
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('SUPABASE_URL exists:', !!SUPABASE_URL);
console.log('SUPABASE_ANON_KEY exists:', !!SUPABASE_ANON_KEY);

async function testConnection() {
  if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    console.error('Missing Supabase credentials');
    return;
  }
  
  try {
    console.log('Attempting to connect to Supabase...');
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    console.log('Fetching products...');
    const { data: products, error } = await supabase
      .from('products')
      .select('id, slug')
      .limit(5);
    
    if (error) {
      console.error('Error fetching products:', error);
    } else {
      console.log('Successfully fetched products!');
      console.log(`Found ${products.length} products`);
      console.log('First few products:', products);
    }
  } catch (err) {
    console.error('Exception during Supabase connection:', err);
  }
}

testConnection();