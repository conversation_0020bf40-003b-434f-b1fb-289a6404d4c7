# UX Mapping Documentation - Cashback Deals Platform

## Overview
This document provides comprehensive UX mapping for the Cashback Deals platform, detailing user journeys, page structures, data sources, and interaction patterns. Created from deep analysis of the UI layer implementation and data flow architecture.

## Table of Contents
1. [User Journey Map](#user-journey-map)
2. [Page Structure & Components](#page-structure--components)
3. [Data Flow Mapping](#data-flow-mapping)
4. [Interaction Patterns](#interaction-patterns)
5. [SEO & Performance Considerations](#seo--performance-considerations)
6. [Component Hierarchy](#component-hierarchy)

---

## User Journey Map

### Primary User Flow: Product Discovery to Purchase
```
Homepage → Browse Products → Product Details → External Retailer → Cashback Claim
    ↓           ↓              ↓                ↓                  ↓
  Hero CTA   Pagination    Specifications   Price Comparison   Promotion Terms
  Featured   Filtering     Image Gallery    Retailer Links     Cashback Amount
  Brands     Search        Similar Items    Stock Status       Claim Process
```

### Secondary User Flows
1. **Brand-Focused Discovery**: Homepage → Brands → Brand Detail → Products → Product Detail
2. **Search-Driven Discovery**: Any Page → Search → Results → Product Detail
3. **Promotion-Driven Discovery**: Homepage → Promotions → Filtered Products → Product Detail

---

## Page Structure & Components

### 1. Homepage (`/`)
**Purpose**: Entry point, brand showcase, featured products
**Key Components**:
- Hero section with primary CTA
- Featured brands grid
- Featured products carousel
- Promotion highlights
- Search functionality

**Data Sources**:
- `/api/brands` - Featured brands with logos
- `/api/products?featured=true` - Featured products
- `/api/promotions` - Active promotions

**UX Elements**:
- Visual hierarchy: Hero → Brands → Products
- Clear navigation paths to all major sections
- Responsive grid layouts
- Loading states for dynamic content

### 2. Products Listing (`/products`)
**Purpose**: Product discovery, filtering, pagination
**Key Components**:
- `ProductsContent` - Main container
- `FilterMenu` - Brand/promotion/price filtering
- `ProductCard` - Individual product display
- `Pagination` - Page navigation controls
- `PaginationInfo` - Results summary

**Data Sources**:
- `/api/products?page={n}&brandId={id}&promotion_id={id}&minPrice={n}&maxPrice={n}`

**UX Elements**:
- **ProductCard Layout** (200px height):
  - Product image with cashback badge
  - Brand name + product name
  - **Prominent price display** (£799.00)
  - Cashback amount (Up to £X.XX)
  - Retailer count (when available)
- Filter sidebar with real-time updates
- Traditional pagination (replaced infinite scroll)
- Responsive grid: 1-2-3-4 columns based on screen size

### 3. Product Detail (`/products/[slug]`)
**Purpose**: Comprehensive product information, purchase decision
**Key Components**:
- `ProductInfo` - Main product display
- `ProductDetailsSection` - Technical specifications
- `PriceComparison` - Retailer offers
- `SimilarProducts` - Recommendations

**Data Sources**:
- `/api/products/[slug]` - Complete product data with specifications

**UX Elements**:
- **ProductInfo Section**:
  - Enhanced image gallery (multiple sources)
  - Product name and brand
  - Cashback promotion details
  - Promotion countdown timers
- **ProductDetailsSection**:
  - Expandable specification categories
  - Technical details, features, dimensions
  - Warranty and energy ratings
- **PriceComparison**:
  - Retailer logos and names
  - Price comparison table
  - Stock status indicators
  - Direct purchase links

### 4. Brands Listing (`/brands`)
**Purpose**: Brand discovery and exploration
**Key Components**:
- Brand grid with logos
- Brand cards with product counts
- Search and filtering

**Data Sources**:
- `/api/brands` - All brands with metadata

**UX Elements**:
- **Brand Cards**:
  - High-quality brand logos (logoUrl)
  - Brand names and descriptions
  - Product count indicators
- Grid layout with consistent spacing
- Hover effects and transitions

### 5. Brand Detail (`/brands/[slug]`)
**Purpose**: Brand-specific product discovery, promotions
**Key Components**:
- Brand header with logo and description
- Active promotions display
- Featured products from brand
- Category breakdown

**Data Sources**:
- `/api/brands/[slug]` - Brand details with products and promotions

**UX Elements**:
- **Brand Header**:
  - Large brand logo display
  - Brand description and details
- **Promotions Section**:
  - Active promotions with end dates
  - Cashback amounts and terms
  - Promotion countdown timers
- **Products Grid**:
  - Brand-specific product cards
  - Category filtering
  - Direct links to product details

---

## Data Flow Mapping

### API to Component Data Flow
```
Database (Supabase) → Data Layer (camelCase transform) → API Routes → Components
```

### Key Data Transformations
1. **Snake_case to camelCase**: All database fields transformed for frontend consistency
2. **Price Calculation**: Specifications price parsed to minPrice (£799.00 → 799)
3. **Image Enhancement**: Multiple image sources combined (Supabase + specifications)
4. **Promotion Processing**: Date calculations for countdown timers

### Caching Strategy
- **Short-term**: Products API (30 minutes)
- **Medium-term**: Brands API (1 hour)
- **Long-term**: Static content (24 hours)

---

## Interaction Patterns

### Navigation Patterns
1. **Breadcrumb Navigation**: Product Detail → Back to Products
2. **Cross-linking**: Similar Products, Brand Links, Category Links
3. **Search Integration**: Global search with results filtering

### User Interactions
1. **Product Discovery**:
   - Filter by brand, price range, promotions
   - Pagination navigation
   - Search functionality

2. **Product Exploration**:
   - Image gallery navigation
   - Specification section expansion
   - Price comparison review

3. **Purchase Journey**:
   - External retailer link clicks
   - Cashback promotion review
   - Terms and conditions access

### Responsive Behavior
- **Mobile**: Single column, touch-friendly controls
- **Tablet**: 2-3 column grids, optimized spacing
- **Desktop**: 4 column grids, hover interactions

---

## SEO & Performance Considerations

### SEO Optimization
1. **Structured Data**: Product schema markup for rich snippets
2. **Meta Tags**: Dynamic titles and descriptions per product
3. **URL Structure**: SEO-friendly slugs for products and brands
4. **Image Optimization**: Alt tags, lazy loading, multiple formats

### Performance Features
1. **Image Loading**: Priority loading for above-fold content
2. **Code Splitting**: Component-level code splitting
3. **Caching**: Multi-level caching strategy
4. **Lazy Loading**: Below-fold content lazy loaded

### Core Web Vitals
- **LCP**: Optimized with image preloading and caching
- **FID**: Minimal JavaScript for initial interactions
- **CLS**: Consistent layouts with proper sizing

---

## Component Hierarchy

### Reusable Components
1. **ProductCard**: Used in products listing, similar products, brand products
2. **Pagination**: Used across all listing pages
3. **FilterMenu**: Reusable filtering interface
4. **ImageGallery**: Available for enhanced image display

### Page-Specific Components
1. **ProductInfo**: Product detail page main component
2. **ProductDetailsSection**: Technical specifications display
3. **PriceComparison**: Retailer comparison table
4. **SimilarProducts**: Product recommendations

### Layout Components
1. **Navigation**: Global navigation header
2. **Footer**: Site-wide footer with links
3. **Container**: Responsive page containers

---

## Technical Implementation Notes

### Data Consistency
- All API responses use camelCase field naming
- Consistent interface definitions across components
- Type safety with TypeScript interfaces

### Error Handling
- Graceful fallbacks for missing images
- "Price not available" for missing pricing
- Loading states for all dynamic content

### Accessibility
- Proper ARIA labels for interactive elements
- Keyboard navigation support
- Screen reader compatible structure

---

---

## Detailed Component Specifications

### ProductCard Component Analysis
**File**: `src/components/ProductCard.tsx`
**Dimensions**: 200px height (enhanced from 160px)
**Layout Structure**:
```
┌─────────────────────────────────┐
│ Image Area (aspect-square)      │
│ ┌─────────────┐ ┌─────────────┐ │
│ │ Cashback    │ │ Promotion   │ │
│ │ Badge (TR)  │ │ Timer (BL)  │ │
│ └─────────────┘ └─────────────┘ │
├─────────────────────────────────┤
│ Content Area (200px)            │
│ • Brand - Product Name          │
│ • Price (£XXX.XX) - Prominent   │
│ • Cashback (Up to £X.XX)        │
│ • Retailer Count (if available) │
└─────────────────────────────────┘
```

**Data Dependencies**:
- `product.minPrice` - Calculated from specifications
- `product.cashbackAmount` - From promotion data
- `product.brand.logoUrl` - Fallback image source
- `product.specifications.product_image_1-4` - Primary images

### Product Detail Page Layout
**File**: `src/app/products/[id]/page.tsx`
**Layout Flow**:
```
┌─────────────────────────────────────────────────────┐
│ Breadcrumb: Back to Products                        │
├─────────────────────────────────────────────────────┤
│ ProductInfo Component                               │
│ ┌─────────────────┐ ┌─────────────────────────────┐ │
│ │ Image Gallery   │ │ Product Details             │ │
│ │ • Main Image    │ │ • Name & Brand              │ │
│ │ • Thumbnails    │ │ • Cashback Badge            │ │
│ │ • Navigation    │ │ • Promotion Details         │ │
│ │ • Cashback      │ │ • Terms & Conditions       │ │
│ └─────────────────┘ └─────────────────────────────┘ │
├─────────────────────────────────────────────────────┤
│ ProductDetailsSection Component                     │
│ • Expandable Specification Categories               │
│ • Technical Details, Features, Dimensions           │
├─────────────────────────────────────────────────────┤
│ PriceComparison Component (if retailer offers)     │
│ • Retailer logos and names                          │
│ • Price comparison table                            │
│ • Stock status indicators                           │
├─────────────────────────────────────────────────────┤
│ SimilarProducts Component                           │
│ • Category-based recommendations                    │
│ • ProductCard grid layout                           │
└─────────────────────────────────────────────────────┘
```

---

## User Experience Patterns

### Visual Hierarchy Principles
1. **Price as Primary Element**: Large, bold pricing on product cards
2. **Cashback Prominence**: Bright badges and clear cashback amounts
3. **Brand Recognition**: Consistent logo placement and sizing
4. **Action-Oriented**: Clear CTAs for external retailer links

### Information Architecture
```
Global Navigation
├── Products (Main Category)
│   ├── All Products (Paginated)
│   ├── By Brand (Filtered)
│   ├── By Promotion (Filtered)
│   └── By Price Range (Filtered)
├── Brands (Secondary Category)
│   ├── All Brands (Grid)
│   └── Individual Brand Pages
│       ├── Brand Information
│       ├── Active Promotions
│       └── Brand Products
└── Search (Utility)
    ├── Product Search Results
    ├── Brand Search Results
    └── Filtered Results
```

### Interaction Feedback
1. **Loading States**: Skeleton screens and spinners
2. **Hover Effects**: Card elevation and transitions
3. **Error States**: Graceful fallbacks with user-friendly messages
4. **Success States**: Clear confirmation for actions

---

## Data Source Mapping

### API Endpoint Usage
| Page/Component | Primary Endpoint | Secondary Endpoints | Cache Duration |
|----------------|------------------|-------------------|----------------|
| Products Listing | `/api/products` | `/api/brands` (filters) | 30 minutes |
| Product Detail | `/api/products/[slug]` | None | 30 minutes |
| Brands Listing | `/api/brands` | None | 1 hour |
| Brand Detail | `/api/brands/[slug]` | None | 1 hour |
| Search Results | `/api/search` | None | 15 minutes |

### Data Transformation Pipeline
```
Raw Database Data (snake_case)
    ↓
Data Layer Transformation (camelCase)
    ↓
API Response (TransformedProduct/TransformedBrand)
    ↓
Component Props (TypeScript Interfaces)
    ↓
UI Rendering (React Components)
```

### Critical Data Fields
**Product Data**:
- `minPrice` - Calculated from specifications.price
- `cashbackAmount` - From promotion relationship
- `images` - Combined from product.images + specifications.product_image_*
- `specifications` - Rich technical data object

**Brand Data**:
- `logoUrl` - High-quality brand logos
- `activePromotions` - Current promotional offers
- `featuredProducts` - Brand-specific product showcase

---

## Performance & SEO Optimization

### Image Optimization Strategy
1. **Multiple Sources**: Supabase storage + external URLs (Samsung, etc.)
2. **Lazy Loading**: Below-fold images loaded on demand
3. **Fallback Chain**: Product images → Brand logo → Placeholder
4. **Responsive Images**: Appropriate sizing for device/viewport

### SEO Implementation
1. **Dynamic Meta Tags**: Product-specific titles and descriptions
2. **Structured Data**: JSON-LD schema for products and brands
3. **URL Structure**: `/products/[seo-friendly-slug]`
4. **Internal Linking**: Cross-links between products, brands, categories

### Core Web Vitals Optimization
- **LCP**: Image preloading for above-fold content
- **FID**: Minimal JavaScript for initial page interactions
- **CLS**: Consistent layouts with proper image dimensions

---

## Accessibility & Usability

### Accessibility Features
1. **Keyboard Navigation**: Full keyboard support for all interactions
2. **Screen Reader Support**: Proper ARIA labels and semantic HTML
3. **Color Contrast**: WCAG AA compliant color schemes
4. **Focus Management**: Clear focus indicators and logical tab order

### Mobile UX Considerations
1. **Touch Targets**: Minimum 44px touch targets
2. **Thumb-Friendly**: Important actions within thumb reach
3. **Responsive Typography**: Scalable text for readability
4. **Simplified Navigation**: Collapsed menus and streamlined flows

---

## Future UX Enhancement Opportunities

### Identified Improvement Areas
1. **Wishlist Functionality**: Save products for later comparison
2. **Advanced Filtering**: More granular product filtering options
3. **Comparison Tool**: Side-by-side product comparison
4. **Personalization**: User-specific recommendations
5. **Enhanced Search**: Auto-complete and search suggestions

### Analytics Integration Points
1. **Product View Tracking**: Monitor popular products and categories
2. **Conversion Funnel**: Track from discovery to external retailer click
3. **Search Analytics**: Monitor search terms and result effectiveness
4. **User Journey Analysis**: Understand common navigation patterns

---

*This comprehensive UX mapping documentation reflects the current state of the UI implementation as of Epic CAS-1 completion (v10.0.3). All components are production-ready with comprehensive testing, consistent data handling, and optimized user experience patterns.*
