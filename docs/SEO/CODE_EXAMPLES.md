# SEO Optimization Code Examples

## Homepage Conversion Example

### Before: Client-Side Rendering
```typescript
// src/app/page.tsx (CURRENT - CLIENT-SIDE)
'use client'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'

export default function HomePage() {
  const { data: featuredProducts, isLoading } = useQuery({
    queryKey: ['featured-products'],
    queryFn: () => fetch('/api/products/featured').then(res => res.json())
  })

  if (isLoading) return <div>Loading...</div>

  return (
    <motion.div>
      <h1>Welcome to CashbackDeals</h1>
      {featuredProducts?.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </motion.div>
  )
}
```

### After: Server-Side Rendering with Client Enhancement
```typescript
// src/app/page.tsx (TARGET - SERVER COMPONENT)
import { Suspense } from 'react'
import { constructMetadata } from '@/lib/metadata-utils'
import { getFeaturedProducts } from '@/lib/data/products'
import { HomePageClient } from '@/components/pages/HomePageClient'
import { HomePageSkeleton } from '@/components/ui/HomePageSkeleton'
import { WebsiteStructuredData } from '@/components/seo/StructuredData'

export const metadata = constructMetadata({
  title: 'Find the Best Cashback Deals',
  description: 'Discover exclusive cashback offers and rebates from your favorite brands to save on your purchases.',
})

export default async function HomePage() {
  const featuredProducts = await getFeaturedProducts()

  return (
    <>
      <WebsiteStructuredData />
      <Suspense fallback={<HomePageSkeleton />}>
        <HomePageClient initialProducts={featuredProducts} />
      </Suspense>
    </>
  )
}
```

```typescript
// src/components/pages/HomePageClient.tsx (CLIENT COMPONENT)
'use client'
import { motion } from 'framer-motion'
import { useState } from 'react'
import { ProductCard } from '@/components/products/ProductCard'

interface HomePageClientProps {
  initialProducts: Product[]
}

export function HomePageClient({ initialProducts }: HomePageClientProps) {
  const [products] = useState(initialProducts)

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="container py-12"
    >
      <motion.h1 
        className="text-4xl font-bold text-center mb-8"
        initial={{ y: -20 }}
        animate={{ y: 0 }}
      >
        Welcome to CashbackDeals
      </motion.h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {products.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <ProductCard product={product} priority={index < 3} />
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}
```

## Product Page Conversion Example

### Before: Client-Side Rendering
```typescript
// src/app/products/[id]/page.tsx (CURRENT - CLIENT-SIDE)
'use client'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'

export default function ProductPage() {
  const params = useParams()
  const { data: product, isLoading } = useQuery({
    queryKey: ['product', params.id],
    queryFn: () => fetch(`/api/products/${params.id}`).then(res => res.json())
  })

  if (isLoading) return <div>Loading...</div>
  if (!product) return <div>Product not found</div>

  return (
    <div>
      <h1>{product.name}</h1>
      <p>{product.description}</p>
      <p>Price: £{product.price}</p>
    </div>
  )
}
```

### After: Server-Side Rendering with Dynamic Metadata
```typescript
// src/app/products/[id]/page.tsx (TARGET - SERVER COMPONENT)
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { constructMetadata } from '@/lib/metadata-utils'
import { getProduct, getSimilarProducts } from '@/lib/data/products'
import { ProductPageClient } from '@/components/products/ProductPageClient'
import { ProductStructuredData } from '@/components/seo/StructuredData'

interface ProductPageProps {
  params: { id: string }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  try {
    const product = await getProduct(params.id)
    
    return constructMetadata({
      title: `${product.name} - ${product.brand.name}`,
      description: `Get cashback on ${product.name}. ${product.description?.slice(0, 150)}...`,
      image: product.images?.[0] || product.brand?.logoUrl,
      pathname: `/products/${params.id}`,
      type: 'product'
    })
  } catch {
    return constructMetadata({
      title: 'Product Not Found',
      description: 'The product you are looking for could not be found.',
      noIndex: true
    })
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  try {
    const [product, similarProducts] = await Promise.all([
      getProduct(params.id),
      getSimilarProducts(params.id)
    ])

    return (
      <>
        <ProductStructuredData product={product} />
        <ProductPageClient 
          product={product} 
          similarProducts={similarProducts}
        />
      </>
    )
  } catch {
    notFound()
  }
}
```

```typescript
// src/lib/data/products.ts (SERVER-SIDE DATA FETCHING)
import { createServerClient } from '@/lib/supabase/server'
import { unstable_cache } from 'next/cache'

export const getProduct = unstable_cache(
  async (id: string) => {
    const supabase = createServerClient()
    
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        brands:brand_id (id, name, logo_url),
        categories:category_id (id, name),
        product_retailer_offers (
          price,
          stock_status,
          url,
          retailers:retailer_id (name, logo_url)
        )
      `)
      .eq('id', id)
      .eq('status', 'active')
      .single()

    if (error) throw new Error(`Product not found: ${error.message}`)
    return data
  },
  ['product'],
  { revalidate: 3600 } // Cache for 1 hour
)

export const getSimilarProducts = unstable_cache(
  async (productId: string) => {
    const product = await getProduct(productId)
    const supabase = createServerClient()
    
    // Note: This is raw database query - transformation happens in data layer
    const { data } = await supabase
      .from('products')
      .select('id, name, images, brands:brand_id(name)')
      .eq('category_id', product.category.id)
      .neq('id', productId)
      .eq('status', 'active')
      .limit(4)

    return data || []
  },
  ['similar-products'],
  { revalidate: 3600 }
)
```

## Structured Data Implementation

### Product Schema
```typescript
// src/components/seo/ProductStructuredData.tsx
interface ProductStructuredDataProps {
  product: Product
}

export function ProductStructuredData({ product }: ProductStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "image": product.images?.[0] || product.brand?.logoUrl,
    "brand": {
      "@type": "Brand",
      "name": product.brand.name,
      "logo": product.brand.logoUrl
    },
    "category": product.category.name,
    "offers": product.retailerOffers?.map(offer => ({
      "@type": "Offer",
      "price": offer.price,
      "priceCurrency": "GBP",
      "availability": offer.stockStatus === 'in_stock'
        ? "https://schema.org/InStock"
        : "https://schema.org/OutOfStock",
      "seller": {
        "@type": "Organization",
        "name": offer.retailer.name
      },
      "url": offer.url
    })) || [],
    "aggregateRating": product.rating ? {
      "@type": "AggregateRating",
      "ratingValue": product.rating,
      "reviewCount": product.reviewCount
    } : undefined
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
```

### Organization Schema for Brand Pages
```typescript
// src/components/seo/BrandStructuredData.tsx
interface BrandStructuredDataProps {
  brand: Brand
}

export function BrandStructuredData({ brand }: BrandStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": brand.name,
    "description": brand.description,
    "logo": brand.logoUrl,
    "url": brand.websiteUrl,
    "sameAs": [
      brand.socialMedia?.facebook,
      brand.social_media?.twitter,
      brand.social_media?.instagram
    ].filter(Boolean)
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
```

## Search Page Hybrid Implementation

### Server Component with Client Enhancement
```typescript
// src/app/search/page.tsx (HYBRID APPROACH)
import { Suspense } from 'react'
import { constructMetadata } from '@/lib/metadata-utils'
import { searchProducts } from '@/lib/data/search'
import { SearchPageClient } from '@/components/search/SearchPageClient'
import { SearchResultsSkeleton } from '@/components/ui/SearchResultsSkeleton'

interface SearchPageProps {
  searchParams: { q?: string; category?: string; brand?: string }
}

export async function generateMetadata({ searchParams }: SearchPageProps) {
  const query = searchParams.q || ''
  
  return constructMetadata({
    title: query ? `Search results for "${query}"` : 'Search Products',
    description: query 
      ? `Find the best cashback deals for "${query}". Compare prices and get cashback.`
      : 'Search for products and find the best cashback deals.',
    pathname: '/search'
  })
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const initialResults = searchParams.q 
    ? await searchProducts(searchParams)
    : { products: [], total: 0 }

  return (
    <Suspense fallback={<SearchResultsSkeleton />}>
      <SearchPageClient 
        initialResults={initialResults}
        initialParams={searchParams}
      />
    </Suspense>
  )
}
```

```typescript
// src/components/search/SearchPageClient.tsx (CLIENT COMPONENT)
'use client'
import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'

interface SearchPageClientProps {
  initialResults: SearchResults
  initialParams: SearchParams
}

export function SearchPageClient({ initialResults, initialParams }: SearchPageClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [filters, setFilters] = useState(initialParams)

  // Client-side search for real-time updates
  const { data: results = initialResults } = useQuery({
    queryKey: ['search', filters],
    queryFn: () => searchProducts(filters),
    enabled: !!filters.q,
    initialData: initialResults
  })

  const updateFilters = (newFilters: Partial<SearchParams>) => {
    const updated = { ...filters, ...newFilters }
    setFilters(updated)
    
    // Update URL
    const params = new URLSearchParams()
    Object.entries(updated).forEach(([key, value]) => {
      if (value) params.set(key, value)
    })
    router.push(`/search?${params.toString()}`)
  }

  return (
    <div className="container py-8">
      <SearchFilters filters={filters} onFiltersChange={updateFilters} />
      <SearchResults results={results} />
    </div>
  )
}
```

## Performance Optimization Examples

### Image Optimization Component
```typescript
// src/components/ui/OptimizedImage.tsx
import Image from 'next/image'
import { useState } from 'react'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  priority?: boolean
  className?: string
}

export function OptimizedImage({ 
  src, 
  alt, 
  width = 400, 
  height = 400,
  priority = false,
  className = ''
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        className={`
          duration-700 ease-in-out
          ${isLoading ? 'scale-110 blur-2xl grayscale' : 'scale-100 blur-0 grayscale-0'}
        `}
        onLoadingComplete={() => setIsLoading(false)}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
    </div>
  )
}
```

### Loading States and Suspense
```typescript
// src/app/products/[id]/loading.tsx
export default function ProductLoading() {
  return (
    <div className="container py-12">
      <div className="animate-pulse">
        {/* Breadcrumb skeleton */}
        <div className="h-4 bg-gray-200 rounded w-64 mb-8"></div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image skeleton */}
          <div className="aspect-square bg-gray-200 rounded-lg"></div>
          
          {/* Content skeleton */}
          <div className="space-y-4">
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-6 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
```

### Error Boundary Implementation
```typescript
// src/components/ui/ErrorBoundary.tsx
'use client'
import { Component, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error caught by boundary:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-4">Something went wrong</h2>
          <button 
            onClick={() => this.setState({ hasError: false })}
            className="px-4 py-2 bg-primary text-white rounded"
          >
            Try again
          </button>
        </div>
      )
    }

    return this.props.children
  }
}
```

These code examples demonstrate the transformation from client-side rendering to a hybrid approach that optimizes for SEO while maintaining interactivity and performance.
