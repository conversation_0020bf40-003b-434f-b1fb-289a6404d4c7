# Performance Optimization Guide

## Core Web Vitals Optimization

### Current Performance Issues
- **LCP (Largest Contentful Paint)**: ~4.2s ❌ (Target: <2.5s)
- **FID (First Input Delay)**: ~180ms ❌ (Target: <100ms)
- **CLS (Cumulative Layout Shift)**: ~0.15 ❌ (Target: <0.1)

### Target Performance Goals
- **LCP**: <2.5s for 75% of page loads
- **FID**: <100ms for 75% of interactions
- **CLS**: <0.1 for 75% of page loads
- **TTFB**: <800ms
- **TTI**: <3.5s

## LCP (Largest Contentful Paint) Optimization

### 1. Image Optimization
```typescript
// Optimized image component
import Image from 'next/image'

interface OptimizedImageProps {
  src: string
  alt: string
  priority?: boolean
  sizes?: string
  className?: string
}

export function OptimizedImage({ 
  src, 
  alt, 
  priority = false,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  className = ""
}: OptimizedImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      priority={priority}
      sizes={sizes}
      className={className}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      onLoad={() => {
        // Track LCP improvement
        if (priority) {
          console.log('Priority image loaded')
        }
      }}
    />
  )
}
```

### 2. Critical Resource Prioritization
```typescript
// Homepage with priority loading
export default async function HomePage() {
  const featuredProducts = await getFeaturedProducts()

  return (
    <div>
      {/* Above-the-fold content with priority */}
      <section className="hero">
        <OptimizedImage
          src="/hero-image.jpg"
          alt="CashbackDeals Hero"
          priority={true}
          sizes="100vw"
        />
      </section>
      
      {/* Featured products with priority for first 3 */}
      <section className="featured-products">
        {featuredProducts.map((product, index) => (
          <ProductCard
            key={product.id}
            product={product}
            priority={index < 3}
          />
        ))}
      </section>
    </div>
  )
}
```

### 3. Server-Side Rendering for LCP
```typescript
// Ensure critical content is server-rendered
export default async function ProductPage({ params }) {
  const product = await getProduct(params.id)
  
  return (
    <div>
      {/* Critical above-the-fold content */}
      <div className="product-hero">
        <OptimizedImage
          src={product.image_url}
          alt={product.name}
          priority={true}
        />
        <h1>{product.name}</h1>
        <p className="price">£{product.price}</p>
      </div>
      
      {/* Non-critical content can be client-side */}
      <Suspense fallback={<ProductDetailsSkeleton />}>
        <ProductDetailsClient product={product} />
      </Suspense>
    </div>
  )
}
```

## FID (First Input Delay) Optimization

### 1. Code Splitting and Lazy Loading
```typescript
// Lazy load non-critical components
import { lazy, Suspense } from 'react'

const ProductReviews = lazy(() => import('@/components/products/ProductReviews'))
const RelatedProducts = lazy(() => import('@/components/products/RelatedProducts'))

export function ProductPageClient({ product }) {
  return (
    <div>
      {/* Critical interactive elements load immediately */}
      <AddToCartButton product={product} />
      <PriceComparison offers={product.offers} />
      
      {/* Non-critical components load lazily */}
      <Suspense fallback={<ReviewsSkeleton />}>
        <ProductReviews productId={product.id} />
      </Suspense>
      
      <Suspense fallback={<RelatedProductsSkeleton />}>
        <RelatedProducts categoryId={product.category_id} />
      </Suspense>
    </div>
  )
}
```

### 2. Optimize JavaScript Execution
```typescript
// Use React.memo for expensive components
import { memo } from 'react'

export const ProductCard = memo(function ProductCard({ product, priority }) {
  return (
    <div className="product-card">
      <OptimizedImage
        src={product.image_url}
        alt={product.name}
        priority={priority}
      />
      <h3>{product.name}</h3>
      <p>£{product.price}</p>
    </div>
  )
})

// Use useMemo for expensive calculations
export function PriceComparison({ offers }) {
  const bestOffer = useMemo(() => {
    return offers.reduce((best, current) => 
      current.price < best.price ? current : best
    )
  }, [offers])

  return (
    <div>
      <p>Best Price: £{bestOffer.price}</p>
    </div>
  )
}
```

### 3. Reduce Main Thread Blocking
```typescript
// Use Web Workers for heavy computations
// utils/priceCalculator.worker.ts
self.onmessage = function(e) {
  const { products, filters } = e.data
  
  // Heavy filtering and sorting logic
  const filteredProducts = products
    .filter(product => applyFilters(product, filters))
    .sort((a, b) => a.price - b.price)
  
  self.postMessage(filteredProducts)
}

// In component
export function ProductListing({ products }) {
  const [filteredProducts, setFilteredProducts] = useState(products)
  
  const filterProducts = useCallback(async (filters) => {
    const worker = new Worker('/priceCalculator.worker.js')
    
    worker.postMessage({ products, filters })
    worker.onmessage = (e) => {
      setFilteredProducts(e.data)
      worker.terminate()
    }
  }, [products])

  return (
    <div>
      <FilterControls onFilter={filterProducts} />
      <ProductGrid products={filteredProducts} />
    </div>
  )
}
```

## CLS (Cumulative Layout Shift) Optimization

### 1. Proper Image Sizing
```typescript
// Always specify dimensions
export function ProductImage({ product }) {
  return (
    <div className="relative aspect-square">
      <Image
        src={product.image_url}
        alt={product.name}
        fill
        className="object-cover"
        sizes="(max-width: 768px) 100vw, 50vw"
      />
    </div>
  )
}
```

### 2. Loading Skeletons
```typescript
// Skeleton components with exact dimensions
export function ProductCardSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>
      <div className="h-4 bg-gray-200 rounded mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
    </div>
  )
}

// Use skeletons during loading
export function ProductGrid({ products, isLoading }) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <ProductCardSkeleton key={i} />
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
```

### 3. Font Loading Optimization
```typescript
// In layout.tsx
import { Inter } from 'next/font/google'

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap', // Prevents layout shift
  preload: true
})

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
```

## Caching Strategies

### 1. API Route Caching
```typescript
// Aggressive caching for static data
export const revalidate = 3600 // 1 hour
export const runtime = 'edge'

export async function GET() {
  const products = await getProducts()
  
  return NextResponse.json(products, {
    headers: {
      'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
      'CDN-Cache-Control': 'public, s-maxage=86400',
      'Vercel-CDN-Cache-Control': 'public, s-maxage=86400'
    }
  })
}
```

### 2. Database Query Caching
```typescript
// Use unstable_cache for expensive queries
import { unstable_cache } from 'next/cache'

export const getProductsWithOffers = unstable_cache(
  async (categoryId?: string) => {
    const supabase = createServerClient()
    
    let query = supabase
      .from('products')
      .select(`
        *,
        brands:brand_id (*),
        product_retailer_offers (
          *,
          retailers:retailer_id (*)
        )
      `)
    
    if (categoryId) {
      query = query.eq('category_id', categoryId)
    }
    
    const { data, error } = await query
    if (error) throw error
    return data
  },
  ['products-with-offers'],
  { revalidate: 1800 } // 30 minutes
)
```

### 3. Client-Side Caching
```typescript
// Optimize React Query configuration
export function Providers({ children }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        retry: (failureCount, error) => {
          if (error.status === 404) return false
          return failureCount < 3
        }
      }
    }
  }))

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
```

## Bundle Optimization

### 1. Dynamic Imports
```typescript
// Split large components
const ProductFilters = dynamic(() => import('@/components/products/ProductFilters'), {
  loading: () => <FiltersSkeleton />,
  ssr: false // Client-side only for interactivity
})

const ProductComparison = dynamic(() => import('@/components/products/ProductComparison'), {
  loading: () => <ComparisonSkeleton />
})
```

### 2. Tree Shaking
```typescript
// Import only what you need
import { debounce } from 'lodash/debounce' // ✅ Good
import _ from 'lodash' // ❌ Bad - imports entire library

// Use specific imports
import { motion } from 'framer-motion' // ✅ Good
import * as motion from 'framer-motion' // ❌ Bad - larger bundle
```

### 3. Bundle Analysis
```json
// package.json
{
  "scripts": {
    "build:analyze": "ANALYZE=true npm run build"
  }
}
```

```javascript
// next.config.js
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer({
  // Next.js config
})
```

## Performance Monitoring

### 1. Web Vitals Tracking
```typescript
// app/layout.tsx
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        {children}
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  )
}
```

### 2. Custom Performance Tracking
```typescript
// lib/performance.ts
export function trackWebVitals(metric: NextWebVitalsMetric) {
  const { name, value, id } = metric

  // Track to analytics service
  gtag('event', name, {
    event_category: 'Web Vitals',
    event_label: id,
    value: Math.round(name === 'CLS' ? value * 1000 : value),
    non_interaction: true,
  })

  // Log performance issues
  if (name === 'LCP' && value > 2500) {
    console.warn(`Poor LCP: ${value}ms`)
  }
  
  if (name === 'FID' && value > 100) {
    console.warn(`Poor FID: ${value}ms`)
  }
  
  if (name === 'CLS' && value > 0.1) {
    console.warn(`Poor CLS: ${value}`)
  }
}
```

### 3. Performance Budget
```javascript
// lighthouse.config.js
module.exports = {
  ci: {
    collect: {
      numberOfRuns: 3,
    },
    assert: {
      assertions: {
        'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        'first-input-delay': ['error', { maxNumericValue: 100 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
      },
    },
  },
}
```

## Performance Checklist

### Pre-Launch
- [ ] LCP < 2.5s on all key pages
- [ ] FID < 100ms for all interactions
- [ ] CLS < 0.1 for all pages
- [ ] Images optimized with Next.js Image
- [ ] Critical resources prioritized
- [ ] Non-critical code lazy loaded
- [ ] Bundle size optimized
- [ ] Caching strategies implemented

### Post-Launch Monitoring
- [ ] Real User Monitoring (RUM) setup
- [ ] Performance alerts configured
- [ ] Regular Lighthouse audits
- [ ] Core Web Vitals tracking
- [ ] Performance regression detection

This performance optimization guide ensures the CashbackDeals application meets modern web performance standards while maintaining excellent user experience.
