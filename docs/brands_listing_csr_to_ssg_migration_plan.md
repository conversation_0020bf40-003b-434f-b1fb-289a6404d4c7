# Brands Listing Page – CSR ➜ SSG Migration Plan

## 1  Overview
The current `/brands` route (`src/app/brands/page.tsx`) is a **client component** that fetches brand data via React Query from `/api/brands`. This document refines and finalises the plan to migrate the page to **Static Site Generation (SSG)** using the Next.js App Router.

---
## 2  Current State Snapshot
| Aspect | Details |
|--------|---------|
| Rendering | **CSR** (`'use client'` + React Query) |
| Data Source | `GET /api/brands` → `getBrands()` (server layer) |
| Freshness | API route `s‑maxage=3600` + `stale-while-revalidate=300` |
| UX | Interactive search + alphabet navigation; framer‑motion animation |
| Issues | Delayed LCP, no HTML for crawlers/JS‑off users, redundant API hop |

---
## 3  Migration Objectives
1. Serve full brand list in the first HTML payload.
2. Preserve interactive search/alphabet nav via client sub‑component.
3. Match existing freshness (revalidate every hour).
4. Reduce LCP & TTFB; improve SEO crawlability.

---
## 4  Feasibility Check ✅
| Factor | Assessment |
|--------|-----------|
| **Data volume** | OK if ≤ ~200 brands (≈5–10 kB JSON). Audit before merge. |
| **Personalisation** | None → safe for SSG. |
| **Freshness needs** | Hourly is acceptable; use `export const revalidate = 3600`. |
| **API dependencies** | None after migration (direct `getBrands()` call). |

---
## 5  File‑level Changes
| File | Action |
|------|--------|
| **`src/app/brands/page.tsx`** | ▸ Remove `'use client'`.<br>▸ Fetch brands with `getBrands()` inside the server component.<br>▸ `export const revalidate = 3600` (ISR). |
| **`src/components/brands/BrandsClient.tsx`** (🆕) | ▸ Contains search state, alphabet nav, framer‑motion effects.<br>▸ Receives `brands` prop from server. |
| **`src/lib/data/index.ts`** | Ensure `getBrands(limit?: number)` handles large limits; keep API route compatibility. |
| API route (`/api/brands`) | **No change**—kept for other consumers. |
| Misc. | Verify image domains in `next.config.js`. |

---
## 6  Implementation Sequence
1. **Spike** – add `dynamic = 'force-static'` to `brands/page.tsx`; call `getBrands(1, 1000)`; confirm local build.
2. **Refactor UI** – move interactive logic to `BrandsClient.tsx`; pass grouped brands list as prop.
3. **Payload Audit** – if HTML + JSON > 150 kB:<br>   • Consider per‑letter static routes using `generateStaticParams()`.
4. **Enable ISR** – switch from `dynamic = 'force-static'` to `revalidate = 3600`.
5. **Testing** – add Playwright & Lighthouse CI checks (`expect(page.locator('text=A')).toBeVisible()` etc.).
6. **Deploy** – monitor Core Web Vitals & Search Console coverage.

---
## 7  Edge Cases & Gotchas
* **Empty dataset** → return `<notFound />` to avoid caching error page.
* **DebugPanel** → wrap in a separate client component so it hydrates only when `isDebugEnabled()` is true.
* **Scroll offsets** – re‑check sticky header alignment post‑hydrate.
* **Image errors** – ensure placeholder logic still works in server HTML.

---
## 8  Metrics & Acceptance Criteria
| Metric | Current CSR | Target SSG |
|--------|-------------|------------|
| **FCP** | ≈ 2.0 s | ≤ 1.2 s |
| **LCP element** | After client fetch | In initial HTML |
| **CLS** | 0.05–0.1 | 0 |
| **SEO Lighthouse** | 83–85 | ≥ 95 |
| **Static HTML contains first brand** | ❌ | ✅ (test) |

All metrics are enforced via **Lighthouse CI** and **Playwright** tests in CI.

---
## 9  Next Steps Checklist
- [ ] Payload size audit (`node scripts/brand-size.js`).
- [ ] Implement server component refactor.
- [ ] Extract `BrandsClient` and move interactive logic.
- [ ] Add ISR revalidation constant.
- [ ] Write E2E & Lighthouse assertions.
- [ ] Update `CHANGELOG.md` under `### Changed`.
- [ ] Deploy to staging and verify.

---
### 📅 Timeline (draft)
| Task | Owner | ETA |
|------|-------|-----|
| Spike & audit | Dev | Day 1 |
| Refactor & tests | Dev | Day 3 |
| Review & merge | PM + QA | Day 4 |
| Staging validation | QA | Day 5 |
| Production rollout | DevOps | Day 6 |

---
## 10  References
* `src/app/brands/page.tsx` (current CSR page)
* `src/app/api/brands/route.ts` (API route)
* `src/lib/data/getBrands()` (data layer)
* Next.js docs: *Static Rendering with App Router*

