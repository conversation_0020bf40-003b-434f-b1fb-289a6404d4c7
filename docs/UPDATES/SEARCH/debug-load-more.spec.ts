import { test, expect, Page, ConsoleMessage } from '@playwright/test';

/**
 * @file This test script debugs the "Load More" functionality on the search results page.
 * It provides detailed logging of API requests, responses, and UI state to diagnose why
 * clicking "Load More" on page 2 doesn't append new results.
 * 
 * @test-case TC-SEARCH-DEBUG-01
 * @description
 * 1. Navigates to search results page with query "series"
 * 2. Verifies initial product count and UI state
 * 3. Clicks "Load More" and captures API request/response
 * 4. Verifies if new products are appended to the DOM
 * 5. Checks for console errors and React warnings
 * 6. Provides detailed debug output
 * 
 * <AUTHOR> AI
 * @date 2025-07-01
 */

test.describe('Search Page: Debug Load More', () => {
  const SEARCH_URL = 'http://localhost:3000/search?q=series';
  const API_ENDPOINT = '**/api/search*';
  
  const PRODUCT_CARD_SELECTOR = '[data-testid="product-card"]';
  const LOAD_MORE_BUTTON = 'button:has-text("Load More")';
  const RESULTS_COUNT = '[data-testid="results-count"]';
  
  let consoleMessages: string[] = [];
  let apiRequests: any[] = [];
  let apiResponses: any[] = [];

  test.beforeEach(async ({ page }) => {
    consoleMessages = [];
    apiRequests = [];
    apiResponses = [];

    // Capture console logs
    page.on('console', (msg: ConsoleMessage) => {
      const text = msg.text();
      consoleMessages.push(`[${msg.type()}] ${text}`);
    });

    // Capture API requests and responses
    await page.route(API_ENDPOINT, async (route, request) => {
      const requestData = {
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        postData: request.postData()
      };
      apiRequests.push(requestData);
      
      // Continue the request
      const response = await page.request.fetch(route.request());
      
      try {
        const responseData = await response.json();
        apiResponses.push({
          url: request.url(),
          status: response.status(),
          headers: response.headers(),
          data: responseData
        });
      } catch (e) {
        apiResponses.push({
          url: request.url(),
          status: response.status(),
          error: e.message
        });
      }
      
      await route.continue();
    });
  });

  test('debug load more functionality', async ({ page }) => {
    // 1. Navigate to search page
    console.log('Navigating to search page...');
    await page.goto(SEARCH_URL);
    
    // Wait for initial results to load
    await page.waitForSelector(PRODUCT_CARD_SELECTOR);
    
    // 2. Log initial state
    const initialProductCount = await page.locator(PRODUCT_CARD_SELECTOR).count();
    const resultsText = await page.locator(RESULTS_COUNT).textContent();
    const totalResults = resultsText ? parseInt(resultsText.match(/(\d+) results/)?.[1] || '0') : 0;
    
    console.log('Initial state:', {
      url: page.url(),
      initialProductCount,
      resultsText,
      totalResults
    });
    
    // 3. Check if Load More button is visible
    const loadMoreButton = page.locator(LOAD_MORE_BUTTON);
    const isLoadMoreVisible = await loadMoreButton.isVisible();
    console.log('Load More button visible:', isLoadMoreVisible);
    
    if (isLoadMoreVisible) {
      // 4. Enhanced button state inspection and click handling
      console.log('\n=== Button State Before Click ===');
      console.log('Button text:', await loadMoreButton.textContent());
      console.log('Is button visible:', await loadMoreButton.isVisible());
      console.log('Is button enabled:', await loadMoreButton.isEnabled());
      console.log('Button class:', await loadMoreButton.getAttribute('class'));
      
      // Get the current page URL before clicking
      const urlBeforeClick = page.url();
      console.log('URL before click:', urlBeforeClick);
      
      // Set up monitoring for page navigations and console logs
      const navigationPromise = page.waitForNavigation({ waitUntil: 'networkidle' });
      
      console.log('\n=== Network Activity Monitoring ===');
      // Log all network requests
      page.on('request', request => {
        if (request.url().includes('/api/')) {
          console.log('Request:', request.method(), request.url());
        }
      });
      
      // Log all responses
      page.on('response', response => {
        if (response.url().includes('/api/')) {
          console.log('Response:', response.status(), response.url());
        }
      });
      
      // Log all console messages
      page.on('console', msg => {
        console.log(`[CONSOLE ${msg.type()}] ${msg.text()}`);
      });
      
      console.log('\n=== Attempting to click Load More ===');
      try {
        // Click with retry logic
        await loadMoreButton.click({ timeout: 10000 });
        console.log('Successfully clicked Load More button');
        
        // Wait for any navigation
        try {
          await navigationPromise;
          console.log('Navigation completed');
        } catch (e) {
          console.log('No navigation occurred after click');
        }
        
        // Check URL after click
        const urlAfterClick = page.url();
        console.log('URL after click:', urlAfterClick);
        
        // Check if URL changed
        if (urlAfterClick !== urlBeforeClick) {
          console.log('URL changed after click');
        } else {
          console.log('URL did not change after click');
        }
        
        // Check if button is still visible/enabled
        console.log('\n=== Button State After Click ===');
        console.log('Is button still visible:', await loadMoreButton.isVisible({ timeout: 2000 }).catch(() => false));
        console.log('Is button still enabled:', await loadMoreButton.isEnabled({ timeout: 2000 }).catch(() => false));
        
        // Verify product count after navigation
        const productsAfterNavigation = await page.locator(PRODUCT_CARD_SELECTOR).count();
        console.log('Products after navigation:', productsAfterNavigation);
        
        // Check if we're still on the search results page
        const isSearchPage = page.url().includes('/search?q=');
        console.log('Still on search page:', isSearchPage);
        
        if (isSearchPage) {
          // Verify if we're seeing the second page of results or if it's still the first page
          const firstProductOnPage = await page.locator(PRODUCT_CARD_SELECTOR).first().getAttribute('data-testid');
          console.log('First product ID on page:', firstProductOnPage);
          
          // Check if the URL has a page parameter
          const url = new URL(page.url());
          const pageParam = url.searchParams.get('page');
          console.log('Current page from URL:', pageParam || '1');
        }
        
        // Check for any error messages in the UI
        const errorMessages = await page.$$eval('.error, .error-message, .alert-error', els => 
          els.map(el => el.textContent?.trim())
        );
        
        if (errorMessages.length > 0) {
          console.log('Error messages found:', errorMessages);
        }
        
      } catch (clickError) {
        console.error('Error clicking Load More button:', clickError);
        
        // Take a screenshot for debugging
        await page.screenshot({ path: 'load-more-error.png' });
        console.log('Screenshot saved as load-more-error.png');
      }  
      
      // 5. Wait for any UI updates
      await page.waitForLoadState('networkidle');
      
      // 6. Log updated state
      const updatedProductCount = await page.locator(PRODUCT_CARD_SELECTOR).count();
      console.log('\n=== After Load More ===');
      console.log('Current URL:', page.url());
      console.log('Product count:', updatedProductCount);
      console.log('Products added:', updatedProductCount - initialProductCount);
      
      // Get the page number from URL
      const currentUrl = new URL(page.url());
      const currentPage = currentUrl.searchParams.get('page') || '1';
      console.log('Current page from URL:', currentPage);
      
      // Check if we're seeing the same products or new ones
      const currentProducts = await page.locator(PRODUCT_CARD_SELECTOR).all();
      const productIds = await Promise.all(currentProducts.map(p => p.getAttribute('data-testid')));
      console.log('First 5 product IDs:', productIds.slice(0, 5));
      
      // Check if Load More button is still visible
      const loadMoreStillVisible = await loadMoreButton.isVisible().catch(() => false);
      console.log('Load More button still visible:', loadMoreStillVisible);
      
      // Log page content for debugging
      const pageContent = await page.content();
      console.log('Page contains "No more results" text:', pageContent.includes('No more results'));
      console.log('Page contains product cards:', pageContent.includes('product-card'));
      
      // 7. Verify if products were added
      if (updatedProductCount <= initialProductCount) {
        console.warn('No new products were added after clicking Load More');
      }
      
      // 8. Log API request/response details
      console.log('API Requests:', JSON.stringify(apiRequests, null, 2));
      console.log('API Responses:', JSON.stringify(apiResponses, null, 2));
      
      // 9. Check for errors in console
      const errors = consoleMessages.filter(m => m.toLowerCase().includes('error') || m.toLowerCase().includes('warning'));
      if (errors.length > 0) {
        console.warn('Console errors/warnings:', errors);
      }
      
      // 10. Take a screenshot
      await page.screenshot({ path: 'debug-load-more.png' });
      
      // 11. & 12. URL and page check are already handled in the enhanced logging above
      
      // 13. Check if Load More button is still visible
      const isLoadMoreStillVisible = await loadMoreButton.isVisible();
      console.log('Load More button still visible after click:', isLoadMoreStillVisible);
      
      // 14. Check for any error messages in the UI
      const errorMessages = await page.locator('.error-message, .alert-error, .text-red-500').allTextContents();
      if (errorMessages.length > 0) {
        console.warn('Error messages in UI:', errorMessages);
      }
      
      // 15. Product IDs already logged in the enhanced output
      
      // 16. Check for duplicate product IDs (potential React key issues)
      const uniqueIds = new Set(productIds);
      if (uniqueIds.size !== productIds.length) {
        console.warn('Duplicate product IDs found in the DOM');
      }
    } else {
      console.warn('Load More button is not visible on the page');
    }
    
    // 17. Output final debug information
    console.log('\n=== DEBUG SUMMARY ===');
    console.log(`Initial Products: ${initialProductCount}`);
    console.log(`Total Results: ${totalResults}`);
    console.log(`API Requests: ${apiRequests.length}`);
    console.log(`Console Messages: ${consoleMessages.length}`);
    console.log(`Console Errors: ${consoleMessages.filter(m => m.toLowerCase().includes('error')).length}`);
    
    // 18. If we're not seeing the expected number of products, suggest next steps
    if (initialProductCount < totalResults && !isLoadMoreVisible) {
      console.warn('\nPOTENTIAL ISSUE: Not all products are loaded and Load More button is not visible');
      console.log('\nNEXT STEPS:');
      console.log('1. Check if the API is returning the correct number of products');
      console.log('2. Verify if the Load More button is being hidden incorrectly');
      console.log('3. Check for any errors in the browser console');
      console.log('4. Verify if the total results count is accurate');
    }
    
    // 19. Save detailed logs to a file
    const fs = require('fs');
    const logData = {
      timestamp: new Date().toISOString(),
      url: page.url(),
      initialProductCount,
      totalResults,
      apiRequests,
      apiResponses: apiResponses.map(r => ({
        url: r.url,
        status: r.status,
        dataCount: r.data?.data?.length || 0
      })),
      consoleErrors: consoleMessages.filter(m => m.toLowerCase().includes('error')),
      consoleWarnings: consoleMessages.filter(m => 
        m.toLowerCase().includes('warning') && 
        !m.toLowerCase().includes('react-query') // Filter out common React Query warnings
      )
    };
    
    fs.writeFileSync('debug-load-more.json', JSON.stringify(logData, null, 2));
    console.log('\nDetailed logs saved to debug-load-more.json');
    
    // 20. Final assertion to fail the test if no products were loaded
    expect(initialProductCount).toBeGreaterThan(0);
  });
});
