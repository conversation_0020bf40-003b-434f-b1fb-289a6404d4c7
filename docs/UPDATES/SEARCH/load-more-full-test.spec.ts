import { test, expect } from '@playwright/test';

/**
 * @file This comprehensive test script verifies the "Load More" functionality on the search results page,
 * including full product loading, button visibility, scroll behavior, and console error checks.
 *
 * @test-case TC-SEARCH-02
 * @description
 * 1. Navigates to a search results page with a known total number of results (e.g., 43).
 * 2. Verifies the initial total count of items is displayed.
 * 3. Verifies the initial set of products (e.g., 20) is loaded.
 * 4. Continuously scrolls to the bottom and clicks the "Load More" button until it disappears.
 * 5. Verifies that the API calls for loading more products are successful.
 * 6. Verifies that all products (e.g., 43) are eventually loaded and appended to the list.
 * 7. Verifies the "Load More" button is hidden once all results are displayed.
 * 8. Verifies the "Back to Top" link appears after scrolling.
 * 9. Clicks the "Back to Top" link and verifies the page scrolls to the top.
 * 10. Asserts that no specific console errors (like duplicate key warnings) are present during the test.
 *
 * <AUTHOR> AI Copilot
 * @date 2025-07-01
 */

test.describe('Search Page: Full Load More & Scroll Test', () => {
  const SEARCH_URL = 'http://localhost:3000/search?q=series';
  const API_SEARCH_ENDPOINT = '**/api/search/more*'; // Specific endpoint for load more
  const EXPECTED_INITIAL_PRODUCTS = 20;
  const EXPECTED_TOTAL_PRODUCTS = 43;
  const EXPECTED_FINAL_PAGE = Math.ceil(EXPECTED_TOTAL_PRODUCTS / EXPECTED_INITIAL_PRODUCTS);

  const PRODUCT_CARD_SELECTOR = '[data-testid="product-card"]';
  const RESULTS_COUNT_SELECTOR = '[data-testid="results-count"]';
  const LOAD_MORE_BUTTON_SELECTOR = 'button:has-text("Load More")';
  const BACK_TO_TOP_SELECTOR = 'a:has-text("Back to Top")';

  test('should load all products, hide load more, and scroll to top without key errors', async ({ page }) => {
    let consoleErrors: string[] = [];

    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Step 1: Navigate to the search page.
    await page.goto(SEARCH_URL);

    // Step 2: Verify the total number of results is displayed.
    await expect(page.locator(`${RESULTS_COUNT_SELECTOR}:has-text("${EXPECTED_TOTAL_PRODUCTS} results found")`)).toBeVisible({ timeout: 10000 });

    // Step 3: Verify the initial number of products is loaded.
    await expect(page.locator(PRODUCT_CARD_SELECTOR)).toHaveCount(EXPECTED_INITIAL_PRODUCTS);

    // Step 4: Continuously load more products until the button is hidden.
    let currentPage = 1;
    while (currentPage < EXPECTED_FINAL_PAGE) {
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      const loadMoreButton = page.locator(LOAD_MORE_BUTTON_SELECTOR);

      // Ensure button is visible before clicking
      await expect(loadMoreButton).toBeVisible();

      const responsePromise = page.waitForResponse(API_SEARCH_ENDPOINT);
      await loadMoreButton.click();

      const response = await responsePromise;
      expect(response.ok(), `API call to ${API_SEARCH_ENDPOINT} failed with status ${response.status()}`).toBeTruthy();

      currentPage++;
      // Calculate expected count after this load
      const expectedCountAfterLoad = Math.min(EXPECTED_TOTAL_PRODUCTS, EXPECTED_INITIAL_PRODUCTS * currentPage);
      await expect(page.locator(PRODUCT_CARD_SELECTOR)).toHaveCount(expectedCountAfterLoad, { timeout: 10000 });
    }

    // Step 5: Verify all products are loaded.
    await expect(page.locator(PRODUCT_CARD_SELECTOR)).toHaveCount(EXPECTED_TOTAL_PRODUCTS);

    // Step 6: Verify the "Load More" button is now hidden.
    await expect(page.locator(LOAD_MORE_BUTTON_SELECTOR)).toBeHidden();

    // Step 7: Verify the "Back to Top" link is now visible.
    const backToTopLink = page.locator(BACK_TO_TOP_SELECTOR);
    await expect(backToTopLink).toBeVisible();

    // Step 8: Click the "Back to Top" link.
    await backToTopLink.click();

    // Step 9: Verify the page has scrolled back to the top.
    await expect.poll(async () => {
      return page.evaluate(() => window.scrollY);
    }, {
      message: 'Page did not scroll to the top after clicking "Back to Top" link.',
      timeout: 5000
    }).toBe(0);

    // Step 10: Check for specific console errors (e.g., duplicate key warnings).
    const duplicateKeyError = consoleErrors.find(error =>
      error.includes('Encountered two children with the same key')
    );
    expect(duplicateKeyError).toBeUndefined();
  });
});