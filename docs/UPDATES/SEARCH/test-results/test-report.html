
  <!DOCTYPE html>
  <html>
  <head>
    <title>Search Functionality Test Report</title>
    <style>
      body { font-family: Arial, sans-serif; margin: 20px; }
      h1 { color: #333; }
      .summary { 
        background-color: #f5f5f5; 
        padding: 15px; 
        border-radius: 5px; 
        margin-bottom: 20px;
      }
      .pass { color: green; }
      .fail { color: red; }
      table { 
        width: 100%; 
        border-collapse: collapse; 
        margin-top: 20px;
      }
      th, td { 
        border: 1px solid #ddd; 
        padding: 8px; 
        text-align: left; 
      }
      th { 
        background-color: #f2f2f2; 
      }
      tr:nth-child(even) { 
        background-color: #f9f9f9; 
      }
      .screenshots { margin-top: 20px; }
      .screenshot { 
        margin: 10px 0; 
        border: 1px solid #ddd;
        padding: 10px;
      }
      .screenshot img { 
        max-width: 100%; 
        height: auto;
        border: 1px solid #eee;
      }
    </style>
  </head>
  <body>
    <h1>Search Functionality Test Report</h1>
    <div class="summary">
      <h2>Test Summary</h2>
      <p>Total Tests: 1</p>
      <p class="pass">Passed: 0</p>
      <p class="fail">Failed: 1</p>
      <p>Pass Rate: 0.00%</p>
      <p>Generated at: 2025-07-01T22:56:11.733Z</p>
    </div>

    <h2>Test Results</h2>
    <table>
      <tr>
        <th>Test Name</th>
        <th>Query</th>
        <th>Expected</th>
        <th>Actual</th>
        <th>Status</th>
        <th>Duration</th>
      </tr>
      
        <tr>
          <td>should return 42 results for query "series"</td>
          <td>series</td>
          <td>42</td>
          <td>0</td>
          <td class="fail">
            FAIL
          </td>
          <td>14761ms</td>
        </tr>
      
    </table>

    
      <div class="test-detail">
        <h3>should return 42 results for query "series"</h3>
        <p><strong>Status:</strong> <span class="fail">
          FAILED
        </span></p>
        <p><strong>Query:</strong> series</p>
        <p><strong>Expected Results:</strong> 42</p>
        <p><strong>Actual Results:</strong> 0</p>
        <p><strong>Duration:</strong> 14761ms</p>
        
        
          <div class="errors">
            <h4>Errors:</h4>
            <pre>[
  {
    "type": "pagination",
    "message": "page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('div.grid > div > div > div[data-testid^=\"product-\"], div.grid > div > div > .product-card') to be visible\u001b[22m\n",
    "stack": "page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('div.grid > div > div > div[data-testid^=\"product-\"], div.grid > div > div > .product-card') to be visible\u001b[22m\n\n    at /Users/<USER>/cashback-deals-v2 copy/docs/UPDATES/SEARCH/search-functionality.spec.ts:198:24",
    "page": 1,
    "timestamp": "2025-07-01T22:56:09.904Z"
  },
  {
    "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m42\u001b[39m\nReceived: \u001b[31m0\u001b[39m",
    "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m42\u001b[39m\nReceived: \u001b[31m0\u001b[39m\n    at /Users/<USER>/cashback-deals-v2 copy/docs/UPDATES/SEARCH/search-functionality.spec.ts:325:30",
    "screenshots": [
      "screenshots/series-initial-2025-07-01T22-55-59-224Z.png",
      "screenshots/series-pagination-error-2025-07-01T22-56-09-560Z.png",
      "screenshots/series-final-2025-07-01T22-56-09-904Z.png"
    ]
  }
]</pre>
          </div>
        

        
          <div class="screenshots">
            <h4>Screenshots:</h4>
            
              <div class="screenshot">
                <img src="../../../../screenshots/series-initial-2025-07-01T22-55-59-224Z.png" alt="Screenshot" />
                <p>series-initial-2025-07-01T22-55-59-224Z.png</p>
              </div>
            
              <div class="screenshot">
                <img src="../../../../screenshots/series-pagination-error-2025-07-01T22-56-09-560Z.png" alt="Screenshot" />
                <p>series-pagination-error-2025-07-01T22-56-09-560Z.png</p>
              </div>
            
              <div class="screenshot">
                <img src="../../../../screenshots/series-final-2025-07-01T22-56-09-904Z.png" alt="Screenshot" />
                <p>series-final-2025-07-01T22-56-09-904Z.png</p>
              </div>
            
          </div>
        
      </div>
    
  </body>
  </html>
  