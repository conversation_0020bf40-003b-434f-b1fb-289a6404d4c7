[{"testName": "should return 42 results for query \"series\"", "query": "series", "expectedCount": 42, "actualCount": 0, "passed": false, "duration": 14761, "timestamp": "2025-07-01T22:55:55.143Z", "screenshots": ["screenshots/series-initial-2025-07-01T22-55-59-224Z.png", "screenshots/series-pagination-error-2025-07-01T22-56-09-560Z.png", "screenshots/series-final-2025-07-01T22-56-09-904Z.png"], "errors": [{"type": "pagination", "message": "page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('div.grid > div > div > div[data-testid^=\"product-\"], div.grid > div > div > .product-card') to be visible\u001b[22m\n", "stack": "page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('div.grid > div > div > div[data-testid^=\"product-\"], div.grid > div > div > .product-card') to be visible\u001b[22m\n\n    at /Users/<USER>/cashback-deals-v2 copy/docs/UPDATES/SEARCH/search-functionality.spec.ts:198:24", "page": 1, "timestamp": "2025-07-01T22:56:09.904Z"}, {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m42\u001b[39m\nReceived: \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m42\u001b[39m\nReceived: \u001b[31m0\u001b[39m\n    at /Users/<USER>/cashback-deals-v2 copy/docs/UPDATES/SEARCH/search-functionality.spec.ts:325:30", "screenshots": ["screenshots/series-initial-2025-07-01T22-55-59-224Z.png", "screenshots/series-pagination-error-2025-07-01T22-56-09-560Z.png", "screenshots/series-final-2025-07-01T22-56-09-904Z.png"]}]}]