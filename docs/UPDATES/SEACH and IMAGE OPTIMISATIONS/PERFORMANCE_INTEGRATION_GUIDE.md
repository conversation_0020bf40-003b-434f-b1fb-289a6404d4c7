# Performance & Resilience Integration Guide

## Quick Start

This guide provides step-by-step instructions for integrating the performance and resilience utilities into your components and features.

## 1. Image Loading Integration

### Replace Standard Image Components

**Before** (Standard Next.js Image):
```typescript
import Image from 'next/image';

<Image
  src={product.imageUrl}
  alt={product.name}
  width={600}
  height={600}
  onError={() => setImageError(true)}
/>
```

**After** (Resilient Image):
```typescript
import { ResilientImage } from '@/components/ui/ResilientImage';

<ResilientImage
  src={product.imageUrl}
  alt={product.name}
  width={600}
  height={600}
  productName={product.name}
  brandName={product.brand?.name}
  enableValidation={true}
  showLoadingState={true}
  retryOnError={true}
  onError={(error) => console.warn('Image failed:', error)}
/>
```

### Benefits Gained
- ✅ Automatic Samsung image timeout handling
- ✅ Circuit breaker protection
- ✅ Intelligent fallback generation
- ✅ Performance monitoring
- ✅ Retry logic with exponential backoff

## 2. Search & API Integration

### Implement Cached Search

**Before** (Direct database query):
```typescript
export async function searchProducts(query: string, page: number) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .ilike('name', `%${query}%`)
    .range(from, to);
  
  return { products: data || [], totalCount: 0 };
}
```

**After** (Cached with monitoring):
```typescript
import { cachedSearchProducts } from '@/lib/cache/searchCache';
import { monitorQuery } from '@/lib/optimization/queryOptimizer';
import { TIMEOUT_CONFIG } from '@/lib/timeoutConfig';

async function _searchProductsInternal(query: string, page: number) {
  return await monitorQuery(
    `search-products-${query}`,
    async () => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .ilike('name', `%${query}%`)
        .range(from, to);
      
      if (error) throw error;
      return { products: data || [], totalCount: 0 };
    },
    {
      timeout: TIMEOUT_CONFIG.DATABASE.QUERY,
      logSlowQueries: true
    }
  );
}

export async function searchProducts(query: string, page: number) {
  return cachedSearchProducts(_searchProductsInternal, query, page);
}
```

### Benefits Gained
- ✅ Intelligent caching (60-80% performance improvement)
- ✅ Query performance monitoring
- ✅ Timeout protection
- ✅ Optimization recommendations

## 3. External API Integration

### Add Timeout Protection

**Before** (No timeout protection):
```typescript
const response = await fetch(externalApiUrl);
const data = await response.json();
```

**After** (With timeout and retry):
```typescript
import { fetchWithTimeout, TIMEOUT_CONFIG } from '@/lib/timeoutConfig';

try {
  const response = await fetchWithTimeout(
    externalApiUrl,
    {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    },
    TIMEOUT_CONFIG.EXTERNAL_API.GENERAL,
    2 // retry attempts
  );
  const data = await response.json();
} catch (error) {
  console.error('API call failed:', error);
  // Implement fallback logic
}
```

### Benefits Gained
- ✅ Automatic timeout handling
- ✅ Retry logic with exponential backoff
- ✅ Consistent error handling
- ✅ Environment-aware timeouts

## 4. Performance Monitoring Integration

### Add Component-Level Monitoring

```typescript
import { useImagePerformanceMonitoring } from '@/lib/monitoring/imagePerformance';

export function ProductGallery({ product }: { product: Product }) {
  const { startImageLoad, recordImageSuccess, recordImageFailure } = useImagePerformanceMonitoring();
  
  const handleImageLoad = (imageUrl: string) => {
    startImageLoad(imageUrl);
  };
  
  const handleImageSuccess = (imageUrl: string, dimensions: { width: number, height: number }) => {
    recordImageSuccess(imageUrl, dimensions);
  };
  
  const handleImageError = (imageUrl: string, error: string) => {
    recordImageFailure(imageUrl, error);
  };
  
  return (
    <div className="gallery">
      {product.images.map((imageUrl, index) => (
        <ResilientImage
          key={index}
          src={imageUrl}
          alt={`${product.name} - Image ${index + 1}`}
          onLoad={() => handleImageSuccess(imageUrl, { width: 300, height: 300 })}
          onError={(error) => handleImageError(imageUrl, error)}
        />
      ))}
    </div>
  );
}
```

## 5. Development Dashboard Usage

### Accessing Performance Metrics

1. **Start Development Server**:
   ```bash
   npm run dev
   ```

2. **Open Performance Dashboard**:
   - Press `Ctrl+Shift+I` (Windows/Linux) or `Cmd+Shift+I` (Mac)
   - Or click the "Image Performance" button in bottom-right corner

3. **Monitor Real-Time Metrics**:
   - Image loading success rates
   - Samsung server performance
   - Circuit breaker states
   - Cache hit rates
   - Performance recommendations

4. **Export Performance Data**:
   - Click "Export" button in dashboard
   - Analyze data for optimization opportunities

### Dashboard Metrics Explained

| Metric | Description | Good Range | Action Required |
|--------|-------------|------------|-----------------|
| Success Rate | % of successful image loads | >90% | <90%: Check Samsung servers |
| Samsung Success Rate | % of Samsung images loaded | >80% | <80%: Review circuit breaker |
| Cache Hit Rate | % of cached search results | >60% | <60%: Optimize cache TTL |
| Average Load Time | Mean image load duration | <3000ms | >3000ms: Check network/CDN |

## 6. Cloudflare CDN Configuration

### Required Page Rules

Add these rules in Cloudflare Dashboard → Page Rules:

1. **Next.js Images**:
   ```
   Pattern: yourdomain.com/_next/image*
   Settings:
   - Cache Level: Cache Everything
   - Edge Cache TTL: 1 month
   - Browser Cache TTL: 1 day
   ```

2. **Search API**:
   ```
   Pattern: yourdomain.com/api/search/*
   Settings:
   - Cache Level: Cache Everything
   - Edge Cache TTL: 5 minutes
   - Browser Cache TTL: 1 minute
   ```

3. **Samsung Images**:
   ```
   Pattern: images.samsung.com/*
   Settings:
   - Cache Level: Cache Everything
   - Edge Cache TTL: 1 day
   - Browser Cache TTL: 1 hour
   ```

### Performance Optimizations

Enable these Cloudflare features:

1. **Speed → Optimization**:
   - ✅ Auto Minify (CSS, HTML, JS)
   - ✅ Brotli compression
   - ✅ Early Hints

2. **Speed → Polish**:
   - ✅ Lossless compression
   - ✅ WebP conversion
   - ✅ AVIF conversion (if available)

3. **Speed → Mirage**:
   - ✅ Enable for mobile optimization

4. **Traffic → Argo**:
   - ✅ Smart Routing (if available)

## 7. Testing Integration

### Unit Tests for Resilient Components

```typescript
import { render, screen, waitFor } from '@testing-library/react';
import { ResilientImage } from '@/components/ui/ResilientImage';

describe('ResilientImage', () => {
  it('should display fallback on Samsung image failure', async () => {
    const mockSamsungUrl = 'https://images.samsung.com/test.jpg';
    
    render(
      <ResilientImage
        src={mockSamsungUrl}
        alt="Test product"
        productName="Test Product"
        brandName="Samsung"
      />
    );
    
    // Simulate image load failure
    const img = screen.getByRole('img');
    fireEvent.error(img);
    
    await waitFor(() => {
      expect(img).toHaveAttribute('src', expect.stringContaining('placehold.co'));
    });
  });
});
```

### Performance Tests

```typescript
import { searchProducts } from '@/lib/data/products';
import { getCacheMetrics } from '@/lib/cache/searchCache';

describe('Search Performance', () => {
  it('should improve performance with caching', async () => {
    const query = 'samsung';
    
    // First search (cache miss)
    const start1 = Date.now();
    await searchProducts(query, 1, 20);
    const duration1 = Date.now() - start1;
    
    // Second search (cache hit)
    const start2 = Date.now();
    await searchProducts(query, 1, 20);
    const duration2 = Date.now() - start2;
    
    // Cache should improve performance
    expect(duration2).toBeLessThan(duration1 * 0.5);
    
    const metrics = getCacheMetrics();
    expect(metrics.stats.hitRate).toBeGreaterThan(0);
  });
});
```

## 8. Troubleshooting Common Issues

### Samsung Images Not Loading

**Symptoms**: Images showing placeholders, high failure rate in dashboard

**Solutions**:
1. Check circuit breaker status in dashboard
2. Reset circuit breaker: `resetCircuitBreaker('images.samsung.com')`
3. Verify Cloudflare cache rules for Samsung domain
4. Check Samsung server status externally

### Search Performance Issues

**Symptoms**: Slow search responses, low cache hit rate

**Solutions**:
1. Review cache metrics in dashboard
2. Adjust cache TTL in `searchCache.ts`
3. Optimize database queries using query optimizer recommendations
4. Check Cloudflare cache rules for API endpoints

### Build/Deployment Issues

**Symptoms**: TypeScript errors, build failures

**Solutions**:
1. Ensure all imports use correct paths
2. Check TypeScript configuration compatibility
3. Verify environment variables are set
4. Review Next.js configuration for conflicts

## 9. Best Practices Checklist

### Before Deploying

- [ ] All external images use `ResilientImage` component
- [ ] Search functions use caching layer
- [ ] Timeout configurations imported from central config
- [ ] Performance monitoring integrated
- [ ] Cloudflare rules configured
- [ ] Tests cover resilience scenarios
- [ ] Dashboard metrics reviewed

### Code Review Checklist

- [ ] No hardcoded timeout values
- [ ] External dependencies have circuit breakers
- [ ] Error handling provides meaningful fallbacks
- [ ] Performance monitoring is implemented
- [ ] TypeScript types are properly defined
- [ ] Tests cover both success and failure scenarios

### Monitoring Checklist

- [ ] Dashboard accessible in development
- [ ] Performance metrics within acceptable ranges
- [ ] Circuit breakers functioning correctly
- [ ] Cache hit rates optimized
- [ ] Error rates below thresholds
- [ ] Fallback mechanisms tested

## Support

For questions or issues with the performance utilities:

1. Check the Technical Standards document
2. Review dashboard metrics for insights
3. Consult troubleshooting section above
4. Contact the development team lead

Remember: These utilities are designed to be the central foundation for performance and resilience. Always use them instead of implementing custom solutions to maintain consistency and leverage collective improvements.
