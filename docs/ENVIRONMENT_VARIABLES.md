# Environment Variable Configuration

This document describes how to set up and manage environment variables for the Cashback Deals project across different environments: local, staging, and production.

## Environment Variable Files

- `.env.local` - Local development environment variables (ignored by git)
- `.env.staging` - Staging environment variables
- `.env.production` - Production environment variables
- `.env.example` - Example environment variables file for reference

## Required Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| NEXT_PUBLIC_SITE_URL         | Base URL of the site                          | https://www.example.com               |
| NEXT_PUBLIC_SUPABASE_URL     | Supabase project URL                          | https://xyzcompany.supabase.co       |
| NEXT_PUBLIC_SUPABASE_ANON_KEY| Supabase anonymous API key                    | public-anon-key                      |
| SUPABASE_SERVICE_ROLE_KEY    | Supabase service role key (server-side only) | service-role-key                     |
| EMAIL_SERVER                | SMTP server for sending emails                | smtp.gmail.com                       |
| EMAIL_PORT                  | SMTP server port                               | 587                                 |
| EMAIL_SECURE                | Use secure connection for SMTP (true/false)  | false                               |
| EMAIL_USER                  | SMTP username                                  | <EMAIL>                    |
| EMAIL_PASSWORD              | SMTP password                                  | password                           |
| EMAIL_FROM                  | Default "from" email address                   | "Cashback Deals" <<EMAIL>> |
| NEXT_PUBLIC_DEBUG_ENABLED   | Enable debug mode on frontend (true/false)    | true                               |
| NEXT_PUBLIC_DEBUG_LEVEL     | Debug level (e.g., error, warn, info, debug)  | error                              |
| DEBUG                      | Enable debug mode on backend (true/false)     | false                              |
| NODE_ENV                   | Node environment (development, production)    | development                       |

## Usage

1. Copy `.env.example` to `.env.local` for local development and fill in the appropriate values.
2. Create `.env.staging` and `.env.production` files with environment-specific values.
3. The project uses `src/env.mjs` to export environment variables with fallback defaults.
4. Access environment variables in the code via the `env` object imported from `src/env.mjs`.

## Notes

- Do not commit `.env.local`, `.env.staging`, or `.env.production` files to version control as they contain sensitive information.
- Use `.env.example` as a reference for required variables.
- Restart the development server after changing environment variables.

## Example

```bash
cp .env.example .env.local
# Edit .env.local with your local environment values
```

## Further Reading

- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [Supabase Environment Setup](https://supabase.com/docs/guides/with-nextjs)
