// check-env.js (plain JavaScript, not module)
console.log('Environment variables test:');
console.log('Process env keys:', Object.keys(process.env).length);
console.log('HOME:', process.env.HOME); // Should show your home directory
console.log('PATH:', process.env.PATH ? 'Found (value hidden)' : 'Not found');

// Now check if we can read from a file directly
const fs = require('fs');
try {
  if (fs.existsSync('.env')) {
    console.log('\n.env file exists');
    const content = fs.readFileSync('.env', 'utf8');
    console.log('First 100 chars:', content.substring(0, 100) + '...');
  } else {
    console.log('\n.env file NOT found in current directory');
  }
  
  if (fs.existsSync('.env.local')) {
    console.log('\n.env.local file exists');
    const content = fs.readFileSync('.env.local', 'utf8');
    console.log('First 100 chars:', content.substring(0, 100) + '...');
  } else {
    console.log('\n.env.local file NOT found in current directory');
  }
  
  console.log('\nCurrent directory:', process.cwd());
  console.log('Directory contents:');
  fs.readdirSync('.').forEach(file => {
    console.log('- ' + file);
  });
} catch (err) {
  console.error('Error reading files:', err);
}