// test-supabase.mjs
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load env vars
dotenv.config();

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('SUPABASE_URL exists:', !!SUPABASE_URL);
console.log('SUPABASE_ANON_KEY exists:', !!SUPABASE_ANON_KEY);

async function testConnection() {
  if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    console.error('Missing Supabase credentials');
    return;
  }
  
  try {
    console.log('Creating Supabase client...');
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    console.log('Testing products table...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, slug')
      .limit(3);
    
    if (productsError) {
      console.error('Error querying products:', productsError);
    } else {
      console.log('Successfully queried products!');
      console.log('Sample products:', products);
    }
    
    console.log('Testing brands table...');
    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('id, slug')
      .limit(3);
    
    if (brandsError) {
      console.error('Error querying brands:', brandsError);
    } else {
      console.log('Successfully queried brands!');
      console.log('Sample brands:', brands);
    }
  } catch (err) {
    console.error('Exception when testing Supabase connection:', err);
  }
}

testConnection();