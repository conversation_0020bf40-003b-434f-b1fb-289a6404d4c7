/**
 * Test script for server-side data layer
 * 
 * This script tests the new server-side data functions to ensure they work correctly
 * before we start refactoring the API routes and pages.
 */

import { createServerClient } from '@supabase/ssr'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('🧪 Testing Server-Side Data Layer')
console.log('================================')

/**
 * Create server client for testing
 */
function createTestServerClient() {
  if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    throw new Error('Missing Supabase environment variables')
  }

  return createServerClient(
    SUPABASE_URL,
    SUPABASE_SERVICE_KEY,
    {
      cookies: {
        get() { return undefined },
        set() {},
        remove() {},
      },
    }
  )
}

/**
 * Test basic product fetching
 */
async function testProductFetching() {
  console.log('\n📦 Testing Product Fetching')
  console.log('---------------------------')
  
  try {
    const supabase = createTestServerClient()
    
    // Test getting all products
    console.log('Fetching products...')
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        *,
        brand:brand_id (
          id,
          name,
          slug,
          logo_url
        ),
        category:category_id (
          id,
          name,
          slug
        ),
        promotion:promotion_id (
          id,
          title,
          max_cashback_amount
        ),
        product_retailer_offers (
          id,
          price,
          stock_status,
          url,
          retailer:retailer_id (
            id,
            name,
            logo_url
          )
        )
      `)
      .eq('status', 'active')
      .limit(3)

    if (error) {
      console.error('❌ Error fetching products:', error)
      return false
    }

    console.log(`✅ Successfully fetched ${products.length} products`)
    
    if (products.length > 0) {
      const product = products[0]
      console.log(`   Sample product: ${product.name}`)
      console.log(`   Brand: ${product.brand?.name || 'No brand'}`)
      console.log(`   Category: ${product.category?.name || 'No category'}`)
      console.log(`   Offers: ${product.product_retailer_offers?.length || 0}`)
    }
    
    return true
  } catch (error) {
    console.error('❌ Exception in testProductFetching:', error)
    return false
  }
}

/**
 * Test brand fetching
 */
async function testBrandFetching() {
  console.log('\n🏷️  Testing Brand Fetching')
  console.log('-------------------------')
  
  try {
    const supabase = createTestServerClient()
    
    console.log('Fetching brands...')
    const { data: brands, error } = await supabase
      .from('brands')
      .select('*')
      .limit(3)

    if (error) {
      console.error('❌ Error fetching brands:', error)
      return false
    }

    console.log(`✅ Successfully fetched ${brands.length} brands`)
    
    if (brands.length > 0) {
      const brand = brands[0]
      console.log(`   Sample brand: ${brand.name}`)
      console.log(`   Slug: ${brand.slug}`)
      console.log(`   Featured: ${brand.featured ? 'Yes' : 'No'}`)
    }
    
    return true
  } catch (error) {
    console.error('❌ Exception in testBrandFetching:', error)
    return false
  }
}

/**
 * Test promotion fetching
 */
async function testPromotionFetching() {
  console.log('\n🎯 Testing Promotion Fetching')
  console.log('-----------------------------')
  
  try {
    const supabase = createTestServerClient()
    
    console.log('Fetching promotions...')
    const { data: promotions, error } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (
          id,
          name,
          slug,
          logo_url
        ),
        category:category_id (
          id,
          name,
          slug
        )
      `)
      .eq('status', 'active')
      .limit(3)

    if (error) {
      console.error('❌ Error fetching promotions:', error)
      return false
    }

    console.log(`✅ Successfully fetched ${promotions.length} promotions`)
    
    if (promotions.length > 0) {
      const promotion = promotions[0]
      console.log(`   Sample promotion: ${promotion.title}`)
      console.log(`   Brand: ${promotion.brand?.name || 'No brand'}`)
      console.log(`   Max cashback: £${promotion.max_cashback_amount}`)
      console.log(`   End date: ${promotion.purchase_end_date}`)
    }
    
    return true
  } catch (error) {
    console.error('❌ Exception in testPromotionFetching:', error)
    return false
  }
}

/**
 * Test complex query with joins
 */
async function testComplexQuery() {
  console.log('\n🔗 Testing Complex Query with Joins')
  console.log('-----------------------------------')
  
  try {
    const supabase = createTestServerClient()
    
    console.log('Fetching product with all related data...')
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        *,
        brand:brand_id (
          id,
          name,
          slug,
          logo_url,
          description
        ),
        category:category_id (
          id,
          name,
          slug
        ),
        promotion:promotion_id (
          id,
          title,
          description,
          max_cashback_amount,
          purchase_start_date,
          purchase_end_date,
          status
        ),
        product_retailer_offers (
          id,
          price,
          stock_status,
          url,
          created_at,
          retailer:retailer_id (
            id,
            name,
            logo_url,
            website_url
          )
        )
      `)
      .eq('status', 'active')
      .limit(1)

    if (error) {
      console.error('❌ Error in complex query:', error)
      return false
    }

    if (products.length === 0) {
      console.log('⚠️  No products found')
      return true
    }

    const product = products[0]
    console.log('✅ Complex query successful')
    console.log(`   Product: ${product.name}`)
    console.log(`   Brand: ${product.brand?.name || 'No brand'}`)
    console.log(`   Category: ${product.category?.name || 'No category'}`)
    console.log(`   Promotion: ${product.promotion?.title || 'No promotion'}`)
    console.log(`   Retailer offers: ${product.product_retailer_offers?.length || 0}`)
    
    if (product.product_retailer_offers?.length > 0) {
      const offer = product.product_retailer_offers[0]
      console.log(`   Sample offer: £${offer.price} at ${offer.retailer?.name || 'Unknown retailer'}`)
    }
    
    return true
  } catch (error) {
    console.error('❌ Exception in testComplexQuery:', error)
    return false
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('Starting server-side data layer tests...\n')
  
  const tests = [
    { name: 'Product Fetching', fn: testProductFetching },
    { name: 'Brand Fetching', fn: testBrandFetching },
    { name: 'Promotion Fetching', fn: testPromotionFetching },
    { name: 'Complex Query', fn: testComplexQuery },
  ]
  
  let passed = 0
  let failed = 0
  
  for (const test of tests) {
    try {
      const result = await test.fn()
      if (result) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" threw an exception:`, error)
      failed++
    }
  }
  
  console.log('\n📊 Test Results')
  console.log('===============')
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`)
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Server-side data layer is working correctly.')
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above.')
  }
}

// Run the tests
runTests().catch(console.error)
