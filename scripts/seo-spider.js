#!/usr/bin/env node

/**
 *  how to run?
 * > node scripts/seo-spider.js http://localhost:3000
 * SEO Spider Simulator
 * 
 * This script simulates what an SEO spider would see when crawling a localhost URL.
 * It analyzes the rendered HTML, metadata, and other important SEO elements.
 * 
 * Usage: 
 *   node scripts/seo-spider.js <url> [options]
 * 
 * Options:
 *   --json       Output results as JSON
 *   --no-color   Disable colored output
 *   --no-images  Skip image analysis
 *   --no-links   Skip link analysis
 *   --no-meta    Skip meta tag analysis
 *   --no-stats   Skip statistics
 * 
 * SEO Spider Script Features
 * Comprehensive Analysis:
 * Extracts and analyzes meta tags, titles, and descriptions
 * Checks for proper heading hierarchy (H1-H6)
 * Validates image attributes (alt text, dimensions, lazy loading)
 * Analyzes internal and external links
 * Detects structured data (Schema.org)
 * Performance Metrics:
 * Page load time
 * DOM size
 * Text to HTML ratio
 * Word count
 * Output Options:
 * Color-coded console output for easy reading
 * JSON output option for programmatic use
 * Detailed statistics and warnings
 */

// Use ESM imports
import { J<PERSON><PERSON> } from 'jsdom';
import chalk from 'chalk';
import { program } from 'commander';
import { performance } from 'perf_hooks';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Configure and run the application
async function run() {
    // Configure CLI
    program
        .arguments('<url>')
        .option('--json', 'Output results as JSON', false)
        .option('--no-color', 'Disable colored output', false)
        .option('--no-images', 'Skip image analysis', false)
        .option('--no-links', 'Skip link analysis', false)
        .option('--no-meta', 'Skip meta tag analysis', false)
        .option('--no-stats', 'Skip statistics', false);

    program.parse(process.argv);
    const options = program.opts();

    // Disable colors if requested
    if (options.noColor) {
        chalk.level = 0;
    }

    const targetUrl = program.args[0];
    if (!targetUrl) {
        console.error(chalk.red('Error: Please provide a URL to analyze'));
        process.exit(1);
    }

    // Validate URL
    let url;
    try {
        url = new URL(targetUrl);
        if (!['http:', 'https:'].includes(url.protocol)) {
            throw new Error('Invalid protocol');
        }
    } catch (err) {
        console.error(chalk.red(`Error: Invalid URL: ${targetUrl}`));
        process.exit(1);
    }

    try {
        const results = await analyzeSeo(url, options);
        displayResults(results, options);
    } catch (error) {
        console.error(chalk.red('Error:'), error);
        process.exit(1);
    }
}

// Helper function to calculate text to HTML ratio
function calculateTextToHtmlRatio(html) {
    const textOnly = html.replace(/<[^>]*>/g, '');
    const textLength = textOnly.length;
    const htmlLength = html.length;

    if (htmlLength === 0) return 0;
    return Math.round((textLength / htmlLength) * 100);
}

// Format and display results
function displayResults(results, options) {
    if (options.json) {
        console.log(JSON.stringify(results, null, 2));
        return;
    }

    console.log(chalk.underline('\n📊 SEO Analysis Results'));
    console.log(chalk.gray('='.repeat(80)));

    // Basic Info
    console.log(chalk.blue('\n🔹 Basic Information'));
    console.log(chalk.gray('-'.repeat(40)));
    console.log(`${chalk.bold('URL:')} ${results.url}`);
    console.log(`${chalk.bold('Title:')} ${results.title || chalk.red('Missing')}`);
    console.log(`${chalk.bold('Description:')} ${results.description ?
        (results.description.length > 100 ?
            results.description.substring(0, 100) + '...' :
            results.description) :
        chalk.red('Missing')}`);
    console.log(`${chalk.bold('Canonical:')} ${results.canonical || chalk.yellow('Not set')}`);
    console.log(`${chalk.bold('Robots:')} ${results.robots}`);
    console.log(`${chalk.bold('Language:')} ${results.language || chalk.yellow('Not set')}`);

    // Headings
    console.log(chalk.blue('\n🔹 Headings'));
    console.log(chalk.gray('-'.repeat(40)));
    for (const [level, headings] of Object.entries(results.headings)) {
        if (headings.length > 0) {
            console.log(`\n${chalk.bold(level.toUpperCase())} (${headings.length}):`);
            headings.forEach((h, i) => {
                console.log(`  ${i + 1}. ${h.text.substring(0, 60)}${h.text.length > 60 ? '...' : ''}`);
            });
        }
    }

    // Images
    if (results.images.length > 0) {
        console.log(chalk.blue('\n🔹 Images'));
        console.log(chalk.gray('-'.repeat(40)));
        console.log(`Found ${results.images.length} images`);

        const imagesWithoutAlt = results.images.filter(img => !img.alt);
        if (imagesWithoutAlt.length > 0) {
            console.log(chalk.yellow(`⚠️  ${imagesWithoutAlt.length} images without alt text`));
        }

        const lazyLoaded = results.images.filter(img => img.isLazy);
        if (lazyLoaded.length > 0) {
            console.log(`ℹ️  ${lazyLoaded.length} images are lazy-loaded`);
        }
    }

    // Links
    if (results.links.internal.length > 0 || results.links.external.length > 0) {
        console.log(chalk.blue('\n🔹 Links'));
        console.log(chalk.gray('-'.repeat(40)));
        console.log(`Internal: ${results.links.internal.length}`);
        console.log(`External: ${results.links.external.length}`);

        const nofollowLinks = [
            ...results.links.internal.filter(l => l.isNoFollow),
            ...results.links.external.filter(l => l.isNoFollow)
        ];

        if (nofollowLinks.length > 0) {
            console.log(`Nofollow: ${nofollowLinks.length}`);
        }
    }

    // Performance
    if (results.performance) {
        console.log(chalk.blue('\n🔹 Performance'));
        console.log(chalk.gray('-'.repeat(40)));
        console.log(`Page load time: ${results.performance.loadTime.toFixed(2)}ms`);
        console.log(`DOM size: ${results.performance.domSize} elements`);
        console.log(`Text to HTML ratio: ${results.performance.textToHtmlRatio}%`);
        console.log(`Word count: ${results.performance.wordCount}`);
    }

    // Structured Data
    if (results.structuredData.length > 0) {
        console.log(chalk.blue('\n🔹 Structured Data'));
        console.log(chalk.gray('-'.repeat(40)));
        results.structuredData.forEach((data, i) => {
            const type = Array.isArray(data['@type']) ?
                data['@type'].join(', ') : data['@type'];
            console.log(`${i + 1}. ${type || 'Unknown type'}`);
        });
    }

    console.log('\n' + chalk.green('✅ Analysis complete!'));
}

// Main analysis function
async function analyzeSeo(url, options) {
    const startTime = performance.now();

    try {
        console.log(chalk.blue(`\n🔍 Analyzing ${url.href}...\n`));

        // Load the page with JSDOM
        const dom = await JSDOM.fromURL(url.href, {
            runScripts: 'dangerously',
            resources: 'usable',
            pretendToBeVisual: true,
        });

        const { document } = dom.window;

        // Wait for dynamic content to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Collect SEO data
        const seoData = {
            url: url.href,
            timestamp: new Date().toISOString(),
            title: document.title || null,
            description: document.querySelector('meta[name="description"]')?.content || null,
            canonical: document.querySelector('link[rel="canonical"]')?.href || null,
            robots: document.querySelector('meta[name="robots"]')?.content || 'index, follow',
            viewport: document.querySelector('meta[name="viewport"]')?.content || null,
            language: document.documentElement.lang || null,
            charset: document.characterSet || document.charset || null,
            metaTags: {},
            headings: {
                h1: [],
                h2: [],
                h3: [],
                h4: [],
                h5: [],
                h6: [],
            },
            images: [],
            links: {
                internal: [],
                external: [],
            },
            structuredData: [],
            performance: {},
        };

        // Extract meta tags
        if (!options.meta) {
            const metaTags = document.querySelectorAll('meta');
            metaTags.forEach(tag => {
                const name = tag.name || tag.getAttribute('property') || tag.httpEquiv || 'unknown';
                seoData.metaTags[name] = tag.content || '';
            });
        }

        // Extract headings
        for (let i = 1; i <= 6; i++) {
            const headings = document.querySelectorAll(`h${i}`);
            headings.forEach(h => {
                const text = h.textContent?.trim();
                if (text) {
                    seoData.headings[`h${i}`].push({
                        text,
                        id: h.id || null,
                        className: h.className || null,
                    });
                }
            });
        }

        // Extract images
        if (!options.images) {
            const images = document.querySelectorAll('img');
            images.forEach((img, index) => {
                seoData.images.push({
                    src: img.src || null,
                    alt: img.alt || null,
                    title: img.title || null,
                    width: img.width || null,
                    height: img.height || null,
                    loading: img.loading || 'eager',
                    decoding: img.decoding || 'auto',
                    isLazy: img.loading === 'lazy' || img.getAttribute('data-src') !== null,
                });
            });
        }

        // Extract links
        if (!options.links) {
            const links = document.querySelectorAll('a[href]');
            links.forEach(link => {
                try {
                    const href = link.href;
                    const isInternal = new URL(href, url).hostname === url.hostname;

                    const linkData = {
                        href,
                        text: link.textContent?.trim() || null,
                        title: link.title || null,
                        rel: link.rel || null,
                        target: link.target || null,
                        isNoFollow: link.rel?.includes('nofollow') || false,
                        isSponsored: link.rel?.includes('sponsored') || false,
                        isUGC: link.rel?.includes('ugc') || false,
                    };

                    if (isInternal) {
                        seoData.links.internal.push(linkData);
                    } else {
                        seoData.links.external.push(linkData);
                    }
                } catch (e) {
                    // Skip invalid URLs
                }
            });
        }

        // Extract structured data
        const scripts = document.querySelectorAll('script[type="application/ld+json"]');
        scripts.forEach(script => {
            try {
                const data = JSON.parse(script.textContent);
                seoData.structuredData.push(data);
            } catch (e) {
                // Skip invalid JSON
            }
        });

        // Calculate performance metrics
        if (!options.stats) {
            const endTime = performance.now();
            seoData.performance = {
                loadTime: endTime - startTime,
                domSize: document.querySelectorAll('*').length,
                textToHtmlRatio: calculateTextToHtmlRatio(document.documentElement.outerHTML),
                wordCount: document.body.textContent?.split(/\s+/).filter(Boolean).length || 0,
            };
        }

        return seoData;
    } catch (error) {
        console.error(chalk.red(`Error analyzing ${url.href}:`), error);
        throw error;
    }
}

// Start the application
run().catch(console.error);
