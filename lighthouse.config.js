// lighthouse.config.js
// Lighthouse CI configuration for automated SEO and performance testing

module.exports = {
    ci: {
        collect: {
            url: [
                'http://localhost:3000/',
                'http://localhost:3000/products',
                'http://localhost:3000/brands',
                'http://localhost:3000/retailers',
                'http://localhost:3000/search?q=samsung',
            ],
            numberOfRuns: 3,
            settings: {
                chromeFlags: '--no-sandbox --disable-dev-shm-usage',
                preset: 'desktop',
                onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
                skipAudits: [
                    'uses-http2', // Skip HTTP/2 audit for local testing
                    'redirects-http', // Skip HTTPS redirect audit for local testing
                ],
            },
        },
        assert: {
            assertions: {
                // Performance thresholds
                'categories:performance': ['error', { minScore: 0.8 }],
                'categories:accessibility': ['error', { minScore: 0.9 }],
                'categories:best-practices': ['error', { minScore: 0.85 }],
                'categories:seo': ['error', { minScore: 0.85 }],

                // Core Web Vitals thresholds
                'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
                'first-input-delay': ['error', { maxNumericValue: 100 }],
                'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],

                // SEO-specific audits
                'document-title': 'error',
                'meta-description': 'error',
                'structured-data': 'warn',
                'robots-txt': 'warn',
                'image-alt': 'error',
                'link-text': 'warn',
                'crawlable-anchors': 'warn',
                'canonical': 'warn',

                // Performance audits
                'unused-css-rules': 'warn',
                'unused-javascript': 'warn',
                'modern-image-formats': 'warn',
                'efficient-animated-content': 'warn',
                'preload-lcp-image': 'warn',

                // Accessibility audits
                'color-contrast': 'error',
                'heading-order': 'warn',
                'label': 'error',
                'button-name': 'error',
                'link-name': 'error',
            },
        },
        upload: {
            target: 'temporary-public-storage',
        },
        server: {
            port: 9001,
            storage: {
                storageMethod: 'filesystem',
                storagePath: './.lighthouseci',
            },
        },
    },
};
