name: SEO Testing and Monitoring

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run SEO monitoring daily at 6 AM UTC
    - cron: '0 6 * * *'

jobs:
  seo-testing:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build

    - name: Start application
      run: |
        npm start &
        sleep 30  # Wait for app to start
      env:
        NODE_ENV: production

    - name: Wait for application to be ready
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'

    - name: Run SEO tests
      run: npm run seo:test
      env:
        BASE_URL: http://localhost:3000

    - name: Run Lighthouse SEO audit
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './lighthouse.config.js'
        uploadArtifacts: true
        temporaryPublicStorage: true

    - name: Validate sitemap
      run: |
        curl -f http://localhost:3000/sitemap.xml > sitemap.xml
        xmllint --noout sitemap.xml && echo "✅ Sitemap is valid XML"

    - name: Check robots.txt
      run: |
        curl -f http://localhost:3000/robots.txt > robots.txt
        grep -q "Sitemap:" robots.txt && echo "✅ Robots.txt contains sitemap reference"

    - name: Test Core Web Vitals API
      run: |
        curl -f http://localhost:3000/api/analytics/web-vitals || echo "⚠️ Web Vitals API not responding"

    - name: Upload SEO test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: seo-test-results
        path: |
          seo-reports/
          lighthouse-results/
          sitemap.xml
          robots.txt

    - name: Comment PR with SEO results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          try {
            const reportPath = 'seo-reports/seo-test-report.md';
            if (fs.existsSync(reportPath)) {
              const report = fs.readFileSync(reportPath, 'utf8');
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `## 🔍 SEO Test Results\n\n${report}`
              });
            }
          } catch (error) {
            console.log('Could not post SEO results:', error);
          }

  performance-monitoring:
    runs-on: ubuntu-latest
    needs: seo-testing
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build and start application
      run: |
        npm run build
        npm start &
        sleep 30

    - name: Run performance monitoring
      run: |
        curl -s http://localhost:3000/api/seo/monitor > monitoring-report.json
        cat monitoring-report.json | jq '.overallScore'

    - name: Check performance thresholds
      run: |
        SCORE=$(cat monitoring-report.json | jq '.overallScore')
        if [ "$SCORE" -lt 80 ]; then
          echo "❌ SEO score below threshold: $SCORE/100"
          exit 1
        else
          echo "✅ SEO score meets threshold: $SCORE/100"
        fi

    - name: Upload monitoring results
      uses: actions/upload-artifact@v4
      with:
        name: performance-monitoring
        path: monitoring-report.json

  lighthouse-ci:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build

    - name: Start application
      run: |
        npm start &
        sleep 30

    - name: Run Lighthouse CI
      if: ${{ !cancelled() && !failure() && github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name == github.repository }}
      run: |
        npm install -g @lhci/cli
        if [ -z "$LHCI_TOKEN" ]; then
          echo "No LHCI token provided, running without GitHub status checks"
          lhci autorun --upload.target=temporary-public-storage
        else
          lhci autorun
        fi
      env:
        LHCI_TOKEN: ${{ secrets.LHCI_TOKEN || '' }}

    - name: Upload Lighthouse results
      uses: actions/upload-artifact@v4
      with:
        name: lighthouse-results
        path: .lighthouseci/
