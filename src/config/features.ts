export const featureFlags = {
	search: {
		categoryFilter: true,
		subCategoryFilter: false,
		recommendedFilter: false,
	},
	navigation: {
		alphabetNav: {
			brands: true, // Controls visibility on brands page
		}
	}
} as const;

export type FeatureFlags = typeof featureFlags;

export const FEATURES = {
	ENABLE_RETAILER_FILTER: false, // Toggle retailer filtering in the FilterMenu
	FORCE_DEBUG_MODE: false, // Override environment-based debug visibility
} as const;

export type Feature = keyof typeof FEATURES;

// Helper function to check if a feature is enabled
export function isFeatureEnabled(feature: Feature): boolean {
	return FEATURES[feature];
}