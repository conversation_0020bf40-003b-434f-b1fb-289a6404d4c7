export interface DebugConfig {
	enabled: boolean;
	level: 'error' | 'warn' | 'info' | 'debug' | 'verbose';
	features: {
		console: boolean;
		ui: boolean;
		timing: boolean;
		queries: boolean;
		fileLogging: boolean;
	};
	allowedEnvironments: string[];
}

export interface DebugData {
	timing: {
		start: number;
		end: number;
		duration: number;
	};
	queries: string[];
	params: Record<string, unknown>;
	error?: unknown;
	environment?: {
		nodeEnv: string;
		supabaseUrl?: string;
		hasAnonKey: boolean;
	};
	request?: {
		url: string;
		method: string;
		headers?: Record<string, string>;
	};
	[key: string]: unknown;
}

export const debugConfig: DebugConfig = {
	enabled: process.env.NEXT_PUBLIC_DEBUG_ENABLED === 'true',
	level: (process.env.NEXT_PUBLIC_DEBUG_LEVEL as DebugConfig['level']) || 'error',

	features: {
		console: true,
		ui: true,
		timing: true,
		queries: true,
		fileLogging: true
	},
	allowedEnvironments: ['development', 'staging', 'production']
};

// Enhanced logging to verify configuration and environment
console.log('Debug Configuration:', {
	enabled: debugConfig.enabled,
	level: debugConfig.level,
	nodeEnv: process.env.NODE_ENV,
	features: debugConfig.features,
	supabaseConfig: {
		hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
		hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
		url: process.env.NEXT_PUBLIC_SUPABASE_URL
	}
});

export const isDebugEnabled = (): boolean => {
	const enabled = debugConfig.enabled &&
		debugConfig.allowedEnvironments.includes(process.env.NODE_ENV || 'development');
	console.log('Debug Enabled:', enabled, {
		configEnabled: debugConfig.enabled,
		environment: process.env.NODE_ENV,
		allowedEnvironments: debugConfig.allowedEnvironments,
		supabaseConfig: {
			hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
			hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
		}
	});
	return enabled;
};
