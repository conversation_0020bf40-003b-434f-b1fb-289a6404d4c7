/**
 * Server-side Supabase client configuration
 * 
 * This module provides secure server-side access to <PERSON>pa<PERSON> using the service role key.
 * It should ONLY be used in server components, API routes, and server-side functions.
 * 
 * IMPORTANT UPDATES (2024-06-15):
 * - Updated cookie handling to work with Next.js 15+ async cookies API
 * - All cookie operations (get/set/remove) are now async
 * - Added proper error handling for server-side operations
 * 
 * Security Features:
 * - Uses service role key (not exposed to client)
 * - Bypasses Row Level Security for administrative operations
 * - Provides full database access for server-side operations
 * - Secure cookie handling with proper async/await patterns
 */

import { createClient } from '@supabase/supabase-js'
import { Database } from '../../types/supabase'

type SupabaseClient = ReturnType<typeof createClient<Database>>

/**
 * Creates a server-side Supabase client with service role privileges
 * 
 * @returns Supabase client with full database access
 * @throws Error if required environment variables are missing
 */
export function createServerSupabaseClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error(
      'Missing required Supabase environment variables. ' +
      'Ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.'
    )
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    }
  })
}

/**
 * Creates a server-side Supabase client for read-only operations
 * 
 * @returns Supabase client with read-only access
 * @throws Error if required environment variables are missing
 */
export function createServerSupabaseReadOnlyClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error(
      'Missing required Supabase environment variables. ' +
      'Ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set.'
    )
  }

  return createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    }
  })
}

/**
 * Creates a simple Supabase client for cached functions
 * This client doesn't use cookies to avoid Next.js caching conflicts
 * 
 * @returns Supabase client compatible with unstable_cache
 */
export function createCacheableSupabaseClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error(
      'Missing required Supabase environment variables. ' +
      'Ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set.'
    )
  }

  return createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    }
  })
}
