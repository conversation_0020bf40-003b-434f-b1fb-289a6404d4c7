/**
 * Centralized timeout configuration for the application
 * Provides consistent timeout settings across different services and operations
 */

// Environment-based timeout multipliers
const getTimeoutMultiplier = (): number => {
  const env = process.env.NODE_ENV;
  switch (env) {
    case 'development':
      return 2; // Longer timeouts in development
    case 'test':
      return 0.5; // Shorter timeouts in tests
    case 'production':
    default:
      return 1; // Standard timeouts in production
  }
};

const TIMEOUT_MULTIPLIER = getTimeoutMultiplier();

/**
 * Base timeout configurations in milliseconds
 */
export const TIMEOUT_CONFIG = {
  // Database operations
  DATABASE: {
    QUERY: 10000 * TIMEOUT_MULTIPLIER, // 10 seconds for complex queries
    SIMPLE_QUERY: 5000 * TIMEOUT_MULTIPLIER, // 5 seconds for simple queries
    TRANSACTION: 15000 * TIMEOUT_MULTIPLIER, // 15 seconds for transactions
    CONNECTION: 3000 * TIMEOUT_MULTIPLIER, // 3 seconds for connection establishment
  },

  // External API calls
  EXTERNAL_API: {
    SAMSUNG_IMAGES: 8000 * TIMEOUT_MULTIPLIER, // 8 seconds for Samsung image requests
    SUPABASE_STORAGE: 6000 * TIMEOUT_MULTIPLIER, // 6 seconds for Supabase storage
    GENERAL: 5000 * TIMEOUT_MULTIPLIER, // 5 seconds for general external APIs
    HEALTH_CHECK: 2000 * TIMEOUT_MULTIPLIER, // 2 seconds for health checks
  },

  // Image operations
  IMAGE: {
    LOAD: 10000 * TIMEOUT_MULTIPLIER, // 10 seconds for image loading
    VALIDATION: 5000 * TIMEOUT_MULTIPLIER, // 5 seconds for image validation
    OPTIMIZATION: 15000 * TIMEOUT_MULTIPLIER, // 15 seconds for image optimization
    UPLOAD: 30000 * TIMEOUT_MULTIPLIER, // 30 seconds for image uploads
  },

  // Search operations
  SEARCH: {
    SIMPLE: 3000 * TIMEOUT_MULTIPLIER, // 3 seconds for simple searches
    COMPLEX: 8000 * TIMEOUT_MULTIPLIER, // 8 seconds for complex searches with joins
    SUGGESTIONS: 1000 * TIMEOUT_MULTIPLIER, // 1 second for search suggestions
    AUTOCOMPLETE: 500 * TIMEOUT_MULTIPLIER, // 500ms for autocomplete
  },

  // API route timeouts
  API_ROUTES: {
    SEARCH: 10000 * TIMEOUT_MULTIPLIER, // 10 seconds for search endpoints
    PRODUCT_DETAIL: 5000 * TIMEOUT_MULTIPLIER, // 5 seconds for product details
    GENERAL: 8000 * TIMEOUT_MULTIPLIER, // 8 seconds for general API routes
    HEALTH: 1000 * TIMEOUT_MULTIPLIER, // 1 second for health endpoints
  },

  // Client-side timeouts
  CLIENT: {
    FETCH: 8000 * TIMEOUT_MULTIPLIER, // 8 seconds for client-side fetch requests
    RETRY_DELAY: 1000 * TIMEOUT_MULTIPLIER, // 1 second base retry delay
    DEBOUNCE: 300, // 300ms debounce (not affected by multiplier)
    THROTTLE: 100, // 100ms throttle (not affected by multiplier)
  },

  // Circuit breaker timeouts
  CIRCUIT_BREAKER: {
    FAILURE_THRESHOLD: 5, // Number of failures before opening circuit
    RECOVERY_TIMEOUT: 30000 * TIMEOUT_MULTIPLIER, // 30 seconds before attempting recovery
    HALF_OPEN_MAX_CALLS: 3, // Max calls in half-open state
  }
} as const;

/**
 * Create a timeout promise that rejects after specified milliseconds
 * @param ms - Timeout in milliseconds
 * @param message - Custom timeout message
 * @returns Promise that rejects with timeout error
 */
export function createTimeoutPromise(ms: number, message?: string): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(message || `Operation timed out after ${ms}ms`);
      error.name = 'TimeoutError';
      reject(error);
    }, ms);
  });
}

/**
 * Wrap a promise with a timeout
 * @param promise - The promise to wrap
 * @param timeoutMs - Timeout in milliseconds
 * @param timeoutMessage - Custom timeout message
 * @returns Promise that resolves with the original promise or rejects with timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage?: string
): Promise<T> {
  const timeoutPromise = createTimeoutPromise(timeoutMs, timeoutMessage);
  
  try {
    return await Promise.race([promise, timeoutPromise]);
  } catch (error) {
    // If it's our timeout error, add additional context
    if (error instanceof Error && error.name === 'TimeoutError') {
      console.warn(`Timeout occurred: ${error.message}`);
    }
    throw error;
  }
}

/**
 * Create a fetch wrapper with timeout and retry logic
 * @param url - URL to fetch
 * @param options - Fetch options
 * @param timeoutMs - Timeout in milliseconds
 * @param retries - Number of retry attempts
 * @returns Promise with fetch response
 */
export async function fetchWithTimeout(
  url: string,
  options: RequestInit = {},
  timeoutMs: number = TIMEOUT_CONFIG.CLIENT.FETCH,
  retries: number = 2
): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

  const fetchOptions: RequestInit = {
    ...options,
    signal: controller.signal,
  };

  try {
    const response = await fetch(url, fetchOptions);
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    
    // Retry logic for network errors (but not for HTTP errors)
    if (retries > 0 && (
      error instanceof TypeError || // Network error
      (error instanceof Error && error.name === 'AbortError') // Timeout
    )) {
      console.warn(`Fetch failed, retrying... (${retries} attempts left)`, error.message);
      await new Promise(resolve => setTimeout(resolve, TIMEOUT_CONFIG.CLIENT.RETRY_DELAY));
      return fetchWithTimeout(url, options, timeoutMs, retries - 1);
    }
    
    throw error;
  }
}

/**
 * Get timeout configuration for a specific operation type
 * @param category - The category of operation
 * @param operation - The specific operation
 * @returns Timeout in milliseconds
 */
export function getTimeout(category: keyof typeof TIMEOUT_CONFIG, operation?: string): number {
  const categoryConfig = TIMEOUT_CONFIG[category];
  
  if (typeof categoryConfig === 'number') {
    return categoryConfig;
  }
  
  if (operation && typeof categoryConfig === 'object' && operation in categoryConfig) {
    return (categoryConfig as any)[operation];
  }
  
  // Return a default timeout if specific operation not found
  if (typeof categoryConfig === 'object') {
    const values = Object.values(categoryConfig);
    return Math.max(...values.filter(v => typeof v === 'number'));
  }
  
  return TIMEOUT_CONFIG.API_ROUTES.GENERAL; // Fallback
}

/**
 * Create a timeout configuration for specific environments or features
 * @param overrides - Timeout overrides
 * @returns Merged timeout configuration
 */
export function createTimeoutConfig(overrides: Partial<typeof TIMEOUT_CONFIG>) {
  return {
    ...TIMEOUT_CONFIG,
    ...overrides
  };
}

/**
 * Validate if a timeout value is reasonable
 * @param timeoutMs - Timeout in milliseconds
 * @param operation - Operation name for context
 * @returns True if timeout is reasonable
 */
export function validateTimeout(timeoutMs: number, operation?: string): boolean {
  const MIN_TIMEOUT = 100; // 100ms minimum
  const MAX_TIMEOUT = 300000; // 5 minutes maximum
  
  if (timeoutMs < MIN_TIMEOUT) {
    console.warn(`Timeout too short for ${operation || 'operation'}: ${timeoutMs}ms (minimum: ${MIN_TIMEOUT}ms)`);
    return false;
  }
  
  if (timeoutMs > MAX_TIMEOUT) {
    console.warn(`Timeout too long for ${operation || 'operation'}: ${timeoutMs}ms (maximum: ${MAX_TIMEOUT}ms)`);
    return false;
  }
  
  return true;
}

/**
 * Get environment-specific timeout recommendations
 * @returns Object with timeout recommendations
 */
export function getTimeoutRecommendations() {
  return {
    development: {
      message: 'Using extended timeouts for development environment',
      multiplier: 2,
      recommendations: [
        'Use longer timeouts to account for debugging and slower development servers',
        'Consider using mock services for external dependencies',
        'Enable detailed timeout logging'
      ]
    },
    test: {
      message: 'Using reduced timeouts for test environment',
      multiplier: 0.5,
      recommendations: [
        'Use shorter timeouts to make tests fail fast',
        'Mock external services to avoid network dependencies',
        'Use deterministic timeouts for consistent test results'
      ]
    },
    production: {
      message: 'Using standard timeouts for production environment',
      multiplier: 1,
      recommendations: [
        'Monitor timeout metrics and adjust based on real-world performance',
        'Implement circuit breakers for external dependencies',
        'Use progressive timeouts (shorter for retries)'
      ]
    }
  };
}
