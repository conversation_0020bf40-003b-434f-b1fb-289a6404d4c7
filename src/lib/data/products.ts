import type { Brand, FilterPromotion, ProductResponse, ProductFilters, TransformedProduct, PaginatedProductsResponse } from './types';
import { createCacheableSupabaseClient } from '../supabase/server';
import { logger } from '../utils/logger';
import { withTimeout, TIMEOUT_CONFIG } from '../timeoutConfig';

export function transformProduct(product: any): TransformedProduct {
  return {
    id: product.id,
    name: product.name,
    slug: product.slug,
    description: product.description || '',
    images: product.images || [],
    specifications: product.specifications || null,
    status: product.status || 'active',
    isFeatured: product.is_featured || false,
    isSponsored: product.is_sponsored || false,
    cashbackAmount: product.cashback_amount || 0,
    minPrice: null, // This should be calculated separately if needed
    modelNumber: product.model_number || '',
    createdAt: product.created_at,
    updatedAt: product.updated_at,
    brand: product.brand ? {
      id: product.brand.id,
      name: product.brand.name,
      slug: product.brand.slug,
      logoUrl: product.brand.logo_url,
      description: product.brand.description || null,
    } : null,
    category: product.category || null,
    promotion: product.promotion ? {
      id: product.promotion.id,
      title: product.promotion.title,
      description: product.promotion.description || null,
      maxCashbackAmount: product.promotion.max_cashback_amount || 0,
      purchaseStartDate: product.promotion.purchase_start_date,
      purchaseEndDate: product.promotion.purchase_end_date,
      claimStartOffsetDays: product.promotion.claim_start_offset_days,
      claimWindowDays: product.promotion.claim_window_days,
      termsUrl: product.promotion.terms_url ?? null,
      termsDescription: product.promotion.terms_description ?? null,
      status: product.promotion.status || 'active',
      isFeatured: product.promotion.is_featured || false,
    } : null,
    retailerOffers: product.retailer_offers || [],
  };
}

export async function getProductPageData(idOrSlug: string): Promise<{ product: TransformedProduct; similarProducts: TransformedProduct[] } | null> {
  const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(idOrSlug);

  if (isUUID) {
    return await getProductWithSimilar(idOrSlug);
  } else {
    const product = await getProductBySlug(idOrSlug);
    if (!product) return null;
    
    const similarProducts = await getSimilarProducts(product.category?.id || '', product.id).catch(() => []);
    
    return { product, similarProducts };
  }
}

export async function getProducts(filters: ProductFilters = {}): Promise<PaginatedProductsResponse> {
  const supabase = createCacheableSupabaseClient();
  
  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  let query = supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `, { count: 'exact' });

  if (filters.brandId) {
    query = query.eq('brand_id', filters.brandId);
  }
  if (filters.categoryId) {
    query = query.eq('category_id', filters.categoryId);
  }
  if (filters.promotionId) {
    query = query.eq('promotion_id', filters.promotionId);
  }
  if (filters.status) {
    query = query.eq('status', filters.status);
  }
  
  const page = filters.page || 1;
  const pageSize = filters.pageSize || 20;
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw error;
  }

  const transformedProducts = (data || []).map(transformProduct);

  return {
    product: transformedProducts,
    similarProducts: [],
    pagination: {
      page,
      pageSize,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / pageSize),
      hasNext: page < Math.ceil((count || 0) / pageSize),
      hasPrev: page > 1
    }
  };
}

export async function getProduct(id: string): Promise<TransformedProduct | null> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching product by ID:', error);
    return null;
  }

  if (!data) return null;

  return transformProduct(data);
}

export async function getProductBySlug(slug: string): Promise<TransformedProduct | null> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `)
    .eq('slug', slug)
    .single();

  if (error) {
    console.error('Error fetching product by slug:', error);
    return null;
  }

  if (!data) return null;

  return transformProduct(data);
}

export async function getProductWithSimilar(id: string): Promise<{ product: TransformedProduct; similarProducts: TransformedProduct[] } | null> {
  const product = await getProduct(id);
  if (!product) return null;

  const similarProducts = await getSimilarProducts(product.category?.id || '', product.id).catch(() => []);

  return { product, similarProducts };
}

export async function getSimilarProducts(categoryId: string, productId: string): Promise<TransformedProduct[]> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      promotion:promotion_id (*),
      retailer_offers:product_retailer_offers(*)
    `)
    .eq('category_id', categoryId)
    .neq('id', productId)
    .limit(10);

  if (error) {
    console.error('Error fetching similar products:', error);
    return [];
  }

  return (data || []).map(transformProduct);
}

export async function getFeaturedProducts(limit: number = 10): Promise<TransformedProduct[]> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (*)
    `)
    .eq('is_featured', true)
    .limit(limit);

  if (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }

  if (!data) return [];

  return data.map(transformProduct);
}

export async function getFilterOptions(): Promise<{ brands: Brand[]; promotions: FilterPromotion[]; }> {
  try {
    const supabase = createCacheableSupabaseClient();

    if (!supabase) {
      console.error('Failed to initialize Supabase client');
      return { brands: [], promotions: [] };
    }

    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('*')
      .eq('status', 'active')
      .order('name', { ascending: true });

    if (brandsError) {
      console.error('Error fetching brands:', brandsError);
      return { brands: [], promotions: [] };
    }

    const { data: promotions, error: promotionsError } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brands!inner(*)
      `)
      .eq('status', 'active')
      .eq('brand.status', 'active')
      .order('title', { ascending: true });

    if (promotionsError) {
      console.error('Error fetching promotions:', promotionsError);
      return { brands: [], promotions: [] };
    }

    const transformedPromotions = promotions.map((promo: any) => ({
      ...promo,
      brand_name: promo.brand?.name || 'Unknown Brand',
    }));

    return {
      brands: brands || [],
      promotions: transformedPromotions || [],
    };
  } catch (error) {
    console.error('Unexpected error in getFilterOptions:', error);
    return { brands: [], promotions: [] };
  }
}

export async function searchProducts(query: string, page: number = 1, pageSize: number = 20): Promise<{ products: TransformedProduct[], totalCount: number }> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Wrap database query with timeout
  const queryTimeout = TIMEOUT_CONFIG.DATABASE.QUERY;
  const { data, error, count } = await withTimeout(
    supabase
      .from('products')
      .select(`
        *,
        brand:brand_id (id, name, slug, logo_url, description),
        promotion:promotion_id (*)
      `, { count: 'exact' })
      .ilike('name', `%${query}%`)
      .range(from, to),
    queryTimeout,
    `Database query timed out after ${queryTimeout}ms for search: ${query}`
  );

  if (error) {
    logger.error('Error searching products', error, { 
      query,
      page,
      pageSize,
      from,
      to 
    });
    return { products: [], totalCount: 0 };
  }

  const products = (data || []).map(transformProduct);
  return { products, totalCount: count || 0 };
}


