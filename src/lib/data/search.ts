/**
 * Server-side search data access layer
 * 
 * This module provides server-side functions for search functionality
 * including product search, suggestions, and filtering.
 */

import { createCacheableSupabaseClient } from '@/lib/supabase/server'
import { createCachedFunction, cacheKeys, CACHE_DURATIONS, CACHE_TAGS } from '@/lib/cache'
import type {
  TransformedProduct,
  SearchFilters,
  SearchResult,
  PaginatedResponse,
} from './types'

/**
 * Parse price from specification string (e.g., "£329.00" -> 329.00)
 */
function parseSpecificationPrice(priceString: string | null | undefined): number | null {
  if (!priceString || typeof priceString !== 'string') return null;

  // Remove currency symbols, commas, and whitespace, then parse
  const cleanPrice = priceString.replace(/[£$€,\s]/g, '');
  const parsed = parseFloat(cleanPrice);

  return isNaN(parsed) ? null : parsed;
}

/**
 * Transform raw product data for search results
 */
function transformSearchProduct(rawProduct: any): TransformedProduct {
  // Debug: Log raw product data to inspect brand information
  console.log('Raw product data - Full object keys:', Object.keys(rawProduct));
  
  // Extract brand data from the nested structure if it exists
  const brandData = rawProduct.brand ? {
    id: rawProduct.brand.id,
    name: rawProduct.brand.name || 'Unknown Brand',
    slug: rawProduct.brand.slug || '',
    logoUrl: rawProduct.brand.logo_url || null,
    description: rawProduct.brand.description || ''
  } : null;

  console.log('Raw product data - Brand info:', {
    brand: brandData,
    brand_id: rawProduct.brand_id,
    rawBrandObject: rawProduct.brand
  });

  // Calculate minPrice from retailer offers or specifications
  const retailerOffers = (rawProduct.product_retailer_offers || []);
  const retailerPrices = retailerOffers.map((offer: any) => offer.price).filter((price: any) => price != null);
  const specificationPrice = parseSpecificationPrice(rawProduct.specifications?.price);

  let minPrice: number | null = null;
  if (retailerPrices.length > 0) {
    minPrice = Math.min(...retailerPrices);
  } else if (specificationPrice !== null) {
    minPrice = specificationPrice;
  }

  // Format promotion data if it exists
  const promotionData = rawProduct.promotion_id ? {
    id: rawProduct.promotion_id,
    title: rawProduct.promotion?.title || '',
    description: rawProduct.promotion?.description || '',
    maxCashbackAmount: rawProduct.promotion?.max_cashback_amount || 0,
    purchaseStartDate: rawProduct.promotion?.purchase_start_date || '',
    purchaseEndDate: rawProduct.promotion?.purchase_end_date || '',
    claimStartOffsetDays: rawProduct.promotion?.claim_start_offset_days || 0,
    claimWindowDays: rawProduct.promotion?.claim_window_days || 0,
    termsUrl: rawProduct.promotion?.terms_url || '',
    termsDescription: rawProduct.promotion?.terms_description || '',
    status: rawProduct.promotion?.status || 'active',
    isFeatured: rawProduct.promotion?.is_featured || false
  } : null;

  return {
    id: rawProduct.id,
    name: rawProduct.name,
    slug: rawProduct.slug,
    description: rawProduct.description || '',
    images: rawProduct.images || [],
    specifications: rawProduct.specifications || null,
    status: rawProduct.status || 'active',
    isFeatured: rawProduct.is_featured || false,
    isSponsored: rawProduct.is_sponsored || false,
    cashbackAmount: rawProduct.cashback_amount,
    minPrice: minPrice,
    modelNumber: rawProduct.model_number || '',
    createdAt: rawProduct.created_at,
    updatedAt: rawProduct.updated_at,
    brand: brandData,
    category: rawProduct.category || null,
    promotion: promotionData,
    retailerOffers: (rawProduct.product_retailer_offers || []).map((offer: any) => ({
      id: offer.id,
      retailer: {
        id: offer.retailer?.id || '',
        name: offer.retailer?.name || '',
        logoUrl: offer.retailer?.logo_url || null,
        websiteUrl: offer.retailer?.website_url || null,
      },
      price: offer.price,
      stockStatus: offer.stock_status || 'unknown',
      url: offer.url || null,
      createdAt: offer.created_at,
    })),
  }
}

/**
 * Search products with advanced filtering and sorting
 */
async function _searchProducts(
  filters: SearchFilters,
  page = 1,
  limit = 20
): Promise<SearchResult> {
  try {
    const supabase = createCacheableSupabaseClient()
    const offset = (page - 1) * limit

    let query = supabase
      .from('products')
      .select(`
        *,
        brand:brand_id (
          id,
          name,
          slug,
          logo_url
        ),
        category:category_id (
          id,
          name,
          slug
        ),
        promotion:promotion_id (
          id,
          title,
          max_cashback_amount,
          purchase_end_date
        ),
        product_retailer_offers (
          id,
          price,
          stock_status,
          url,
          retailer:retailer_id (
            id,
            name,
            logo_url
          )
        )
      `, { count: 'exact' })

    // Apply search query
    if (filters.query && filters.query.trim()) {
      const searchTerm = filters.query.trim()
      query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
    }

    // Apply filters
    if (filters.brand) {
      query = query.eq('brand_id', filters.brand)
    }
    if (filters.category) {
      query = query.eq('category_id', filters.category)
    }
    if (filters.minPrice !== undefined) {
      // This would require a join with product_retailer_offers
      // For now, we'll implement basic filtering
    }
    if (filters.maxPrice !== undefined) {
      // This would require a join with product_retailer_offers
      // For now, we'll implement basic filtering
    }

    // Always filter for active products
    query = query.eq('status', 'active')

    // Apply sorting
    switch (filters.sortBy) {
      case 'price_asc':
        // Would need to join with offers for price sorting
        query = query.order('name', { ascending: true })
        break
      case 'price_desc':
        // Would need to join with offers for price sorting
        query = query.order('name', { ascending: false })
        break
      case 'newest':
        query = query.order('created_at', { ascending: false })
        break
      case 'featured':
        query = query.order('is_featured', { ascending: false })
          .order('created_at', { ascending: false })
        break
      case 'relevance':
      default:
        // For text search, order by relevance (simplified)
        if (filters.query) {
          query = query.order('is_featured', { ascending: false })
            .order('created_at', { ascending: false })
        } else {
          query = query.order('created_at', { ascending: false })
        }
        break
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    // Log the raw SQL query for debugging
    const { data, error, count } = await query;
    
    // Log the first raw product data before transformation
    if (data && data.length > 0) {
      console.log('First raw product from DB:', JSON.stringify({
        id: data[0].id,
        name: data[0].name,
        brand: data[0].brand,
        brand_id: data[0].brand_id,
        promotion: data[0].promotion,
        hasRetailerOffers: !!(data[0].product_retailer_offers?.length)
      }, null, 2));
    }
    
    // Log the generated SQL query (Supabase doesn't expose this directly, but we can log the query parameters)
    console.log('Search query parameters:', {
      table: 'products',
      select: '*, brand:brand_id (id, name, slug, logo_url), category:category_id (id, name, slug), promotion:promotion_id (id, title, max_cashback_amount), product_retailer_offers (id, price, stock_status, url, retailer:retailer_id (id, name, logo_url))',
      searchTerm: filters.query,
      filters: {
        brand: filters.brand,
        category: filters.category,
        minPrice: filters.minPrice,
        maxPrice: filters.maxPrice
      },
      sortBy: filters.sortBy,
      page,
      limit,
      offset
    });

    if (error) {
      console.error('Error in search query:', error)
      throw new Error(`Search failed: ${error.message}`)
    }

    // Debug: Log raw data before transformation
    console.log('Raw search results data:', JSON.stringify(data?.slice(0, 1), null, 2)); // Log first item for brevity
    
    const transformedProducts = (data || []).map(product => {
      const transformed = transformSearchProduct(product);
      // Debug: Log transformed data
      console.log('Transformed product:', {
        id: transformed.id,
        name: transformed.name,
        brand: transformed.brand,
        promotion: transformed.promotion,
        retailerOffers: transformed.retailerOffers?.length
      });
      return transformed;
    });
    
    const total = count || 0

    return {
      products: transformedProducts,
      total,
      filtersApplied: filters,
      suggestions: [], // TODO: Implement search suggestions
    }
  } catch (error) {
    console.error('Exception in searchProducts:', error)
    throw error
  }
}

/**
 * Cached version of searchProducts
 */
export const searchProducts = createCachedFunction(
  _searchProducts,
  {
    key: 'searchProducts',
    revalidate: CACHE_DURATIONS.SHORT,
    tags: [CACHE_TAGS.SEARCH, CACHE_TAGS.PRODUCTS],
  }
)

/**
 * Get search suggestions based on query
 */
async function _getSearchSuggestions(query: string, limit = 5): Promise<string[]> {
  try {
    if (!query || query.trim().length < 2) {
      return []
    }

    const supabase = createCacheableSupabaseClient()
    const searchTerm = query.trim()

    // Get product name suggestions
    const { data: productSuggestions, error: productError } = await supabase
      .from('products')
      .select('name')
      .ilike('name', `%${searchTerm}%`)
      .eq('status', 'active')
      .limit(limit)

    if (productError) {
      console.error('Error fetching product suggestions:', productError)
    }

    // Get brand name suggestions
    const { data: brandSuggestions, error: brandError } = await supabase
      .from('brands')
      .select('name')
      .ilike('name', `%${searchTerm}%`)
      .limit(limit)

    if (brandError) {
      console.error('Error fetching brand suggestions:', brandError)
    }

    // Combine and deduplicate suggestions
    const allSuggestions = [
      ...(productSuggestions || []).map((p: { name: string }) => p.name),
      ...(brandSuggestions || []).map((b: { name: string }) => b.name),
    ]

    // Remove duplicates and limit results
    const uniqueSuggestions = Array.from(new Set(allSuggestions))
      .slice(0, limit)

    return uniqueSuggestions
  } catch (error) {
    console.error('Exception in getSearchSuggestions:', error)
    return []
  }
}

/**
 * Cached version of getSearchSuggestions
 */
export const getSearchSuggestions = createCachedFunction(
  _getSearchSuggestions,
  {
    key: 'getSearchSuggestions',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.SEARCH],
  }
)

/**
 * Get popular search terms (placeholder implementation)
 */
async function _getPopularSearchTerms(limit = 10): Promise<string[]> {
  // TODO: Implement based on search analytics
  // For now, return some common terms
  return [
    'iPhone',
    'Samsung',
    'Laptop',
    'Headphones',
    'Gaming',
    'Fitness',
    'Home',
    'Fashion',
    'Beauty',
    'Electronics',
  ].slice(0, limit)
}

/**
 * Cached version of getPopularSearchTerms
 */
export const getPopularSearchTerms = createCachedFunction(
  _getPopularSearchTerms,
  {
    key: 'getPopularSearchTerms',
    revalidate: CACHE_DURATIONS.EXTENDED,
    tags: [CACHE_TAGS.SEARCH],
  }
)
