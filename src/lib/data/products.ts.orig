import type { Brand, FilterPromotion, ProductResponse, ProductFilters, TransformedProduct, PaginatedProductsResponse } from './types';
import { createCacheableSupabaseClient } from '../supabase/server';
import { calculateProductMinPrice } from '@/app/utils/product-utils';

// REPLACE your existing getProductPageData function with this one.
// It removes the dynamic import and calls functions directly.

export async function getProductPageData(idOrSlug: string): Promise<{ product: TransformedProduct; similarProducts: TransformedProduct[] } | null> {
  const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(idOrSlug);

  if (isUUID) {
    // We can call getProductWithSimilar directly because it's in the same file.
    return await getProductWithSimilar(idOrSlug);
  } else {
    // We can call these functions directly too.
    const product = await getProductBySlug(idOrSlug);
    if (!product) return null;
    
    const similarProducts = await getSimilarProducts(product.category?.id || '', product.id).catch(() => []);
    
    return { product, similarProducts };
  }
}




export async function getProducts(filters: ProductFilters = {}): Promise<PaginatedProductsResponse> {
  const supabase = createCacheableSupabaseClient();
  
  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  // Base query with count for pagination
  let query = supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (
        id, 
        title, 
        description, 
        max_cashback_amount, 
        purchase_start_date, 
        purchase_end_date, 
        claim_start_offset_days, 
        claim_window_days, 
        status, 
        is_featured
      )
    `, { count: 'exact' });

  // Apply filters
  // Temporarily disabling price filters as they reference a non-existent 'price' column
  // Prices are actually stored in product_retailer_offers or specifications
  // TODO: Reimplement price filtering using the new product-utils functions
  // when ready to properly support price-based filtering
  /*
  if (filters.minPrice !== undefined) {
    query = query.gte('price', filters.minPrice);
  }
  if (filters.maxPrice !== undefined) {
    query = query.lte('price', filters.maxPrice);
  }
  */
  
  if (filters.brandId) {
    query = query.eq('brand_id', filters.brandId);
  }
  if (filters.categoryId) {
    query = query.eq('category_id', filters.categoryId);
  }
  if (filters.promotionId) {
    query = query.eq('promotion_id', filters.promotionId);
  }
  if (filters.status) {
    query = query.eq('status', filters.status);
  }
  
  // Apply pagination
  const page = filters.page || 1;
  const pageSize = filters.pageSize || 20;
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    throw error;
  }

  // Transform products to match the TransformedProduct type
  const transformedProducts = (data || []).map((product: any) => ({
    ...product,
    cashbackAmount: product.cashback_amount || 0,
    isFeatured: product.is_featured || false,
    isSponsored: product.is_sponsored || false,
    modelNumber: product.model_number || '',
    images: product.images || [],
    retailerOffers: [], // This will be populated separately if needed
    brand: product.brand ? {
      id: product.brand.id,
      name: product.brand.name,
      slug: product.brand.slug,
      logoUrl: product.brand.logo_url,
      description: product.brand.description || null,
      featured: false,
      sponsored: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } : null,
    promotion: product.promotion ? {
      id: product.promotion.id,
      title: product.promotion.title,
      description: product.promotion.description || null,
      maxCashbackAmount: product.promotion.max_cashback_amount || 0,
      purchaseStartDate: product.promotion.purchase_start_date,
      purchaseEndDate: product.promotion.purchase_end_date,
      termsUrl: null,
      termsDescription: null,
      status: product.promotion.status || 'active',
      isFeatured: product.promotion.is_featured || false,
      brand: null,
      category: null
    } : null
  }));

  return {
    product: transformedProducts,
    similarProducts: [],
    pagination: {
      page,
      limit: pageSize,
      pageSize,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / pageSize),
      hasNext: page < Math.ceil((count || 0) / pageSize),
      hasPrev: page > 1
    }
  };
}

export async function getProduct(id: string): Promise<TransformedProduct | null> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (id, title, description, max_cashback_amount, purchase_start_date, purchase_end_date, status, is_featured)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching product by ID:', error);
    return null;
  }

  if (!data) return null;

  // Transform the data to match TransformedProduct type
  return {
    ...data,
    cashbackAmount: data.cashback_amount || 0,
    isFeatured: data.is_featured || false,
    isSponsored: data.is_sponsored || false,
    modelNumber: data.model_number || '',
    images: data.images || [],
    retailerOffers: [], // This will be populated separately if needed
    brand: data.brand ? {
      id: data.brand.id,
      name: data.brand.name,
      slug: data.brand.slug,
      logoUrl: data.brand.logo_url,
      description: data.brand.description || null,
      featured: false,
      sponsored: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } : null,
    promotion: data.promotion ? {
      id: data.promotion.id,
      title: data.promotion.title,
      description: data.promotion.description || null,
      maxCashbackAmount: data.promotion.max_cashback_amount || 0,
      purchaseStartDate: data.promotion.purchase_start_date,
      purchaseEndDate: data.promotion.purchase_end_date,
      termsUrl: null,
      termsDescription: null,
      status: data.promotion.status || 'active',
      isFeatured: data.promotion.is_featured || false,
      brand: null,
      category: null
    } : null
  };
}

export async function getProductBySlug(slug: string): Promise<TransformedProduct | null> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (id, title, description, max_cashback_amount, purchase_start_date, purchase_end_date, status, is_featured)
    `)
    .eq('slug', slug)
    .single();

  if (error) {
    console.error('Error fetching product by slug:', error);
    return null;
  }

  if (!data) return null;

  // Transform the data to match TransformedProduct type
  return {
    ...data,
    cashbackAmount: data.cashback_amount || 0,
    isFeatured: data.is_featured || false,
    isSponsored: data.is_sponsored || false,
    modelNumber: data.model_number || '',
    images: data.images || [],
    retailerOffers: [], // This will be populated separately if needed
    brand: data.brand ? {
      id: data.brand.id,
      name: data.brand.name,
      slug: data.brand.slug,
      logoUrl: data.brand.logo_url,
      description: data.brand.description || null,
      featured: false,
      sponsored: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } : null,
    promotion: data.promotion ? {
      id: data.promotion.id,
      title: data.promotion.title,
      description: data.promotion.description || null,
      maxCashbackAmount: data.promotion.max_cashback_amount || 0,
      purchaseStartDate: data.promotion.purchase_start_date,
      purchaseEndDate: data.promotion.purchase_end_date,
      termsUrl: null,
      termsDescription: null,
      status: data.promotion.status || 'active',
      isFeatured: data.promotion.is_featured || false,
      brand: null,
      category: null
    } : null
  };
}

export async function getProductWithSimilar(id: string): Promise<{ product: TransformedProduct; similarProducts: TransformedProduct[] } | null> {
  const product = await getProduct(id);
  if (!product) return null;

  const similarProducts = await getSimilarProducts(product.category?.id || '', product.id).catch(() => []);

  return { product, similarProducts };
}

export async function getSimilarProducts(categoryId: string, productId: string): Promise<TransformedProduct[]> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      promotion:promotion_id (id, title, description, max_cashback_amount, purchase_start_date, purchase_end_date, status, is_featured),
      retailer_offers:product_retailer_offers(*)
    `)
    .eq('category_id', categoryId)
    .neq('id', productId)
    .limit(10);

  if (error) {
    console.error('Error fetching similar products:', error);
    return [];
  }

  // Transform data to ensure retailerOffers is always an array and map cashbackAmount and other fields
  return (data || []).map((product: any) => ({
    ...product,
    cashbackAmount: product.cashback_amount || 0,
    retailerOffers: product.retailer_offers || [],
    brand: product.brand ? {
      id: product.brand.id,
      name: product.brand.name,
      slug: product.brand.slug,
      logoUrl: product.brand.logo_url,
      description: product.brand.description || null,
      featured: false,
      sponsored: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } : null,
    promotion: product.promotion ? {
      id: product.promotion.id,
      title: product.promotion.title,
      description: product.promotion.description || null,
      maxCashbackAmount: product.promotion.max_cashback_amount || 0,
      purchaseStartDate: product.promotion.purchase_start_date,
      purchaseEndDate: product.promotion.purchase_end_date,
      termsUrl: null,
      termsDescription: null,
      status: product.promotion.status || 'active',
      isFeatured: product.promotion.is_featured || false,
      brand: null,
      category: null
    } : null
  }));
}

export async function getFeaturedProducts(limit: number = 10): Promise<TransformedProduct[]> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      category:category_id (id, name, slug, parent_id, featured, sponsored),
      promotion:promotion_id (id, title, description, max_cashback_amount, purchase_start_date, purchase_end_date, status, is_featured)
    `)
    .eq('is_featured', true)
    .limit(limit);

  if (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }

  if (!data) return [];

  // Transform each product to match the TransformedProduct type
  return data.map((product: any) => ({
    ...product,
    cashbackAmount: product.cashback_amount || 0,
    isFeatured: product.is_featured || false,
    isSponsored: product.is_sponsored || false,
    modelNumber: product.model_number || '',
    images: product.images || [],
    retailerOffers: [], // This will be populated separately if needed
    brand: product.brand ? {
      id: product.brand.id,
      name: product.brand.name,
      slug: product.brand.slug,
      logoUrl: product.brand.logo_url,
      description: product.brand.description || null,
      featured: false,
      sponsored: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } : null,
    promotion: product.promotion ? {
      id: product.promotion.id,
      title: product.promotion.title,
      description: product.promotion.description || null,
      maxCashbackAmount: product.promotion.max_cashback_amount || 0,
      purchaseStartDate: product.promotion.purchase_start_date,
      purchaseEndDate: product.promotion.purchase_end_date,
      termsUrl: null,
      termsDescription: null,
      status: product.promotion.status || 'active',
      isFeatured: product.promotion.is_featured || false,
      brand: null,
      category: null
    } : null
  }));
}

export async function getFilterOptions(): Promise<{
  brands: Brand[];
  promotions: FilterPromotion[];
}> {
  try {
    const supabase = createCacheableSupabaseClient();

    if (!supabase) {
      console.error('Failed to initialize Supabase client');
      return { brands: [], promotions: [] };
    }

    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('*')
      .eq('status', 'active')
      .order('name', { ascending: true });

    if (brandsError) {
      console.error('Error fetching brands:', brandsError);
      return { brands: [], promotions: [] };
    }

    const { data: promotions, error: promotionsError } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brands!inner(*)
      `)
      .eq('status', 'active')
      .eq('brand.status', 'active')
      .order('title', { ascending: true });

    if (promotionsError) {
      console.error('Error fetching promotions:', promotionsError);
      return { brands: [], promotions: [] };
    }

    const transformedPromotions = promotions.map((promo: any) => ({
      ...promo,
      brand_name: promo.brand?.name || 'Unknown Brand',
    }));

    return {
      brands: brands || [],
      promotions: transformedPromotions || [],
    };
  } catch (error) {
    console.error('Unexpected error in getFilterOptions:', error);
    return { brands: [], promotions: [] };
  }
}

export async function searchProducts(query: string): Promise<TransformedProduct[]> {
  const supabase = createCacheableSupabaseClient();

  if (!supabase) {
    throw new Error('Failed to initialize Supabase client');
  }

  const { data, error } = await supabase
    .from('products')
    .select('*')
    .ilike('name', `%${query}%`)
    .limit(20);

  if (error) {
    console.error('Error searching products:', error);
    return [];
  }

  return data || [];
}

/**
 * Transforms raw product data from the database into a normalized format
 * Handles both snake_case and camelCase field names
 * Calculates derived fields like claim period
 */
export function transformProduct(product: any): TransformedProduct | null {
  if (!product) return product;

  if (product.promotion) {
    const promo = product.promotion;
    // Support both camelCase and snake_case
    const claimStartOffsetDays = promo.claimStartOffsetDays ?? promo.claim_start_offset_days;
    const claimWindowDays = promo.claimWindowDays ?? promo.claim_window_days;
    const purchaseEndDate = promo.purchaseEndDate ?? promo.purchase_end_date;

    let claimPeriod = null;
    if (purchaseEndDate && claimStartOffsetDays != null && claimWindowDays) {
      try {
        const startDate = new Date(purchaseEndDate);
        startDate.setDate(startDate.getDate() + claimStartOffsetDays);
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + claimWindowDays);
        const formatDate = (date: Date) => new Intl.DateTimeFormat('en-GB', { day: 'numeric', month: 'long', year: 'numeric' }).format(date);
        claimPeriod = {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          formatted: `${formatDate(startDate)} - ${formatDate(endDate)}`
        };
      } catch (error) {
        console.error('Error calculating claim period:', error);
      }
    }

    // Return transformed product with normalized promotion data
    return {
      ...product,
      promotion: {
        ...promo,
        claim_start_offset_days: claimStartOffsetDays,
        claim_window_days: claimWindowDays,
        purchase_start_date: promo.purchaseStartDate ?? promo.purchase_start_date,
        purchase_end_date: purchaseEndDate,
        max_cashback_amount: promo.maxCashbackAmount ?? promo.max_cashback_amount,
        terms_url: promo.termsUrl ?? promo.terms_url,
        terms_description: promo.termsDescription ?? promo.terms_description,
        brand_id: promo.brandId ?? promo.brand_id,
        category_id: promo.categoryId ?? promo.category_id,
        is_featured: promo.isFeatured ?? promo.is_featured ?? false,
        created_at: promo.createdAt ?? promo.created_at,
        claimPeriod,
      },
    };
  }

  // If no promotion, just return normalized product
  return {
    ...product,
    promotion: null,
  };
}

