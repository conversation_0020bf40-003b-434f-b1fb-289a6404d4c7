/**
 * Utility functions for handling product images
 */

/**
 * Constructs the full URL for a product image
 * @param image - The image path from the database
 * @param supabaseUrl - The Supabase URL from environment variables
 * @returns The complete URL for the image
 */
export function getProductImageUrl(image: string, supabaseUrl: string): string {
    // If it's already a full URL, return it as is
    if (image.startsWith('http')) {
        return image;
    }

    // Construct Supabase storage URL
    return `${supabaseUrl}/storage/v1/object/public/${image}`;
}

/**
 * Gets a fallback image URL if the main image fails to load
 * @param brandLogoUrl - Optional URL for the brand's logo
 * @param productName - Name of the product for the placeholder
 * @returns A fallback image URL
 */
export function getFallbackImageUrl(brandLogoUrl?: string, productName?: string): string {
    return brandLogoUrl || 
        `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(productName || 'Product Image')}`;
}

/**
 * Creates an error handler for image loading
 * @param onError - Optional callback for additional error handling
 * @param debugInfo - Optional debug information to log
 * @returns A function to handle image loading errors
 */
export function createImageErrorHandler(
    onError?: () => void,
    debugInfo?: Record<string, any>
): (e: React.SyntheticEvent<HTMLImageElement, Event>) => void {
    return (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        const target = e.target as HTMLImageElement;
        console.error('Image load error:', {
            attemptedSrc: target.src,
            ...debugInfo
        });
        onError?.();
    };
}
