// src/lib/sitemapValidator.ts
// Sitemap validation and testing utilities for SEO compliance

interface SitemapValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    stats: {
      totalUrls: number;
      validUrls: number;
      duplicateUrls: number;
      invalidUrls: number;
      sizeKB: number;
    };
    urlTests: {
      url: string;
      isValid: boolean;
      issues: string[];
    }[];
  }
  
  interface SitemapURL {
    loc: string;
    lastmod?: string;
    changefreq?: string;
    priority?: string;
  }
  
  /**
   * Sitemap Validator for SEO compliance testing
   */
  export class SitemapValidator {
    private baseUrl: string;
    private maxUrls = 50000; // Google's limit
    private maxSizeKB = 50 * 1024; // 50MB limit
  
    constructor(baseUrl: string = 'http://localhost:3000') {
      this.baseUrl = baseUrl;
    }
  
    /**
     * Validate sitemap.xml file
     */
    async validateSitemap(sitemapUrl: string = '/sitemap.xml'): Promise<SitemapValidationResult> {
      const errors: string[] = [];
      const warnings: string[] = [];
      const urlTests: { url: string; isValid: boolean; issues: string[] }[] = [];
  
      try {
        const response = await fetch(`${this.baseUrl}${sitemapUrl}`);
        
        if (!response.ok) {
          errors.push(`Sitemap not accessible: ${response.status} ${response.statusText}`);
          return this.createErrorResult(errors, warnings, urlTests);
        }
  
        const contentType = response.headers.get('content-type');
        if (!contentType?.includes('xml')) {
          warnings.push(`Sitemap content-type is "${contentType}", should be "application/xml" or "text/xml"`);
        }
  
        const sitemapContent = await response.text();
        const sizeKB = new Blob([sitemapContent]).size / 1024;
  
        if (sizeKB > this.maxSizeKB) {
          errors.push(`Sitemap too large: ${sizeKB.toFixed(2)}KB (max: ${this.maxSizeKB}KB)`);
        }
  
        // Parse XML and extract URLs
        const urls = this.parseSitemapXML(sitemapContent);
        
        if (urls.length === 0) {
          errors.push('No URLs found in sitemap');
          return this.createErrorResult(errors, warnings, urlTests);
        }
  
        if (urls.length > this.maxUrls) {
          errors.push(`Too many URLs: ${urls.length} (max: ${this.maxUrls})`);
        }
  
        // Validate individual URLs
        const duplicateUrls = this.findDuplicateUrls(urls);
        let validUrls = 0;
        let invalidUrls = 0;
  
        for (const url of urls.slice(0, 100)) { // Test first 100 URLs to avoid overwhelming
          const urlTest = await this.validateUrl(url);
          urlTests.push(urlTest);
          
          if (urlTest.isValid) {
            validUrls++;
          } else {
            invalidUrls++;
          }
        }
  
        // Add warnings for common issues
        if (duplicateUrls.length > 0) {
          warnings.push(`Found ${duplicateUrls.length} duplicate URLs`);
        }
  
        const invalidPercentage = (invalidUrls / Math.min(urls.length, 100)) * 100;
        if (invalidPercentage > 5) {
          warnings.push(`High percentage of invalid URLs: ${invalidPercentage.toFixed(1)}%`);
        }
  
        return {
          isValid: errors.length === 0,
          errors,
          warnings,
          stats: {
            totalUrls: urls.length,
            validUrls,
            duplicateUrls: duplicateUrls.length,
            invalidUrls,
            sizeKB: Math.round(sizeKB * 100) / 100,
          },
          urlTests: urlTests.slice(0, 20), // Return first 20 for reporting
        };
  
      } catch (error) {
        errors.push(`Failed to validate sitemap: ${error}`);
        return this.createErrorResult(errors, warnings, urlTests);
      }
    }
  
    /**
     * Parse sitemap XML and extract URLs
     */
    private parseSitemapXML(xml: string): SitemapURL[] {
      const urls: SitemapURL[] = [];
      
      // Simple regex-based XML parsing (in production, use a proper XML parser)
      const urlMatches = xml.match(/<url[^>]*>[\s\S]*?<\/url>/gi) || [];
      
      urlMatches.forEach(urlBlock => {
        const locMatch = urlBlock.match(/<loc[^>]*>([^<]+)<\/loc>/i);
        const lastmodMatch = urlBlock.match(/<lastmod[^>]*>([^<]+)<\/lastmod>/i);
        const changefreqMatch = urlBlock.match(/<changefreq[^>]*>([^<]+)<\/changefreq>/i);
        const priorityMatch = urlBlock.match(/<priority[^>]*>([^<]+)<\/priority>/i);
        
        if (locMatch) {
          urls.push({
            loc: locMatch[1],
            lastmod: lastmodMatch?.[1],
            changefreq: changefreqMatch?.[1],
            priority: priorityMatch?.[1],
          });
        }
      });
  
      return urls;
    }
  
    /**
     * Find duplicate URLs in sitemap
     */
    private findDuplicateUrls(urls: SitemapURL[]): string[] {
      const seen = new Set<string>();
      const duplicates = new Set<string>();
  
      urls.forEach(url => {
        if (seen.has(url.loc)) {
          duplicates.add(url.loc);
        } else {
          seen.add(url.loc);
        }
      });
  
      return Array.from(duplicates);
    }
  
    /**
     * Validate individual URL
     */
    private async validateUrl(url: SitemapURL): Promise<{ url: string; isValid: boolean; issues: string[] }> {
      const issues: string[] = [];
      let isValid = true;
  
      try {
        // Validate URL format
        new URL(url.loc);
      } catch {
        issues.push('Invalid URL format');
        isValid = false;
      }
  
      // Validate lastmod format
      if (url.lastmod) {
        const lastmodDate = new Date(url.lastmod);
        if (isNaN(lastmodDate.getTime())) {
          issues.push('Invalid lastmod date format');
        }
      }
  
      // Validate changefreq
      if (url.changefreq) {
        const validFreqs = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
        if (!validFreqs.includes(url.changefreq)) {
          issues.push(`Invalid changefreq: ${url.changefreq}`);
        }
      }
  
      // Validate priority
      if (url.priority) {
        const priority = parseFloat(url.priority);
        if (isNaN(priority) || priority < 0 || priority > 1) {
          issues.push(`Invalid priority: ${url.priority} (must be 0.0-1.0)`);
        }
      }
  
      // Test URL accessibility (sample only to avoid overwhelming the server)
      if (Math.random() < 0.1) { // Test 10% of URLs
        try {
          const response = await fetch(url.loc, { method: 'HEAD' });
          if (!response.ok) {
            issues.push(`URL returns ${response.status}`);
            if (response.status >= 400) {
              isValid = false;
            }
          }
        } catch {
          issues.push('URL not accessible');
          isValid = false;
        }
      }
  
      return {
        url: url.loc,
        isValid: isValid && issues.length === 0,
        issues,
      };
    }
  
    /**
     * Create error result structure
     */
    private createErrorResult(
      errors: string[], 
      warnings: string[], 
      urlTests: { url: string; isValid: boolean; issues: string[] }[]
    ): SitemapValidationResult {
      return {
        isValid: false,
        errors,
        warnings,
        stats: {
          totalUrls: 0,
          validUrls: 0,
          duplicateUrls: 0,
          invalidUrls: 0,
          sizeKB: 0,
        },
        urlTests,
      };
    }
  
    /**
     * Generate sitemap validation report
     */
    generateReport(result: SitemapValidationResult): string {
      let report = '# Sitemap Validation Report\n\n';
      
      report += `## Overall Status: ${result.isValid ? '✅ PASSED' : '❌ FAILED'}\n\n`;
      
      report += '## Statistics\n';
      report += `- Total URLs: ${result.stats.totalUrls.toLocaleString()}\n`;
      report += `- Valid URLs: ${result.stats.validUrls.toLocaleString()}\n`;
      report += `- Invalid URLs: ${result.stats.invalidUrls.toLocaleString()}\n`;
      report += `- Duplicate URLs: ${result.stats.duplicateUrls.toLocaleString()}\n`;
      report += `- Sitemap Size: ${result.stats.sizeKB}KB\n\n`;
  
      if (result.errors.length > 0) {
        report += '## ❌ Errors\n';
        result.errors.forEach(error => {
          report += `- ${error}\n`;
        });
        report += '\n';
      }
  
      if (result.warnings.length > 0) {
        report += '## ⚠️ Warnings\n';
        result.warnings.forEach(warning => {
          report += `- ${warning}\n`;
        });
        report += '\n';
      }
  
      if (result.urlTests.length > 0) {
        report += '## URL Test Results (Sample)\n';
        result.urlTests.forEach(test => {
          const status = test.isValid ? '✅' : '❌';
          report += `${status} ${test.url}\n`;
          if (test.issues.length > 0) {
            test.issues.forEach(issue => {
              report += `  - ${issue}\n`;
            });
          }
        });
      }
  
      return report;
    }
  
    /**
     * Validate robots.txt file
     */
    async validateRobotsTxt(): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
      const errors: string[] = [];
      const warnings: string[] = [];
  
      try {
        const response = await fetch(`${this.baseUrl}/robots.txt`);
        
        if (!response.ok) {
          errors.push(`robots.txt not accessible: ${response.status}`);
          return { isValid: false, errors, warnings };
        }
  
        const robotsContent = await response.text();
        
        // Check for sitemap reference
        if (!robotsContent.toLowerCase().includes('sitemap:')) {
          warnings.push('robots.txt should include sitemap reference');
        }
  
        // Check for basic structure
        if (!robotsContent.toLowerCase().includes('user-agent:')) {
          errors.push('robots.txt missing User-agent directive');
        }
  
        return {
          isValid: errors.length === 0,
          errors,
          warnings,
        };
  
      } catch (error) {
        errors.push(`Failed to validate robots.txt: ${error}`);
        return { isValid: false, errors, warnings };
      }
    }
  }
  