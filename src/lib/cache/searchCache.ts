/**
 * Search Results Caching System
 * Implements intelligent caching for search results to improve performance
 */

import { TransformedProduct } from '@/lib/data/types';
import { TIMEOUT_CONFIG } from '@/lib/timeoutConfig';

interface CacheEntry {
  data: { products: TransformedProduct[], totalCount: number };
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  totalRequests: number;
  hitRate: number;
  cacheSize: number;
  oldestEntry: number;
  newestEntry: number;
}

class SearchCache {
  private cache = new Map<string, CacheEntry>();
  private maxSize = 100; // Maximum number of cached entries
  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
  private stats = {
    hits: 0,
    misses: 0,
    totalRequests: 0
  };

  /**
   * Generate cache key from search parameters
   */
  private generateKey(query: string, page: number, pageSize: number): string {
    // Normalize query for consistent caching
    const normalizedQuery = query.toLowerCase().trim();
    return `search:${normalizedQuery}:${page}:${pageSize}`;
  }

  /**
   * Check if cache entry is still valid
   */
  private isValid(entry: CacheEntry): boolean {
    return Date.now() < entry.expiresAt;
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now >= entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  /**
   * Evict least recently used entries if cache is full
   */
  private evictLRU(): void {
    if (this.cache.size < this.maxSize) return;

    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Get cached search results
   */
  get(query: string, page: number, pageSize: number): { products: TransformedProduct[], totalCount: number } | null {
    this.stats.totalRequests++;
    
    const key = this.generateKey(query, page, pageSize);
    const entry = this.cache.get(key);

    if (!entry || !this.isValid(entry)) {
      this.stats.misses++;
      if (entry) {
        this.cache.delete(key); // Remove expired entry
      }
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    return entry.data;
  }

  /**
   * Store search results in cache
   */
  set(
    query: string, 
    page: number, 
    pageSize: number, 
    data: { products: TransformedProduct[], totalCount: number },
    ttl?: number
  ): void {
    // Don't cache empty results or very large result sets
    if (!data.products.length || data.products.length > 100) {
      return;
    }

    // Don't cache if query is too short (likely to change frequently)
    if (query.trim().length < 2) {
      return;
    }

    this.cleanup();
    this.evictLRU();

    const key = this.generateKey(query, page, pageSize);
    const now = Date.now();
    const expirationTime = ttl || this.defaultTTL;

    const entry: CacheEntry = {
      data,
      timestamp: now,
      expiresAt: now + expirationTime,
      accessCount: 1,
      lastAccessed: now
    };

    this.cache.set(key, entry);
  }

  /**
   * Invalidate cache entries matching a pattern
   */
  invalidate(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    const keysToDelete: string[] = [];
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests) * 100 
      : 0;

    let oldestEntry = Date.now();
    let newestEntry = 0;

    for (const entry of this.cache.values()) {
      if (entry.timestamp < oldestEntry) {
        oldestEntry = entry.timestamp;
      }
      if (entry.timestamp > newestEntry) {
        newestEntry = entry.timestamp;
      }
    }

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      totalRequests: this.stats.totalRequests,
      hitRate: Number(hitRate.toFixed(2)),
      cacheSize: this.cache.size,
      oldestEntry: this.cache.size > 0 ? oldestEntry : 0,
      newestEntry: this.cache.size > 0 ? newestEntry : 0
    };
  }

  /**
   * Warm up cache with popular searches
   */
  async warmUp(popularQueries: string[]): Promise<void> {
    // This would typically be called with a list of popular search terms
    // For now, we'll just prepare the cache structure
    console.log(`Cache warming initiated for ${popularQueries.length} queries`);
  }

  /**
   * Clear all cache data and reset statistics
   */
  clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      totalRequests: 0
    };
  }

  /**
   * Get cache size in bytes (approximate)
   */
  getSizeEstimate(): number {
    let size = 0;
    for (const [key, entry] of this.cache.entries()) {
      // Rough estimation of memory usage
      size += key.length * 2; // String characters are 2 bytes each
      size += JSON.stringify(entry.data).length * 2;
      size += 64; // Overhead for timestamps and metadata
    }
    return size;
  }

  /**
   * Configure cache settings
   */
  configure(options: {
    maxSize?: number;
    defaultTTL?: number;
  }): void {
    if (options.maxSize !== undefined) {
      this.maxSize = options.maxSize;
    }
    if (options.defaultTTL !== undefined) {
      this.defaultTTL = options.defaultTTL;
    }
  }
}

// Global cache instance
export const searchCache = new SearchCache();

/**
 * Cached search function wrapper
 */
export async function cachedSearchProducts(
  searchFunction: (query: string, page: number, pageSize: number) => Promise<{ products: TransformedProduct[], totalCount: number }>,
  query: string,
  page: number = 1,
  pageSize: number = 20
): Promise<{ products: TransformedProduct[], totalCount: number }> {
  // Try to get from cache first
  const cached = searchCache.get(query, page, pageSize);
  if (cached) {
    console.log(`Cache hit for search: ${query} (page ${page})`);
    return cached;
  }

  // If not in cache, perform the search
  console.log(`Cache miss for search: ${query} (page ${page})`);
  const startTime = Date.now();
  
  try {
    const result = await searchFunction(query, page, pageSize);
    const duration = Date.now() - startTime;
    
    // Cache the result with dynamic TTL based on performance
    let ttl = 5 * 60 * 1000; // Default 5 minutes
    
    // Longer TTL for slower queries (they're more expensive to repeat)
    if (duration > TIMEOUT_CONFIG.SEARCH.COMPLEX / 2) {
      ttl = 10 * 60 * 1000; // 10 minutes for slow queries
    }
    
    // Shorter TTL for very fast queries (likely simple/popular)
    if (duration < 1000) {
      ttl = 2 * 60 * 1000; // 2 minutes for fast queries
    }
    
    searchCache.set(query, page, pageSize, result, ttl);
    
    return result;
  } catch (error) {
    // Don't cache errors, but log them
    console.error(`Search error for query "${query}":`, error);
    throw error;
  }
}

/**
 * Preload popular searches into cache
 */
export async function preloadPopularSearches(
  searchFunction: (query: string, page: number, pageSize: number) => Promise<{ products: TransformedProduct[], totalCount: number }>,
  popularQueries: string[]
): Promise<void> {
  const preloadPromises = popularQueries.map(async (query) => {
    try {
      await cachedSearchProducts(searchFunction, query, 1, 20);
    } catch (error) {
      console.warn(`Failed to preload search for "${query}":`, error);
    }
  });

  await Promise.allSettled(preloadPromises);
  console.log(`Preloaded ${popularQueries.length} popular searches`);
}

/**
 * Get cache performance metrics
 */
export function getCacheMetrics() {
  return {
    stats: searchCache.getStats(),
    sizeEstimate: searchCache.getSizeEstimate(),
    recommendations: getCacheRecommendations()
  };
}

/**
 * Get cache optimization recommendations
 */
function getCacheRecommendations(): string[] {
  const stats = searchCache.getStats();
  const recommendations: string[] = [];

  if (stats.hitRate < 30) {
    recommendations.push('Low cache hit rate - consider increasing TTL or cache size');
  }

  if (stats.hitRate > 80) {
    recommendations.push('High cache hit rate - consider reducing TTL to ensure freshness');
  }

  if (stats.cacheSize > 80) {
    recommendations.push('Cache is near capacity - consider increasing max size or reducing TTL');
  }

  if (stats.totalRequests > 1000 && stats.hitRate < 50) {
    recommendations.push('Consider implementing cache warming for popular queries');
  }

  return recommendations;
}
