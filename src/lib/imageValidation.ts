// src/lib/imageValidation.ts
// Image validation utilities for SEO and accessibility compliance

interface ImageValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    score: number; // 0-100
  }
  
  interface ImageValidationOptions {
    requireAltText?: boolean;
    maxAltTextLength?: number;
    minAltTextLength?: number;
    checkFileSize?: boolean;
    maxFileSizeKB?: number;
    allowedFormats?: string[];
    requireResponsiveSizes?: boolean;
  }
  
  const DEFAULT_OPTIONS: ImageValidationOptions = {
    requireAltText: true,
    maxAltTextLength: 125,
    minAltTextLength: 5,
    checkFileSize: false,
    maxFileSizeKB: 500,
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp', 'avif'],
    requireResponsiveSizes: true
  };
  
  /**
   * Validates image properties for SEO and accessibility compliance
   */
  export function validateImageSEO(
    src: string,
    alt: string,
    options: Partial<ImageValidationOptions> = {}
  ): ImageValidationResult {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 100;
  
    // Validate alt text
    if (opts.requireAltText) {
      if (!alt || alt.trim() === '') {
        errors.push('Alt text is required for accessibility');
        score -= 30;
      } else {
        // Check alt text length
        if (alt.length > opts.maxAltTextLength!) {
          warnings.push(`Alt text is too long (${alt.length} chars). Recommended: ${opts.maxAltTextLength} chars or less`);
          score -= 10;
        }
        
        if (alt.length < opts.minAltTextLength!) {
          warnings.push(`Alt text is too short (${alt.length} chars). Recommended: ${opts.minAltTextLength} chars or more`);
          score -= 5;
        }
  
        // Check for common alt text mistakes
        const lowercaseAlt = alt.toLowerCase();
        if (lowercaseAlt.includes('image of') || lowercaseAlt.includes('picture of')) {
          warnings.push('Alt text should not include "image of" or "picture of"');
          score -= 5;
        }
  
        if (lowercaseAlt === 'image' || lowercaseAlt === 'photo' || lowercaseAlt === 'picture') {
          errors.push('Alt text is too generic. Describe what the image shows');
          score -= 20;
        }
      }
    }
  
    // Validate image source
    if (!src || src.trim() === '') {
      errors.push('Image source is required');
      score -= 40;
    } else {
      // Check file format
      const fileExtension = src.split('.').pop()?.toLowerCase();
      if (fileExtension && opts.allowedFormats && !opts.allowedFormats.includes(fileExtension)) {
        warnings.push(`File format .${fileExtension} may not be optimal. Consider: ${opts.allowedFormats.join(', ')}`);
        score -= 5;
      }
  
      // Check for modern formats
      if (fileExtension && !['webp', 'avif'].includes(fileExtension)) {
        warnings.push('Consider using modern formats like WebP or AVIF for better performance');
        score -= 5;
      }
    }
  
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: Math.max(0, score)
    };
  }
  
  /**
   * Generates SEO-optimized alt text for product images
   */
  export function generateProductAltText(
    productName: string,
    brandName?: string,
    imageType: 'main' | 'thumbnail' | 'gallery' = 'main',
    imageIndex?: number,
    totalImages?: number
  ): string {
    let altText = '';
  
    // Base description
    if (brandName) {
      altText = `${productName} by ${brandName}`;
    } else {
      altText = productName;
    }
  
    // Add context based on image type
    switch (imageType) {
      case 'thumbnail':
        altText += ' thumbnail';
        break;
      case 'gallery':
        if (imageIndex !== undefined && totalImages !== undefined) {
          altText += ` - Image ${imageIndex + 1} of ${totalImages}`;
        }
        break;
    }
  
    return altText;
  }
  
  /**
   * Generates SEO-optimized alt text for brand logos
   */
  export function generateBrandAltText(brandName: string, context?: string): string {
    let altText = `${brandName} logo`;
    
    if (context) {
      altText += ` - ${context}`;
    }
    
    return altText;
  }
  
  /**
   * Validates image accessibility compliance
   */
  export function validateImageAccessibility(
    element: HTMLImageElement | null
  ): ImageValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 100;
  
    if (!element) {
      return {
        isValid: false,
        errors: ['Image element not found'],
        warnings: [],
        score: 0
      };
    }
  
    // Check alt attribute
    if (!element.alt) {
      errors.push('Missing alt attribute');
      score -= 30;
    } else if (element.alt.trim() === '') {
      errors.push('Empty alt attribute');
      score -= 25;
    }
  
    // Check loading attribute
    if (!element.loading) {
      warnings.push('Missing loading attribute. Consider adding loading="lazy" for performance');
      score -= 5;
    }
  
    // Check sizes attribute for responsive images
    if (!element.sizes && element.srcset) {
      warnings.push('Missing sizes attribute for responsive image');
      score -= 10;
    }
  
    // Check for proper dimensions
    if (!element.width || !element.height) {
      warnings.push('Missing width or height attributes. This can cause layout shift');
      score -= 10;
    }
  
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: Math.max(0, score)
    };
  }
  
  /**
   * Batch validates multiple images on a page
   */
  export function validatePageImages(): ImageValidationResult {
    const images = document.querySelectorAll('img');
    const allErrors: string[] = [];
    const allWarnings: string[] = [];
    let totalScore = 0;
    let validImages = 0;
  
    images.forEach((img, index) => {
      const result = validateImageAccessibility(img);
      
      if (result.errors.length > 0) {
        allErrors.push(`Image ${index + 1}: ${result.errors.join(', ')}`);
      }
      
      if (result.warnings.length > 0) {
        allWarnings.push(`Image ${index + 1}: ${result.warnings.join(', ')}`);
      }
      
      totalScore += result.score;
      if (result.isValid) validImages++;
    });
  
    const averageScore = images.length > 0 ? totalScore / images.length : 0;
  
    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      score: Math.round(averageScore)
    };
  }
  
  /**
   * Development helper to log image validation results
   */
  export function logImageValidation(
    src: string,
    alt: string,
    context?: string
  ): void {
    if (process.env.NODE_ENV === 'development') {
      const result = validateImageSEO(src, alt);
      
      console.group(`🖼️ Image Validation${context ? ` - ${context}` : ''}`);
      console.log('Source:', src);
      console.log('Alt text:', alt);
      console.log('Score:', `${result.score}/100`);
      
      if (result.errors.length > 0) {
        console.error('❌ Errors:', result.errors);
      }
      
      if (result.warnings.length > 0) {
        console.warn('⚠️ Warnings:', result.warnings);
      }
      
      if (result.isValid && result.warnings.length === 0) {
        console.log('✅ Image validation passed');
      }
      
      console.groupEnd();
    }
  }
  