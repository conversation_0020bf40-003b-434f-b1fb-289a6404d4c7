// src/lib/performanceOptimization.ts
// Performance optimization utilities for Core Web Vitals improvement
import React from 'react';

/**
 * Resource preloading utilities
 */
export const resourcePreloader = {
    // Preload critical CSS
    preloadCSS: (href: string) => {
      if (typeof window === 'undefined') return;
      
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      link.onload = () => {
        link.rel = 'stylesheet';
      };
      document.head.appendChild(link);
    },
  
    // Preload critical fonts
    preloadFont: (href: string, type = 'font/woff2') => {
      if (typeof window === 'undefined') return;
      
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = type;
      link.href = href;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    },
  
    // Preload critical images
    preloadImage: (src: string, sizes?: string) => {
      if (typeof window === 'undefined') return;
      
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      if (sizes) link.setAttribute('imagesizes', sizes);
      document.head.appendChild(link);
    },
  
    // Prefetch next page resources
    prefetchPage: (href: string) => {
      if (typeof window === 'undefined') return;
      
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = href;
      document.head.appendChild(link);
    },
  };
  
  /**
   * Layout shift prevention utilities
   */
  export const layoutShiftPrevention = {
    // Reserve space for images to prevent CLS
    getImagePlaceholder: (width: number, height: number) => ({
      width,
      height,
      style: {
        aspectRatio: `${width} / ${height}`,
        backgroundColor: '#f1f5f9',
        display: 'block',
      },
    }),
  
    // Reserve space for dynamic content
    getContentPlaceholder: (minHeight: number) => ({
      style: {
        minHeight: `${minHeight}px`,
        backgroundColor: '#f1f5f9',
        borderRadius: '4px',
        animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
    }),
  
    // Prevent layout shift from web fonts
    getFontDisplaySwap: () => ({
      fontDisplay: 'swap',
    }),
  };
  
  /**
   * JavaScript optimization utilities
   */
  export const jsOptimization = {
    // Debounce function for expensive operations
    debounce: <T extends (...args: any[]) => any>(
      func: T,
      wait: number,
      immediate = false
    ): T => {
      let timeout: NodeJS.Timeout | null = null;
      
      return ((...args: any[]) => {
        const later = () => {
          timeout = null;
          if (!immediate) func(...args);
        };
        
        const callNow = immediate && !timeout;
        
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        
        if (callNow) func(...args);
      }) as T;
    },
  
    // Throttle function for scroll/resize events
    throttle: <T extends (...args: any[]) => any>(
      func: T,
      limit: number
    ): T => {
      let inThrottle: boolean;
      
      return ((...args: any[]) => {
        if (!inThrottle) {
          func(...args);
          inThrottle = true;
          setTimeout(() => (inThrottle = false), limit);
        }
      }) as T;
    },
  
    // Idle callback for non-critical tasks
    runWhenIdle: (callback: () => void, timeout = 5000) => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(callback, { timeout });
      } else {
        setTimeout(callback, 1);
      }
    },
  
    // Break up long tasks
    yieldToMain: () => {
      return new Promise(resolve => {
        setTimeout(resolve, 0);
      });
    },
  };
  
  /**
   * Performance measurement utilities
   */
  export const performanceMeasurement = {
    // Mark performance milestones
    mark: (name: string) => {
      if ('performance' in window && performance.mark) {
        performance.mark(name);
      }
    },
  
    // Measure performance between marks
    measure: (name: string, startMark: string, endMark?: string) => {
      if ('performance' in window && performance.measure) {
        try {
          performance.measure(name, startMark, endMark);
          const measure = performance.getEntriesByName(name, 'measure')[0];
          return measure?.duration || 0;
        } catch (e) {
          console.warn('Performance measurement failed:', e);
          return 0;
        }
      }
      return 0;
    },
  
    // Get navigation timing
    getNavigationTiming: () => {
      if ('performance' in window && performance.getEntriesByType) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.startTime,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstByte: navigation.responseStart - navigation.requestStart,
          domInteractive: navigation.domInteractive - navigation.startTime,
          totalLoadTime: navigation.loadEventEnd - navigation.startTime,
        };
      }
      return null;
    },
  
    // Get resource timing
    getResourceTiming: (resourceName: string) => {
      if ('performance' in window && performance.getEntriesByName) {
        const resources = performance.getEntriesByName(resourceName);
        return resources.map(resource => ({
          name: resource.name,
          duration: resource.duration,
          size: (resource as PerformanceResourceTiming).transferSize || 0,
          startTime: resource.startTime,
        }));
      }
      return [];
    },
  };
  
  /**
   * Bundle optimization utilities
   */
  export const bundleOptimization = {
    // Dynamic import with error handling
    dynamicImport: async <T>(importFn: () => Promise<T>): Promise<T | null> => {
      try {
        return await importFn();
      } catch (error) {
        console.error('Dynamic import failed:', error);
        return null;
      }
    },
  
    // Lazy load component with fallback
    lazyLoadComponent: <T extends React.ComponentType<any>>(
      importFn: () => Promise<{ default: T }>,
      fallback?: React.ComponentType
    ) => {
      const LazyComponent = React.lazy(importFn);
      
      return function WrappedComponent(props: React.ComponentProps<T>) {
        const fallbackElement = fallback ? React.createElement(fallback) : React.createElement('div', null, 'Loading...');
        return React.createElement(
          React.Suspense,
          { fallback: fallbackElement },
          React.createElement(LazyComponent, props)
        );
      };
    },
  };
  
  /**
   * Core Web Vitals optimization strategies
   */
  export const coreWebVitalsOptimization = {
    // LCP optimization
    optimizeLCP: {
      preloadHeroImage: (src: string, sizes?: string) => {
        resourcePreloader.preloadImage(src, sizes);
      },
      
      preloadCriticalCSS: (href: string) => {
        resourcePreloader.preloadCSS(href);
      },
      
      removeRenderBlockingResources: () => {
        // Mark non-critical CSS as non-blocking
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        stylesheets.forEach((link) => {
          if (!link.hasAttribute('data-critical')) {
            link.setAttribute('media', 'print');
            link.addEventListener('load', () => {
              link.setAttribute('media', 'all');
            });
          }
        });
      },
    },
  
    // FID optimization
    optimizeFID: {
      deferNonCriticalJS: () => {
        // Defer non-critical JavaScript
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach((script) => {
          if (!script.hasAttribute('data-critical')) {
            script.setAttribute('defer', '');
          }
        });
      },
      
      breakUpLongTasks: async (tasks: (() => void)[]) => {
        for (const task of tasks) {
          task();
          await jsOptimization.yieldToMain();
        }
      },
      
      useWebWorkers: (script: string, data: any): Promise<any> => {
        return new Promise((resolve, reject) => {
          const worker = new Worker(script);
          worker.postMessage(data);
          worker.onmessage = (e) => {
            resolve(e.data);
            worker.terminate();
          };
          worker.onerror = reject;
        });
      },
    },
  
    // CLS optimization
    optimizeCLS: {
      reserveImageSpace: layoutShiftPrevention.getImagePlaceholder,
      reserveContentSpace: layoutShiftPrevention.getContentPlaceholder,
      
      preloadFonts: (fonts: string[]) => {
        fonts.forEach(font => resourcePreloader.preloadFont(font));
      },
      
      avoidDynamicContent: (element: HTMLElement, content: string) => {
        // Use transform instead of changing layout properties
        element.style.transform = `translateY(${content.length * 0.1}px)`;
      },
    },
  };
  
  // React is already imported at the top of the file
  