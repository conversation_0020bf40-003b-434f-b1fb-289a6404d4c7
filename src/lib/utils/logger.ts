/**
 * Structured logging utility with different log levels
 * 
 * Usage:
 * import { logger } from '@/lib/utils/logger';
 * 
 * // Basic usage
 * logger.debug('Debug message', { additional: 'data' });
 * logger.info('Info message');
 * logger.warn('Warning message', { context: 'some context' });
 * logger.error('Error message', error, { context: 'error context' });
 * 
 * // In development, logs are output to console
 * // In production, only warn and error logs are output
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  [key: string]: any;
}

class Logger {
  private readonly isProduction: boolean;
  private readonly minLevel: LogLevel;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.minLevel = this.isProduction ? 'warn' : 'debug';
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    };
    return levels[level] >= levels[this.minLevel];
  }

  private log(level: LogLevel, message: string, data?: any, error?: Error): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      ...(data && { ...data }),
      ...(error && { 
        error: {
          name: error.name,
          message: error.message,
          stack: this.isProduction ? undefined : error.stack,
        }
      })
    };

    const logMethod = console[level] || console.log;
    logMethod(JSON.stringify(entry));
  }

  debug(message: string, data?: any): void {
    this.log('debug', message, data);
  }

  info(message: string, data?: any): void {
    this.log('info', message, data);
  }

  warn(message: string, data?: any, error?: Error): void {
    this.log('warn', message, data, error);
  }

  error(message: string, error?: Error, data?: any): void {
    this.log('error', message, data, error);
  }
}

export const logger = new Logger();
