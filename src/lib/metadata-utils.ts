import { Metadata } from 'next';

import { env } from '@/env.mjs';

// Base metadata object that will be used as default for all pages
export const siteConfig = {
  name: 'RebateRay',
  description: 'Discover and compare cashback deals and rebates from top brands in the UK.',
  url: env.NEXT_PUBLIC_SITE_URL || 'https://4-2.d3q274urye85k3.amplifyapp.com/', // Use env variable or fallback
};

// Helper function to construct metadata for any page
export function constructMetadata({
  title,
  description,
  image,
  noIndex = false,
  pathname,
}: {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  pathname?: string;
}): Metadata {
  const metaTitle = title 
    ? `${title} | ${siteConfig.name}` 
    : `${siteConfig.name} - Find the Best Rebates and Cashback Reward Deals`;
  
  const metaDescription = description || siteConfig.description;
  
  // Construct canonical URL
  const url = pathname 
    ? `${siteConfig.url}${pathname}` 
    : siteConfig.url;
  
  return {
    title: metaTitle,
    description: metaDescription,
    metadataBase: new URL(siteConfig.url),
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url,
      siteName: siteConfig.name,
      images: image ? [{ url: image }] : undefined,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: image ? [image] : undefined,
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
    },
    alternates: {
      canonical: url,
    },
  };
}
