/**
 * Caching utilities for server-side data fetching
 * 
 * This module provides caching strategies using Next.js unstable_cache
 * for optimal performance and SEO benefits.
 */

import { unstable_cache } from 'next/cache'
import type { CacheConfig } from './data/types'

/**
 * Cache configuration constants
 */
export const CACHE_DURATIONS = {
  // Short-term cache for frequently changing data
  SHORT: 300, // 5 minutes
  
  // Medium-term cache for moderately stable data
  MEDIUM: 1800, // 30 minutes
  
  // Long-term cache for stable data
  LONG: 3600, // 1 hour
  
  // Extended cache for very stable data
  EXTENDED: 86400, // 24 hours
} as const

/**
 * Cache tags for organized cache invalidation
 */
export const CACHE_TAGS = {
  PRODUCTS: 'products',
  PRODUCT: 'product',
  BRANDS: 'brands',
  BRAND: 'brand',
  CATEGORIES: 'categories',
  CATEGORY: 'category',
  PROMOTIONS: 'promotions',
  PROMOTION: 'promotion',
  RETAILERS: 'retailers',
  RETAILER: 'retailer',
  SEARCH: 'search',
  FEATURED: 'featured',
} as const

/**
 * Creates a cached version of a function with optimized cache settings
 * 
 * @param fn - The function to cache
 * @param config - Cache configuration
 * @returns Cached function
 */
export function createCachedFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config: CacheConfig
): T {
  return unstable_cache(
    fn,
    [config.key],
    {
      revalidate: config.revalidate || CACHE_DURATIONS.MEDIUM,
      tags: config.tags || [],
    }
  ) as T
}

/**
 * Cache key generators for consistent cache management
 */
export const cacheKeys = {
  // Product cache keys
  product: (id: string) => `product:${id}`,
  products: (filters?: Record<string, any>) => 
    `products:${filters ? JSON.stringify(filters) : 'all'}`,
  productsByBrand: (brandId: string, page = 1, limit = 20) => 
    `products:brand:${brandId}:${page}:${limit}`,
  productsByCategory: (categoryId: string, page = 1, limit = 20) => 
    `products:category:${categoryId}:${page}:${limit}`,
  featuredProducts: (limit = 10) => `products:featured:${limit}`,
  similarProducts: (productId: string, limit = 8) => 
    `products:similar:${productId}:${limit}`,

  // Brand cache keys
  brand: (id: string) => `brand:${id}`,
  brands: (page = 1, limit = 20) => `brands:${page}:${limit}`,
  featuredBrands: (limit = 10) => `brands:featured:${limit}`,
  brandWithPromotions: (id: string) => `brand:promotions:${id}`,

  // Category cache keys
  category: (id: string) => `category:${id}`,
  categories: () => 'categories:all',
  categoryTree: () => 'categories:tree',

  // Promotion cache keys
  promotion: (id: string) => `promotion:${id}`,
  promotions: (filters?: Record<string, any>) => 
    `promotions:${filters ? JSON.stringify(filters) : 'all'}`,
  featuredPromotions: (limit = 3) => `promotions:featured:${limit}`,
  activePromotions: () => 'promotions:active',

  // Retailer cache keys
  retailer: (id: string) => `retailer:${id}`,
  retailers: (filters?: Record<string, any>, page = 1, limit = 20) =>
    `retailers:${filters ? JSON.stringify(filters) : 'all'}:${page}:${limit}`,
  featuredRetailers: (limit = 10) => `retailers:featured:${limit}`,
  retailerWithProducts: (id: string) => `retailer:products:${id}`,

  // Search cache keys
  search: (query: string, filters?: Record<string, any>) =>
    `search:${query}:${filters ? JSON.stringify(filters) : 'none'}`,
  searchSuggestions: (query: string) => `search:suggestions:${query}`,

  // Sitemap cache keys
  sitemap: () => 'sitemap:all',
  sitemapProducts: () => 'sitemap:products',
  sitemapBrands: () => 'sitemap:brands',
}

/**
 * Cache invalidation utilities
 */
export const cacheInvalidation = {
  /**
   * Invalidate all product-related caches
   */
  invalidateProducts: () => {
    // Note: In a real implementation, you would use revalidateTag
    // This is a placeholder for the cache invalidation logic
    console.log('Invalidating product caches')
  },

  /**
   * Invalidate specific product cache
   */
  invalidateProduct: (id: string) => {
    console.log(`Invalidating product cache for: ${id}`)
  },

  /**
   * Invalidate all brand-related caches
   */
  invalidateBrands: () => {
    console.log('Invalidating brand caches')
  },

  /**
   * Invalidate specific brand cache
   */
  invalidateBrand: (id: string) => {
    console.log(`Invalidating brand cache for: ${id}`)
  },

  /**
   * Invalidate search caches
   */
  invalidateSearch: () => {
    console.log('Invalidating search caches')
  },

  /**
   * Invalidate all caches (use sparingly)
   */
  invalidateAll: () => {
    console.log('Invalidating all caches')
  },
}

/**
 * Cache warming utilities for preloading important data
 */
export const cacheWarming = {
  /**
   * Warm up featured products cache
   */
  warmFeaturedProducts: async () => {
    // This would be implemented with actual data fetching functions
    console.log('Warming featured products cache')
  },

  /**
   * Warm up popular brands cache
   */
  warmPopularBrands: async () => {
    console.log('Warming popular brands cache')
  },

  /**
   * Warm up active promotions cache
   */
  warmActivePromotions: async () => {
    console.log('Warming active promotions cache')
  },
}

/**
 * Cache monitoring utilities for performance tracking
 */
export const cacheMonitoring = {
  /**
   * Log cache hit/miss statistics
   */
  logCacheStats: (key: string, hit: boolean) => {
    console.log(`Cache ${hit ? 'HIT' : 'MISS'} for key: ${key}`)
  },

  /**
   * Monitor cache performance
   */
  monitorCachePerformance: () => {
    // Implementation would track cache hit rates, response times, etc.
    console.log('Monitoring cache performance')
  },
}
