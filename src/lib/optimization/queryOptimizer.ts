/**
 * Database Query Optimization Utilities
 * Provides tools for optimizing database queries and monitoring performance
 */

import { logger } from '@/lib/utils/logger';
import { TIMEOUT_CONFIG } from '@/lib/timeoutConfig';

interface QueryMetric {
  query: string;
  duration: number;
  timestamp: number;
  success: boolean;
  error?: string;
  resultCount?: number;
}

interface QueryOptimizationSuggestion {
  type: 'index' | 'query' | 'caching' | 'pagination';
  priority: 'high' | 'medium' | 'low';
  description: string;
  impact: string;
}

class QueryOptimizer {
  private metrics: QueryMetric[] = [];
  private maxMetrics = 500;

  /**
   * Monitor a database query and collect performance metrics
   */
  async monitorQuery<T>(
    queryName: string,
    queryFunction: () => Promise<T>,
    options: {
      timeout?: number;
      logSlowQueries?: boolean;
      slowQueryThreshold?: number;
    } = {}
  ): Promise<T> {
    const {
      timeout = TIMEOUT_CONFIG.DATABASE.QUERY,
      logSlowQueries = true,
      slowQueryThreshold = 2000 // 2 seconds
    } = options;

    const startTime = performance.now();
    const timestamp = Date.now();

    try {
      // Execute query with timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Query timeout after ${timeout}ms: ${queryName}`));
        }, timeout);
      });

      const result = await Promise.race([queryFunction(), timeoutPromise]);
      const duration = performance.now() - startTime;

      // Record successful query
      this.recordMetric({
        query: queryName,
        duration,
        timestamp,
        success: true,
        resultCount: Array.isArray(result) ? result.length : undefined
      });

      // Log slow queries
      if (logSlowQueries && duration > slowQueryThreshold) {
        logger.warn('Slow query detected', {
          query: queryName,
          duration: `${duration.toFixed(2)}ms`,
          threshold: `${slowQueryThreshold}ms`
        });
      }

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Record failed query
      this.recordMetric({
        query: queryName,
        duration,
        timestamp,
        success: false,
        error: errorMessage
      });

      logger.error('Query failed', error instanceof Error ? error : new Error(String(error)), {
        query: queryName,
        duration: `${duration.toFixed(2)}ms`
      });

      throw error;
    }
  }

  /**
   * Record query metric
   */
  private recordMetric(metric: QueryMetric): void {
    this.metrics.push(metric);

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * Get query performance statistics
   */
  getQueryStats(queryName?: string): {
    totalQueries: number;
    successfulQueries: number;
    failedQueries: number;
    averageDuration: number;
    slowQueries: number;
    fastestQuery: number;
    slowestQuery: number;
  } {
    const filteredMetrics = queryName 
      ? this.metrics.filter(m => m.query === queryName)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        totalQueries: 0,
        successfulQueries: 0,
        failedQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        fastestQuery: 0,
        slowestQuery: 0
      };
    }

    const successfulQueries = filteredMetrics.filter(m => m.success);
    const failedQueries = filteredMetrics.filter(m => !m.success);
    const durations = filteredMetrics.map(m => m.duration);
    const slowQueries = filteredMetrics.filter(m => m.duration > 2000); // > 2 seconds

    return {
      totalQueries: filteredMetrics.length,
      successfulQueries: successfulQueries.length,
      failedQueries: failedQueries.length,
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      slowQueries: slowQueries.length,
      fastestQuery: Math.min(...durations),
      slowestQuery: Math.max(...durations)
    };
  }

  /**
   * Get optimization suggestions based on query performance
   */
  getOptimizationSuggestions(queryName?: string): QueryOptimizationSuggestion[] {
    const stats = this.getQueryStats(queryName);
    const suggestions: QueryOptimizationSuggestion[] = [];

    // High failure rate
    if (stats.totalQueries > 10 && stats.failedQueries / stats.totalQueries > 0.1) {
      suggestions.push({
        type: 'query',
        priority: 'high',
        description: 'High query failure rate detected',
        impact: 'Review query logic and error handling'
      });
    }

    // Slow average duration
    if (stats.averageDuration > 3000) {
      suggestions.push({
        type: 'index',
        priority: 'high',
        description: 'Queries are running slowly on average',
        impact: 'Consider adding database indexes or optimizing query structure'
      });
    }

    // Many slow queries
    if (stats.totalQueries > 5 && stats.slowQueries / stats.totalQueries > 0.2) {
      suggestions.push({
        type: 'index',
        priority: 'medium',
        description: 'High percentage of slow queries',
        impact: 'Database indexes or query optimization needed'
      });
    }

    // Caching opportunities
    if (stats.totalQueries > 20 && stats.averageDuration > 1000) {
      suggestions.push({
        type: 'caching',
        priority: 'medium',
        description: 'Frequent queries with moderate duration',
        impact: 'Implement caching to reduce database load'
      });
    }

    // Pagination suggestions
    const searchMetrics = this.metrics.filter(m => m.query.includes('search'));
    if (searchMetrics.length > 10) {
      const largeResultSets = searchMetrics.filter(m => (m.resultCount || 0) > 50);
      if (largeResultSets.length > searchMetrics.length * 0.3) {
        suggestions.push({
          type: 'pagination',
          priority: 'low',
          description: 'Large result sets detected in search queries',
          impact: 'Implement better pagination or result limiting'
        });
      }
    }

    return suggestions;
  }

  /**
   * Get top slowest queries
   */
  getSlowestQueries(limit: number = 10): QueryMetric[] {
    return [...this.metrics]
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }

  /**
   * Get query frequency analysis
   */
  getQueryFrequency(): Record<string, number> {
    const frequency: Record<string, number> = {};
    
    for (const metric of this.metrics) {
      frequency[metric.query] = (frequency[metric.query] || 0) + 1;
    }

    return frequency;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      stats: this.getQueryStats(),
      suggestions: this.getOptimizationSuggestions(),
      slowestQueries: this.getSlowestQueries(),
      queryFrequency: this.getQueryFrequency()
    }, null, 2);
  }
}

// Global optimizer instance
export const queryOptimizer = new QueryOptimizer();

/**
 * Convenience function to monitor a query
 */
export async function monitorQuery<T>(
  queryName: string,
  queryFunction: () => Promise<T>,
  options?: {
    timeout?: number;
    logSlowQueries?: boolean;
    slowQueryThreshold?: number;
  }
): Promise<T> {
  return queryOptimizer.monitorQuery(queryName, queryFunction, options);
}

/**
 * Get query performance insights
 */
export function getQueryInsights() {
  return {
    stats: queryOptimizer.getQueryStats(),
    suggestions: queryOptimizer.getOptimizationSuggestions(),
    slowestQueries: queryOptimizer.getSlowestQueries(5),
    queryFrequency: queryOptimizer.getQueryFrequency()
  };
}

/**
 * Optimize search query parameters
 */
export function optimizeSearchQuery(query: string): {
  optimizedQuery: string;
  suggestions: string[];
} {
  const suggestions: string[] = [];
  let optimizedQuery = query.trim();

  // Remove excessive whitespace
  optimizedQuery = optimizedQuery.replace(/\s+/g, ' ');

  // Suggest minimum query length
  if (optimizedQuery.length < 2) {
    suggestions.push('Query too short - consider minimum 2 characters for better performance');
  }

  // Suggest avoiding very long queries
  if (optimizedQuery.length > 100) {
    suggestions.push('Very long query - consider breaking into multiple searches');
    optimizedQuery = optimizedQuery.substring(0, 100);
  }

  // Suggest avoiding special characters that might cause issues
  const specialChars = /[<>{}[\]\\]/g;
  if (specialChars.test(optimizedQuery)) {
    suggestions.push('Special characters detected - they may affect search performance');
    optimizedQuery = optimizedQuery.replace(specialChars, '');
  }

  return {
    optimizedQuery,
    suggestions
  };
}
