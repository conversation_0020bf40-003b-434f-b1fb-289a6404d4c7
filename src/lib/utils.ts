import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export type SortOption = 'price_asc' | 'price_desc' | 'newest' | 'oldest' | 'recommended' | 'cashback_asc' | 'cashback_desc';

export const sortOptions = {
    price_asc: 'Price: Low to High',
    price_desc: 'Price: High to Low',
    newest: 'Newest Listed',
    oldest: 'Oldest Listed',
    recommended: 'Recommended',
    cashback_asc: 'Lowest Cashback',
    cashback_desc: 'Highest Cashback'
} as const;

export const defaultSortOption: SortOption = 'price_asc';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

/**
 * Calculates the final price after applying cashback
 */
export const calculatePriceAfterCashback = (price: number, cashback: number): number => {
    return price - cashback;
};

/**
 * Formats a price number to a string with 2 decimal places
 */
export const formatPrice = (price: number | null | undefined): string => {
    if (price === null || price === undefined) {
        return '£0.00';
    }
    return `£${price.toFixed(2)}`;
};

/**
 * Validates if a string is a valid UUID v4
 */
export const isValidUUID = (uuid: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
};

/**
 * Input validation and sanitization utilities
 */

/**
 * Sanitizes a string by removing potentially dangerous characters
 */
export const sanitizeString = (input: string | null | undefined, maxLength = 255): string => {
    if (!input || typeof input !== 'string') return '';

    return input
        .trim()
        .replace(/[<>\"'&]/g, '') // Remove HTML/XML dangerous characters
        .replace(/[\x00-\x1f\x7f-\x9f]/g, '') // Remove control characters
        .substring(0, maxLength);
};

/**
 * Validates and sanitizes a UUID or slug parameter
 */
export const validateIdParameter = (id: string | null | undefined): { isValid: boolean; sanitized: string; isUUID: boolean } => {
    if (!id || typeof id !== 'string') {
        return { isValid: false, sanitized: '', isUUID: false };
    }

    const sanitized = sanitizeString(id, 150);
    const isUUID = isValidUUID(sanitized);

    // For slugs, allow alphanumeric, hyphens, and underscores only
    const isValidSlug = /^[a-zA-Z0-9_-]+$/.test(sanitized) && sanitized.length >= 2;

    return {
        isValid: isUUID || isValidSlug,
        sanitized,
        isUUID
    };
};

/**
 * Validates and sanitizes pagination parameters
 */
export const validatePaginationParams = (pageParam: string | null, limitParam: string | null) => {
    const page = Math.max(1, Math.min(1000, parseInt(pageParam || '1') || 1));
    const limit = Math.max(1, Math.min(100, parseInt(limitParam || '20') || 20));

    return { page, limit };
};

/**
 * Validates and sanitizes search query
 */
export const validateSearchQuery = (query: string | null | undefined): { isValid: boolean; sanitized: string } => {
    if (!query || typeof query !== 'string') {
        return { isValid: false, sanitized: '' };
    }

    const sanitized = sanitizeString(query, 200);

    // Minimum length check
    if (sanitized.length < 1) {
        return { isValid: false, sanitized: '' };
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
        /script/i,
        /javascript/i,
        /vbscript/i,
        /onload/i,
        /onerror/i,
        /eval\(/i,
        /expression\(/i
    ];

    const hasSuspiciousContent = suspiciousPatterns.some(pattern => pattern.test(sanitized));

    return {
        isValid: !hasSuspiciousContent,
        sanitized
    };
};

/**
 * Validates filter parameters for API endpoints
 */
export const validateFilterParams = (filters: Record<string, string | null>): Record<string, string> => {
    const sanitizedFilters: Record<string, string> = {};

    Object.entries(filters).forEach(([key, value]) => {
        if (value && typeof value === 'string') {
            const sanitized = sanitizeString(value, 100);
            if (sanitized.length > 0) {
                sanitizedFilters[key] = sanitized;
            }
        }
    });

    return sanitizedFilters;
};

export async function checkProductImages(productId: string) {
    const response = await fetch(`/api/products/${productId}`);
    const data = await response.json();
    console.log('API Response:', {
        hasData: !!data.data,
        productImages: data.data?.product?.images,
        error: data.error
    });
    return data;
}
