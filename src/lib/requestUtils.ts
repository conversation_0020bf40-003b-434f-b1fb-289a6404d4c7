/**
 * Request Utilities
 * 
 * This module provides utilities for managing HTTP requests, including:
 * - Rate limiting
 * - Request cancellation
 * - Request deduplication
 * - Exponential backoff for retries
 */

// Store for active request AbortControllers
const activeRequests = new Map<string, AbortController>();

// Rate limiting configuration
const rateLimits = {
  // Default: 10 requests per 10 seconds
  default: { maxRequests: 10, timeWindow: 10000, currentRequests: 0, resetTime: Date.now() },
  // Search: 5 requests per 5 seconds
  search: { maxRequests: 5, timeWindow: 5000, currentRequests: 0, resetTime: Date.now() },
  // Products: 8 requests per 10 seconds
  products: { maxRequests: 8, timeWindow: 10000, currentRequests: 0, resetTime: Date.now() },
};

// Request history for deduplication
const requestCache = new Map<string, { timestamp: number, data: any }>();
const CACHE_TTL = 60000; // 1 minute cache TTL

/**
 * Creates a request key for deduplication and tracking
 */
export function createRequestKey(url: string, params?: Record<string, any>): string {
  if (!params) return url;
  const sortedParams = Object.keys(params).sort().map(key => `${key}=${params[key]}`).join('&');
  return `${url}?${sortedParams}`;
}

/**
 * Checks if a request is allowed based on rate limits
 */
export function checkRateLimit(category: keyof typeof rateLimits = 'default'): boolean {
  const limit = rateLimits[category] || rateLimits.default;
  const now = Date.now();
  
  // Reset counter if time window has passed
  if (now > limit.resetTime + limit.timeWindow) {
    limit.currentRequests = 0;
    limit.resetTime = now;
  }
  
  // Check if we're over the limit
  if (limit.currentRequests >= limit.maxRequests) {
    console.warn(`Rate limit exceeded for ${category}. Try again later.`);
    return false;
  }
  
  // Increment counter
  limit.currentRequests++;
  return true;
}

/**
 * Cancels any existing request with the same key
 */
export function cancelExistingRequest(requestKey: string): void {
  if (activeRequests.has(requestKey)) {
    activeRequests.get(requestKey)?.abort();
    activeRequests.delete(requestKey);
  }
}

/**
 * Checks cache for a recent identical request
 */
export function checkRequestCache(requestKey: string): any {
  const cached = requestCache.get(requestKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  return null;
}

/**
 * Updates the request cache
 */
export function updateRequestCache(requestKey: string, data: any): void {
  requestCache.set(requestKey, { timestamp: Date.now(), data });
  
  // Clean up old cache entries
  for (const [key, value] of requestCache.entries()) {
    if (Date.now() - value.timestamp > CACHE_TTL) {
      requestCache.delete(key);
    }
  }
}

/**
 * Enhanced fetch function with rate limiting and request cancellation
 */
export async function enhancedFetch(
  url: string, 
  options: RequestInit = {}, 
  category: keyof typeof rateLimits = 'default',
  enableCache = true,
  retries = 3
): Promise<Response> {
  // Create a request key for tracking
  const requestKey = createRequestKey(url, {});
  
  // Check cache if enabled
  if (enableCache) {
    const cachedData = checkRequestCache(requestKey);
    if (cachedData) {
      return new Response(JSON.stringify(cachedData), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
  
  // Cancel any existing request with the same key
  cancelExistingRequest(requestKey);
  
  // Check rate limit
  if (!checkRateLimit(category)) {
    throw new Error(`Rate limit exceeded for ${category}. Try again later.`);
  }
  
  // Create a new AbortController for this request
  const controller = new AbortController();
  activeRequests.set(requestKey, controller);
  
  try {
    // Add the signal to the options
    const fetchOptions = { ...options, signal: controller.signal };
    
    // Attempt the fetch with exponential backoff for retries
    let attempt = 0;
    let lastError: Error | null = null;
    
    while (attempt < retries) {
      try {
        const response = await fetch(url, fetchOptions);
        
        // Remove from active requests
        activeRequests.delete(requestKey);
        
        // Cache successful responses if enabled
        if (enableCache && response.ok) {
          const clonedResponse = response.clone();
          const data = await clonedResponse.json();
          updateRequestCache(requestKey, data);
        }
        
        return response;
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry if request was aborted
        if (error instanceof DOMException && error.name === 'AbortError') {
          throw error;
        }
        
        // Exponential backoff
        const backoffTime = Math.pow(2, attempt) * 100;
        await new Promise(resolve => setTimeout(resolve, backoffTime));
        
        attempt++;
      }
    }
    
    // If we've exhausted all retries, throw the last error
    throw lastError;
  } finally {
    // Clean up if something went wrong
    if (activeRequests.has(requestKey)) {
      activeRequests.delete(requestKey);
    }
  }
}

/**
 * Cancels all active requests
 */
export function cancelAllRequests(): void {
  for (const controller of activeRequests.values()) {
    controller.abort();
  }
  activeRequests.clear();
}

/**
 * Resets all rate limiters
 */
export function resetRateLimits(): void {
  const now = Date.now();
  for (const limit of Object.values(rateLimits)) {
    limit.currentRequests = 0;
    limit.resetTime = now;
  }
}
