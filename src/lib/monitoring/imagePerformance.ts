/**
 * Image Performance Monitoring System
 * Tracks image loading performance, failures, and provides insights for optimization
 */

interface ImageLoadMetric {
  url: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  error?: string;
  size?: number;
  width?: number;
  height?: number;
  isSamsung?: boolean;
  retryCount?: number;
  fallbackUsed?: boolean;
  userAgent?: string;
  timestamp: string;
}

interface ImagePerformanceStats {
  totalLoads: number;
  successfulLoads: number;
  failedLoads: number;
  averageLoadTime: number;
  samsungImageStats: {
    totalLoads: number;
    successfulLoads: number;
    failedLoads: number;
    averageLoadTime: number;
    timeoutCount: number;
  };
  fallbackUsageCount: number;
  retryStats: {
    totalRetries: number;
    successAfterRetry: number;
  };
}

class ImagePerformanceMonitor {
  private metrics: ImageLoadMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics
  private isEnabled = true;

  constructor() {
    // Disable in test environment
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'test') {
      this.isEnabled = false;
    }
  }

  /**
   * Start tracking an image load
   */
  startImageLoad(url: string): string {
    if (!this.isEnabled) return '';

    const loadId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const metric: ImageLoadMetric = {
      url,
      startTime: performance.now(),
      success: false,
      timestamp: new Date().toISOString(),
      isSamsung: this.isSamsungImage(url),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Server'
    };

    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    return loadId;
  }

  /**
   * Record successful image load
   */
  recordImageSuccess(url: string, options: {
    width?: number;
    height?: number;
    size?: number;
    retryCount?: number;
  } = {}): void {
    if (!this.isEnabled) return;

    const metric = this.findMetricByUrl(url);
    if (metric) {
      metric.endTime = performance.now();
      metric.duration = metric.endTime - metric.startTime;
      metric.success = true;
      metric.width = options.width;
      metric.height = options.height;
      metric.size = options.size;
      metric.retryCount = options.retryCount || 0;
    }

    // Log performance in development
    if (process.env.NODE_ENV === 'development' && metric?.duration) {
      console.log(`Image loaded: ${url} (${metric.duration.toFixed(2)}ms)`);
    }
  }

  /**
   * Record image load failure
   */
  recordImageFailure(url: string, error: string, options: {
    retryCount?: number;
    fallbackUsed?: boolean;
  } = {}): void {
    if (!this.isEnabled) return;

    const metric = this.findMetricByUrl(url);
    if (metric) {
      metric.endTime = performance.now();
      metric.duration = metric.endTime - metric.startTime;
      metric.success = false;
      metric.error = error;
      metric.retryCount = options.retryCount || 0;
      metric.fallbackUsed = options.fallbackUsed || false;
    }

    // Log failures
    console.warn(`Image failed to load: ${url} - ${error}`, {
      retryCount: options.retryCount,
      fallbackUsed: options.fallbackUsed
    });
  }

  /**
   * Get performance statistics
   */
  getStats(): ImagePerformanceStats {
    if (!this.isEnabled) {
      return this.getEmptyStats();
    }

    const completedMetrics = this.metrics.filter(m => m.endTime !== undefined);
    const successfulMetrics = completedMetrics.filter(m => m.success);
    const failedMetrics = completedMetrics.filter(m => !m.success);
    const samsungMetrics = completedMetrics.filter(m => m.isSamsung);
    const samsungSuccessful = samsungMetrics.filter(m => m.success);
    const samsungFailed = samsungMetrics.filter(m => !m.success);
    const samsungTimeouts = samsungFailed.filter(m => m.error?.includes('timeout') || m.error?.includes('timed out'));

    const calculateAverage = (metrics: ImageLoadMetric[]) => {
      if (metrics.length === 0) return 0;
      const total = metrics.reduce((sum, m) => sum + (m.duration || 0), 0);
      return total / metrics.length;
    };

    return {
      totalLoads: completedMetrics.length,
      successfulLoads: successfulMetrics.length,
      failedLoads: failedMetrics.length,
      averageLoadTime: calculateAverage(successfulMetrics),
      samsungImageStats: {
        totalLoads: samsungMetrics.length,
        successfulLoads: samsungSuccessful.length,
        failedLoads: samsungFailed.length,
        averageLoadTime: calculateAverage(samsungSuccessful),
        timeoutCount: samsungTimeouts.length
      },
      fallbackUsageCount: this.metrics.filter(m => m.fallbackUsed).length,
      retryStats: {
        totalRetries: this.metrics.reduce((sum, m) => sum + (m.retryCount || 0), 0),
        successAfterRetry: this.metrics.filter(m => m.success && (m.retryCount || 0) > 0).length
      }
    };
  }

  /**
   * Get detailed metrics for analysis
   */
  getDetailedMetrics(): ImageLoadMetric[] {
    return [...this.metrics];
  }

  /**
   * Get performance recommendations based on collected data
   */
  getRecommendations(): string[] {
    const stats = this.getStats();
    const recommendations: string[] = [];

    // Samsung image performance recommendations
    if (stats.samsungImageStats.totalLoads > 0) {
      const samsungFailureRate = stats.samsungImageStats.failedLoads / stats.samsungImageStats.totalLoads;
      const samsungTimeoutRate = stats.samsungImageStats.timeoutCount / stats.samsungImageStats.totalLoads;

      if (samsungFailureRate > 0.2) {
        recommendations.push('High Samsung image failure rate detected - consider implementing local image caching');
      }

      if (samsungTimeoutRate > 0.1) {
        recommendations.push('Samsung image timeouts are frequent - consider reducing timeout values or using more aggressive fallbacks');
      }

      if (stats.samsungImageStats.averageLoadTime > 5000) {
        recommendations.push('Samsung images are loading slowly - consider preloading critical images or using lower quality settings');
      }
    }

    // General performance recommendations
    if (stats.totalLoads > 0) {
      const overallFailureRate = stats.failedLoads / stats.totalLoads;
      
      if (overallFailureRate > 0.15) {
        recommendations.push('High overall image failure rate - review image URLs and implement better error handling');
      }

      if (stats.averageLoadTime > 3000) {
        recommendations.push('Average image load time is high - consider image optimization and CDN usage');
      }

      if (stats.retryStats.totalRetries > stats.totalLoads * 0.5) {
        recommendations.push('High retry rate detected - review retry logic and timeout settings');
      }
    }

    // Fallback usage recommendations
    if (stats.fallbackUsageCount > stats.totalLoads * 0.3) {
      recommendations.push('High fallback usage - consider improving primary image reliability');
    }

    return recommendations;
  }

  /**
   * Export metrics for external analysis
   */
  exportMetrics(): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      stats: this.getStats(),
      metrics: this.metrics,
      recommendations: this.getRecommendations()
    }, null, 2);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Enable or disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Private helper methods
  private findMetricByUrl(url: string): ImageLoadMetric | undefined {
    // Find the most recent metric for this URL
    for (let i = this.metrics.length - 1; i >= 0; i--) {
      if (this.metrics[i].url === url && this.metrics[i].endTime === undefined) {
        return this.metrics[i];
      }
    }
    return undefined;
  }

  private isSamsungImage(url: string): boolean {
    return url.includes('images.samsung.com');
  }

  private getEmptyStats(): ImagePerformanceStats {
    return {
      totalLoads: 0,
      successfulLoads: 0,
      failedLoads: 0,
      averageLoadTime: 0,
      samsungImageStats: {
        totalLoads: 0,
        successfulLoads: 0,
        failedLoads: 0,
        averageLoadTime: 0,
        timeoutCount: 0
      },
      fallbackUsageCount: 0,
      retryStats: {
        totalRetries: 0,
        successAfterRetry: 0
      }
    };
  }
}

// Global instance
export const imagePerformanceMonitor = new ImagePerformanceMonitor();

// Convenience functions
export const startImageLoad = (url: string) => imagePerformanceMonitor.startImageLoad(url);
export const recordImageSuccess = (url: string, options?: any) => imagePerformanceMonitor.recordImageSuccess(url, options);
export const recordImageFailure = (url: string, error: string, options?: any) => imagePerformanceMonitor.recordImageFailure(url, error, options);
export const getImageStats = () => imagePerformanceMonitor.getStats();
export const getImageRecommendations = () => imagePerformanceMonitor.getRecommendations();

// React hook for monitoring in components
export function useImagePerformanceMonitoring() {
  return {
    startImageLoad,
    recordImageSuccess,
    recordImageFailure,
    getStats: getImageStats,
    getRecommendations: getImageRecommendations
  };
}
