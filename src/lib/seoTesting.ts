// src/lib/seoTesting.ts
// SEO testing and validation utilities for automated quality assurance

interface SEOTestResult {
    passed: boolean;
    score: number;
    errors: string[];
    warnings: string[];
    details: Record<string, any>;
  }
  
  interface PageSEOTest {
    url: string;
    title: SEOTestResult;
    description: SEOTestResult;
    keywords: SEOTestResult;
    structuredData: SEOTestResult;
    images: SEOTestResult;
    performance: SEOTestResult;
    accessibility: SEOTestResult;
    overallScore: number;
  }
  
  /**
   * SEO Testing Suite for automated validation
   */
  export class SEOTester {
    private baseUrl: string;
  
    constructor(baseUrl: string = 'http://localhost:3000') {
      this.baseUrl = baseUrl;
    }
  
    /**
     * Test title tag optimization
     */
    async testTitle(url: string): Promise<SEOTestResult> {
      const errors: string[] = [];
      const warnings: string[] = [];
      let score = 100;
  
      try {
        const response = await fetch(`${this.baseUrl}${url}`);
        const html = await response.text();
        
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        const title = titleMatch?.[1] || '';
  
        if (!title) {
          errors.push('Missing title tag');
          score -= 50;
        } else {
          if (title.length < 30) {
            warnings.push(`Title too short (${title.length} chars). Recommended: 30-60 chars`);
            score -= 10;
          }
          if (title.length > 60) {
            warnings.push(`Title too long (${title.length} chars). Recommended: 30-60 chars`);
            score -= 15;
          }
          if (!title.includes('Cashback') && !title.includes('Deals')) {
            warnings.push('Title should include target keywords like "Cashback" or "Deals"');
            score -= 5;
          }
        }
  
        return {
          passed: errors.length === 0,
          score: Math.max(0, score),
          errors,
          warnings,
          details: { title, length: title.length }
        };
      } catch (error) {
        return {
          passed: false,
          score: 0,
          errors: [`Failed to test title: ${error}`],
          warnings: [],
          details: {}
        };
      }
    }
  
    /**
     * Test meta description optimization
     */
    async testDescription(url: string): Promise<SEOTestResult> {
      const errors: string[] = [];
      const warnings: string[] = [];
      let score = 100;
  
      try {
        const response = await fetch(`${this.baseUrl}${url}`);
        const html = await response.text();
        
        const descMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i);
        const description = descMatch?.[1] || '';
  
        if (!description) {
          errors.push('Missing meta description');
          score -= 50;
        } else {
          if (description.length < 120) {
            warnings.push(`Description too short (${description.length} chars). Recommended: 120-160 chars`);
            score -= 10;
          }
          if (description.length > 160) {
            warnings.push(`Description too long (${description.length} chars). Recommended: 120-160 chars`);
            score -= 15;
          }
          if (!description.toLowerCase().includes('cashback')) {
            warnings.push('Description should include target keywords');
            score -= 5;
          }
        }
  
        return {
          passed: errors.length === 0,
          score: Math.max(0, score),
          errors,
          warnings,
          details: { description, length: description.length }
        };
      } catch (error) {
        return {
          passed: false,
          score: 0,
          errors: [`Failed to test description: ${error}`],
          warnings: [],
          details: {}
        };
      }
    }
  
    /**
     * Test structured data validation
     */
    async testStructuredData(url: string): Promise<SEOTestResult> {
      const errors: string[] = [];
      const warnings: string[] = [];
      let score = 100;
  
      try {
        const response = await fetch(`${this.baseUrl}${url}`);
        const html = await response.text();
        
        const jsonLdMatches = html.match(/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>([^<]+)<\/script>/gi);
        
        if (!jsonLdMatches || jsonLdMatches.length === 0) {
          errors.push('No structured data found');
          score -= 40;
        } else {
          let validSchemas = 0;
          
          jsonLdMatches.forEach((match, index) => {
            try {
              const jsonContent = match.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');
              const schema = JSON.parse(jsonContent);
              
              if (schema['@context'] && schema['@type']) {
                validSchemas++;
                
                // Validate required fields based on schema type
                if (schema['@type'] === 'Product' && !schema.name) {
                  warnings.push(`Product schema ${index + 1} missing name`);
                  score -= 5;
                }
                if (schema['@type'] === 'Organization' && !schema.name) {
                  warnings.push(`Organization schema ${index + 1} missing name`);
                  score -= 5;
                }
              } else {
                warnings.push(`Schema ${index + 1} missing @context or @type`);
                score -= 10;
              }
            } catch (e) {
              errors.push(`Invalid JSON-LD schema ${index + 1}: ${e}`);
              score -= 15;
            }
          });
  
          if (validSchemas === 0) {
            errors.push('No valid structured data schemas found');
            score -= 30;
          }
        }
  
        return {
          passed: errors.length === 0,
          score: Math.max(0, score),
          errors,
          warnings,
          details: { 
            schemasFound: jsonLdMatches?.length || 0,
            validSchemas: jsonLdMatches?.length || 0
          }
        };
      } catch (error) {
        return {
          passed: false,
          score: 0,
          errors: [`Failed to test structured data: ${error}`],
          warnings: [],
          details: {}
        };
      }
    }
  
    /**
     * Test image optimization
     */
    async testImages(url: string): Promise<SEOTestResult> {
      const errors: string[] = [];
      const warnings: string[] = [];
      let score = 100;
  
      try {
        const response = await fetch(`${this.baseUrl}${url}`);
        const html = await response.text();
        
        const imgMatches = html.match(/<img[^>]*>/gi) || [];
        let imagesWithoutAlt = 0;
        let imagesWithoutSizes = 0;
        let imagesWithoutLoading = 0;
  
        imgMatches.forEach((img, index) => {
          if (!img.includes('alt=')) {
            imagesWithoutAlt++;
          } else {
            const altMatch = img.match(/alt=["\']([^"\']*)["\']/) ;
            const alt = altMatch?.[1] || '';
            if (!alt.trim()) {
              imagesWithoutAlt++;
            }
          }
  
          if (!img.includes('sizes=')) {
            imagesWithoutSizes++;
          }
  
          if (!img.includes('loading=')) {
            imagesWithoutLoading++;
          }
        });
  
        if (imagesWithoutAlt > 0) {
          errors.push(`${imagesWithoutAlt} images missing alt text`);
          score -= imagesWithoutAlt * 10;
        }
  
        if (imagesWithoutSizes > 0) {
          warnings.push(`${imagesWithoutSizes} images missing sizes attribute`);
          score -= imagesWithoutSizes * 2;
        }
  
        if (imagesWithoutLoading > 0) {
          warnings.push(`${imagesWithoutLoading} images missing loading attribute`);
          score -= imagesWithoutLoading * 1;
        }
  
        return {
          passed: errors.length === 0,
          score: Math.max(0, score),
          errors,
          warnings,
          details: { 
            totalImages: imgMatches.length,
            imagesWithoutAlt,
            imagesWithoutSizes,
            imagesWithoutLoading
          }
        };
      } catch (error) {
        return {
          passed: false,
          score: 0,
          errors: [`Failed to test images: ${error}`],
          warnings: [],
          details: {}
        };
      }
    }
  
    /**
     * Run comprehensive SEO test for a page
     */
    async testPage(url: string): Promise<PageSEOTest> {
      const [title, description, structuredData, images] = await Promise.all([
        this.testTitle(url),
        this.testDescription(url),
        this.testStructuredData(url),
        this.testImages(url)
      ]);
  
      // Mock performance and accessibility tests (would integrate with Lighthouse in production)
      const performance: SEOTestResult = {
        passed: true,
        score: 85,
        errors: [],
        warnings: ['Consider optimizing images for better LCP'],
        details: { lcp: 2.1, fid: 95, cls: 0.08 }
      };
  
      const accessibility: SEOTestResult = {
        passed: true,
        score: 92,
        errors: [],
        warnings: ['Some buttons could have better contrast'],
        details: { contrastRatio: 4.2 }
      };
  
      const overallScore = Math.round(
        (title.score + description.score + structuredData.score + images.score + performance.score + accessibility.score) / 6
      );
  
      return {
        url,
        title,
        description,
        keywords: { passed: true, score: 80, errors: [], warnings: [], details: {} }, // Placeholder
        structuredData,
        images,
        performance,
        accessibility,
        overallScore
      };
    }
  
    /**
     * Test multiple pages and generate report
     */
    async testSite(urls: string[]): Promise<PageSEOTest[]> {
      const results = await Promise.all(
        urls.map(url => this.testPage(url))
      );
  
      return results;
    }
  }
  