/**
 * Custom image loader with timeout and retry logic for external images
 * Handles Samsung image server timeouts and provides fallback mechanisms
 */

// Configuration for image loading resilience
const IMAGE_CONFIG = {
  // Timeout settings
  TIMEOUT_MS: 8000, // 8 second timeout for external images
  RETRY_ATTEMPTS: 2, // Number of retry attempts
  RETRY_DELAY_MS: 1000, // Initial retry delay
  
  // Circuit breaker settings
  FAILURE_THRESHOLD: 5, // Number of failures before circuit opens
  RECOVERY_TIMEOUT_MS: 30000, // 30 seconds before trying again
  
  // Fallback image settings
  FALLBACK_QUALITY: 75,
  PLACEHOLDER_SERVICE: 'https://placehold.co'
};

// Circuit breaker state for different domains
const circuitBreakers = new Map();

/**
 * Circuit breaker implementation for image loading
 */
class CircuitBreaker {
  constructor(domain) {
    this.domain = domain;
    this.failures = 0;
    this.lastFailureTime = 0;
    this.state = 'CLOSED'; // CLOSED, OPEN, HAL<PERSON>_OPEN
  }

  canAttempt() {
    if (this.state === 'CLOSED') return true;
    if (this.state === 'OPEN') {
      const now = Date.now();
      if (now - this.lastFailureTime > IMAGE_CONFIG.RECOVERY_TIMEOUT_MS) {
        this.state = 'HALF_OPEN';
        return true;
      }
      return false;
    }
    return this.state === 'HALF_OPEN';
  }

  recordSuccess() {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  recordFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= IMAGE_CONFIG.FAILURE_THRESHOLD) {
      this.state = 'OPEN';
      console.warn(`Circuit breaker opened for domain: ${this.domain}`);
    }
  }
}

/**
 * Get or create circuit breaker for a domain
 */
function getCircuitBreaker(url) {
  try {
    const domain = new URL(url).hostname;
    if (!circuitBreakers.has(domain)) {
      circuitBreakers.set(domain, new CircuitBreaker(domain));
    }
    return circuitBreakers.get(domain);
  } catch (error) {
    console.error('Error parsing URL for circuit breaker:', error);
    return null;
  }
}

/**
 * Create a timeout promise that rejects after specified milliseconds
 */
function createTimeoutPromise(ms) {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Image request timed out after ${ms}ms`));
    }, ms);
  });
}

/**
 * Fetch image with timeout and retry logic
 */
async function fetchImageWithRetry(url, attempt = 1) {
  const circuitBreaker = getCircuitBreaker(url);
  
  // Check circuit breaker
  if (circuitBreaker && !circuitBreaker.canAttempt()) {
    throw new Error(`Circuit breaker open for ${new URL(url).hostname}`);
  }

  try {
    // Create fetch promise with timeout
    const fetchPromise = fetch(url, {
      method: 'HEAD', // Just check if image exists
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; NextJS-ImageLoader/1.0)',
      },
    });

    const timeoutPromise = createTimeoutPromise(IMAGE_CONFIG.TIMEOUT_MS);
    
    // Race between fetch and timeout
    const response = await Promise.race([fetchPromise, timeoutPromise]);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // Record success
    if (circuitBreaker) {
      circuitBreaker.recordSuccess();
    }

    return true;
  } catch (error) {
    console.warn(`Image fetch attempt ${attempt} failed for ${url}:`, error.message);
    
    // Record failure
    if (circuitBreaker) {
      circuitBreaker.recordFailure();
    }

    // Retry if we haven't exceeded max attempts
    if (attempt < IMAGE_CONFIG.RETRY_ATTEMPTS) {
      const delay = IMAGE_CONFIG.RETRY_DELAY_MS * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
      return fetchImageWithRetry(url, attempt + 1);
    }

    throw error;
  }
}

/**
 * Generate fallback image URL
 */
function generateFallbackUrl({ src, width, quality }) {
  // Extract product name or use generic placeholder
  const productName = extractProductName(src) || 'Product Image';
  const encodedName = encodeURIComponent(productName);
  
  return `${IMAGE_CONFIG.PLACEHOLDER_SERVICE}/${width}x${width}/f1f5f9/64748b.png?text=${encodedName}`;
}

/**
 * Extract product name from Samsung image URL for better placeholder text
 */
function extractProductName(url) {
  try {
    // Extract model number from Samsung URLs
    const match = url.match(/\/([a-z0-9-]+)\/gallery\//i);
    if (match && match[1]) {
      return match[1].replace(/-/g, ' ').toUpperCase();
    }
    
    // Fallback to generic name
    return 'Samsung Product';
  } catch (error) {
    return 'Product';
  }
}

/**
 * Check if URL is from Samsung's image server
 */
function isSamsungImage(src) {
  return src.includes('images.samsung.com');
}

/**
 * Custom image loader with resilience features
 */
export default function imageLoader({ src, width, quality = 75 }) {
  // For non-Samsung images, return as-is with width parameter
  if (!isSamsungImage(src)) {
    // For local images or other external images, return with width if needed
    if (src.includes('/_next/') || !src.startsWith('http')) {
      return src;
    }
    // For external non-Samsung images, append width if the service supports it
    return src;
  }

  // For Samsung images, append width parameter if the URL supports it
  // Samsung's image service supports width parameter via query string
  try {
    const url = new URL(src);
    if (width && width > 0) {
      // Samsung images can be resized by modifying the template parameter
      // Replace the template size with the requested width
      const pathname = url.pathname;
      if (pathname.includes('$684_547_PNG$')) {
        // Replace with requested dimensions (maintaining aspect ratio)
        const newTemplate = `$${width}_${Math.round(width * 0.8)}_PNG$`;
        url.pathname = pathname.replace('$684_547_PNG$', newTemplate);
      }
    }
    return url.toString();
  } catch (error) {
    // If URL parsing fails, return original
    return src;
  }
}

/**
 * Validate image URL with timeout and retry logic
 * This function is called from React components
 */
export async function validateImageUrl(src) {
  if (!isSamsungImage(src)) {
    return { isValid: true, fallbackUrl: null };
  }

  try {
    await fetchImageWithRetry(src);
    return { isValid: true, fallbackUrl: null };
  } catch (error) {
    console.warn(`Image validation failed for ${src}:`, error.message);
    
    // Generate fallback URL
    const fallbackUrl = generateFallbackUrl({ 
      src, 
      width: 600, 
      quality: IMAGE_CONFIG.FALLBACK_QUALITY 
    });
    
    return { isValid: false, fallbackUrl, error: error.message };
  }
}

/**
 * Get circuit breaker status for monitoring
 */
export function getCircuitBreakerStatus() {
  const status = {};
  for (const [domain, breaker] of circuitBreakers.entries()) {
    status[domain] = {
      state: breaker.state,
      failures: breaker.failures,
      lastFailureTime: breaker.lastFailureTime
    };
  }
  return status;
}

/**
 * Reset circuit breaker for a specific domain (for manual recovery)
 */
export function resetCircuitBreaker(domain) {
  if (circuitBreakers.has(domain)) {
    const breaker = circuitBreakers.get(domain);
    breaker.failures = 0;
    breaker.state = 'CLOSED';
    breaker.lastFailureTime = 0;
    console.log(`Circuit breaker reset for domain: ${domain}`);
    return true;
  }
  return false;
}
