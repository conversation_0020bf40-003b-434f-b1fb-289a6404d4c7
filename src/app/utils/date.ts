export const DEFAULT_LOCALE = 'en-GB';
const DEFAULT_DATE_OPTIONS: Intl.DateTimeFormatOptions = {
  year: 'numeric',
  month: 'short',
  day: 'numeric',
};
const DEFAULT_TIME_OPTIONS: Intl.DateTimeFormatOptions = {
  hour: '2-digit',
  minute: '2-digit',
  hour12: true,
};

type DateFormatOptions = {
  includeTime?: boolean;
  locale?: string;
  dateOptions?: Intl.DateTimeFormatOptions;
  timeOptions?: Intl.DateTimeFormatOptions;
};

/**
 * Formats a date string or Date object into a human-readable format
 * @param date - The date to format (string, number, or Date object)
 * @param options - Formatting options
 * @returns Formatted date string
 */
export function formatDate(
  date: string | number | Date | null | undefined,
  options: DateFormatOptions = {}
): string {
  if (!date) return '';
  
  const {
    includeTime = false,
    locale = DEFAULT_LOCALE,
    dateOptions = {},
    timeOptions = {},
  } = options;

  try {
    const dateObj = new Date(date);
    if (Number.isNaN(dateObj.getTime())) return '';

    const formatOptions: Intl.DateTimeFormatOptions = {
      ...DEFAULT_DATE_OPTIONS,
      ...dateOptions,
    };

    if (includeTime) {
      Object.assign(formatOptions, {
        ...DEFAULT_TIME_OPTIONS,
        ...timeOptions,
      });
    }

    return new Intl.DateTimeFormat(locale, formatOptions).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Formats a date range into a human-readable string
 * @param startDate - The start date
 * @param endDate - The end date
 * @param options - Formatting options
 * @returns Formatted date range string
 */
export function formatDateRange(
  startDate: string | number | Date | null | undefined,
  endDate: string | number | Date | null | undefined,
  options: Omit<DateFormatOptions, 'includeTime'> = {}
): string {
  if (!startDate || !endDate) return '';
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (Number.isNaN(start.getTime()) || Number.isNaN(end.getTime())) return '';
  
  const startFormatted = formatDate(start, options);
  const endFormatted = formatDate(end, { ...options });
  
  return `${startFormatted} - ${endFormatted}`;
}

/**
 * Checks if a date is in the past
 * @param date - The date to check
 * @returns Boolean indicating if the date is in the past
 */
export function isPastDate(date: string | number | Date): boolean {
  try {
    const dateObj = new Date(date);
    if (Number.isNaN(dateObj.getTime())) return false;
    return dateObj < new Date();
  } catch (error) {
    console.error('Error checking if date is in past:', error);
    return false;
  }
}

/**
 * Gets the time remaining until a date
 * @param date - The target date
 * @returns Object with days, hours, minutes, and seconds remaining
 */
export function getTimeRemaining(date: string | number | Date) {
  try {
    const target = new Date(date);
    if (Number.isNaN(target.getTime())) return null;
    
    const now = new Date();
    const diff = target.getTime() - now.getTime();
    
    if (diff <= 0) return null;
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    return { days, hours, minutes, seconds };
  } catch (error) {
    console.error('Error getting time remaining:', error);
    return null;
  }
}
