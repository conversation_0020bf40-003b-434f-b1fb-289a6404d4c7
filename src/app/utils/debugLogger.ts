interface DebugLogger {
    log: (message: string, ...args: any[]) => void;
    warn: (message: string, ...args: any[]) => void;
    error: (message: string, ...args: any[]) => void;
}

const debugLogger: DebugLogger = {
    log: (message, ...args) => {
        if (process.env.NODE_ENV === 'development') {
            console.log(`[DEBUG] ${message}`, ...args);
        }
    },
    warn: (message, ...args) => {
        if (process.env.NODE_ENV === 'development') {
            console.warn(`[WARN] ${message}`, ...args);
        }
    },
    error: (message, ...args) => {
        if (process.env.NODE_ENV === 'development') {
            console.error(`[ERROR] ${message}`, ...args);
        }
    }
};

export default debugLogger;