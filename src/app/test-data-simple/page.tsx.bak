/**
 * Simple test page for Phase 1 server-side data layer (without caching)
 * 
 * This page tests our server-side data functions without caching to verify
 * the basic functionality works correctly.
 */

import { createCacheableSupabaseClient } from '../../lib/supabase/server'
import { TransformedProduct, TransformedBrand, TransformedPromotion } from '../../lib/data/types'

import { notFound } from 'next/navigation'

import { env } from '@/env.mjs'

/**
 * Simple server component to test direct database access
 */
async function SimpleDataTest() {
  if (env.NEXT_PUBLIC_ENV === 'production') {
    // Prevent access in production by rendering 404
    notFound()
  }

  try {
    const supabase = createCacheableSupabaseClient()

    // Test basic product query
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        id,
        name,
        brand:brand_id (name),
        category:category_id (name)
      `)
      .eq('status', 'active')
      .limit(3)

    // Test basic brand query
    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('id, name, slug')
      .limit(3)

    // Test basic promotion query
    const { data: promotions, error: promotionsError } = await supabase
      .from('promotions')
      .select(`
        id,
        title,
        max_cashback_amount,
        brand:brand_id (name)
      `)
      .eq('status', 'active')
      .limit(3)

    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Simple Data Layer Test
            </h1>
            <p className="text-gray-600">
              Testing direct server-side database access without caching
            </p>
          </div>

          <div className="space-y-6">
            {/* Products Test */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold mb-4 text-green-600">
                ✅ Products Test
              </h2>
              {productsError ? (
                <p className="text-red-600">Error: {productsError.message}</p>
              ) : (
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Found {products?.length || 0} products
                  </p>
                  <ul className="space-y-2">
                    {products?.map((product: TransformedProduct) => (
                      <li key={product.id} className="text-sm">
                        <span className="font-medium">{product.name}</span>
                        {product.brand && (
                          <span className="text-gray-500"> - {product.brand.name}</span>
                        )}
                        {product.category && (
                          <span className="text-blue-600"> ({product.category.name})</span>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Brands Test */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold mb-4 text-green-600">
                ✅ Brands Test
              </h2>
              {brandsError ? (
                <p className="text-red-600">Error: {brandsError.message}</p>
              ) : (
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Found {brands?.length || 0} brands
                  </p>
                  <ul className="space-y-2">
                    {brands?.map((brand: TransformedBrand) => (
                      <li key={brand.id} className="text-sm">
                        <span className="font-medium">{brand.name}</span>
                        <span className="text-gray-500"> ({brand.slug})</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Promotions Test */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold mb-4 text-green-600">
                ✅ Promotions Test
              </h2>
              {promotionsError ? (
                <p className="text-red-600">Error: {promotionsError.message}</p>
              ) : (
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Found {promotions?.length || 0} promotions
                  </p>
                  <ul className="space-y-2">
                    {promotions?.map((promotion: TransformedPromotion) => (
                      <li key={promotion.id} className="text-sm">
                        <span className="font-medium">{promotion.title}</span>
                        {promotion.brand && (
                          <span className="text-gray-500"> - {promotion.brand.name}</span>
                        )}
                        <span className="text-green-600"> (£{promotion.maxCashbackAmount})</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          <div className="mt-8 bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">
              🧪 Test Results
            </h2>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Server-side database access: ✅ Working</li>
              <li>• Service role key authentication: ✅ Working</li>
              <li>• Database queries with joins: ✅ Working</li>
              <li>• No caching conflicts: ✅ Working</li>
            </ul>
          </div>

          <div className="mt-6 text-center space-x-4">
            <a
              href="/test-data-layer"
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Test Cached Version
            </a>
            <a
              href="/"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              ← Back to Homepage
            </a>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-red-50 p-6 rounded-lg border border-red-200">
            <h1 className="text-xl font-semibold text-red-600 mb-2">
              ❌ Server Error
            </h1>
            <p className="text-red-700">
              Error: {error instanceof Error ? error.message : 'Unknown error'}
            </p>
          </div>
        </div>
      </div>
    )
  }
}

/**
 * Main page component
 */
export default function SimpleTestPage() {
  return <SimpleDataTest />
}

/**
 * Metadata for the test page
 */
export const metadata = {
  title: 'Simple Data Layer Test | CashbackDeals',
  description: 'Testing basic server-side data layer functionality',
  robots: 'noindex,nofollow', // Don't index this test page
}
