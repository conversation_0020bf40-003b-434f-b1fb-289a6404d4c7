
// src/app/api/search/more/route.ts
// This file defines the API endpoint for fetching additional pages of search results
// for the "Load More" functionality on the search page.

import { NextResponse } from 'next/server';
import { searchProducts } from '@/lib/data/products';
import { TransformedProduct } from '@/lib/data/types';
import { logger } from '@/lib/utils/logger';

// Default number of items per page
const DEFAULT_PAGE_SIZE = 20;

/**
 * Handles GET requests to fetch a specific page of search results.
 * This endpoint is designed to be called from the client-side to dynamically
 * load more products without a full page refresh.
 */
export async function GET(request: Request) {
  const requestStartTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 10);
  
  try {
    // Extract search parameters from the request URL
    const { searchParams, pathname } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
    const category = searchParams.get('category') || '';
    const subcategory = searchParams.get('subcategory') || '';
    const pageSize = Math.min(50, parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10));
    
    logger.info('API Request Received', {
      requestId,
      endpoint: pathname,
      method: 'GET',
      query: query ? '[REDACTED]' : '', // Redact full query in logs
      page,
      pageSize,
      category,
      subcategory,
      hasQuery: !!query,
      hasCategory: !!category,
      hasSubcategory: !!subcategory,
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
    });

    // Validate input parameters
    if (!query && !category && !subcategory) {
      logger.warn('Empty search parameters', { requestId });
      return NextResponse.json({ 
        products: [],
        totalCount: 0,
        hasMore: false,
        currentPage: 1,
        pageSize: DEFAULT_PAGE_SIZE
      });
    }

    const searchStartTime = Date.now();
    const { products, totalCount } = await searchProducts(query, page, pageSize);
    const searchDuration = Date.now() - searchStartTime;
    
    // Calculate pagination metadata
    const hasMore = (page * pageSize) < totalCount;
    const totalPages = Math.ceil(totalCount / pageSize);

    // Create response
    const response = {
      products,
      currentPage: page,
      pageSize,
      totalCount,
      totalPages,
      hasMore
    };

    // Log successful response summary
    logger.info('Search completed', {
      requestId,
      productsCount: products.length,
      currentPage: page,
      pageSize,
      totalCount,
      totalPages,
      hasMore,
      searchDurationMs: searchDuration,
      executionTime: Date.now() - requestStartTime
    });

    return NextResponse.json(response);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorForLogging = error instanceof Error ? error : new Error(String(error));
    
    logger.error('Error in search API', errorForLogging, {
      requestId,
      executionTime: Date.now() - requestStartTime
    });
    
    const responseTime = Date.now() - requestStartTime;
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: 'An error occurred while processing your request',
        requestId
      },
      { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
          'X-Response-Time': `${responseTime}ms`
        }
      }
    );
  }
}
