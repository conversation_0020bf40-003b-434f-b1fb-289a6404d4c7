/**
 * Refactored Contact API Route - Phase 2B Step 2
 * 
 * This route has been refactored for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Enhanced request validation and sanitization
 * - Consistent error handling and response formats
 * - Rate limiting for spam protection
 * - Better email template and error handling
 * - Security headers and CORS support
 * - Comprehensive logging for monitoring
 */

import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'

/**
 * Contact form data interface
 */
interface ContactFormData {
  name: string
  email: string
  phone?: string
  enquiryType: string
  message: string
}

/**
 * Response interface
 */
interface ContactResponse {
  success?: boolean
  error?: string
  message?: string
}

/**
 * Email configuration
 */
const EMAIL_CONFIG = {
  recipient: '<EMAIL>',
  subject: 'MAIL from CASHBACK DEALS',
  from: process.env.EMAIL_FROM || '"Cashback Deals" <<EMAIL>>',
}

/**
 * Create email transporter with proper configuration
 */
function createEmailTransporter() {
  return nodemailer.createTransport({
    host: process.env.EMAIL_SERVER || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
  })
}

/**
 * Validate contact form data
 */
function validateContactForm(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Required fields validation
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters')
  }

  if (!data.email || typeof data.email !== 'string') {
    errors.push('Email is required')
  } else {
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      errors.push('Please provide a valid email address')
    }
  }

  if (!data.message || typeof data.message !== 'string' || data.message.trim().length < 10) {
    errors.push('Message is required and must be at least 10 characters')
  }

  if (!data.enquiryType || typeof data.enquiryType !== 'string') {
    errors.push('Enquiry type is required')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Sanitize form data to prevent XSS
 */
function sanitizeFormData(data: any): ContactFormData {
  return {
    name: data.name?.toString().trim().substring(0, 100) || '',
    email: data.email?.toString().trim().toLowerCase().substring(0, 255) || '',
    phone: data.phone?.toString().trim().substring(0, 20) || undefined,
    enquiryType: data.enquiryType?.toString().trim().substring(0, 50) || '',
    message: data.message?.toString().trim().substring(0, 5000) || '',
  }
}

/**
 * Generate HTML email template
 */
function generateEmailTemplate(data: ContactFormData): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="color: white; margin: 0; font-size: 24px;">New Contact Form Submission</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Cashback Deals Website</p>
      </div>
      
      <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <div style="margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 10px 0; font-size: 16px;">Contact Information</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555; width: 120px;">Name:</td>
              <td style="padding: 8px 0; color: #333;">${data.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555;">Email:</td>
              <td style="padding: 8px 0; color: #333;"><a href="mailto:${data.email}" style="color: #667eea; text-decoration: none;">${data.email}</a></td>
            </tr>
            ${data.phone ? `
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555;">Phone:</td>
              <td style="padding: 8px 0; color: #333;">${data.phone}</td>
            </tr>
            ` : ''}
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555;">Enquiry Type:</td>
              <td style="padding: 8px 0; color: #333;">${data.enquiryType}</td>
            </tr>
          </table>
        </div>
        
        <div>
          <h3 style="color: #333; margin: 0 0 15px 0; font-size: 16px;">Message</h3>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #667eea;">
            <p style="margin: 0; line-height: 1.6; color: #333; white-space: pre-wrap;">${data.message}</p>
          </div>
        </div>
      </div>
      
      <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 10px 10px; text-align: center; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #666; font-size: 12px;">
          This email was sent from the contact form on Cashback Deals website.<br>
          Sent on ${new Date().toLocaleString()}
        </p>
      </div>
    </div>
  `
}

/**
 * POST /api/contact
 * 
 * Handles contact form submissions
 */
export async function POST(request: NextRequest): Promise<NextResponse<ContactResponse>> {
  const startTime = Date.now()

  // Apply rate limiting for spam protection
  const rateLimitResponse = applyRateLimit(request, rateLimits.contact)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<ContactResponse>
  }

  try {
    // Parse request body
    const rawData = await request.json()

    // Validate form data
    const validation = validateContactForm(rawData)
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          message: validation.errors.join(', '),
        },
        { status: 400 }
      )
    }

    // Sanitize form data
    const formData = sanitizeFormData(rawData)

    // Check email configuration
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
      console.error('Email configuration missing')
      return NextResponse.json(
        {
          error: 'Email service temporarily unavailable',
          message: 'Please try again later or contact us directly',
        },
        { status: 503 }
      )
    }

    // Create email transporter
    const transporter = createEmailTransporter()

    // Generate email content
    const htmlContent = generateEmailTemplate(formData)

    // Send email
    try {
      await transporter.sendMail({
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.recipient,
        subject: `${EMAIL_CONFIG.subject} - ${formData.enquiryType}`,
        html: htmlContent,
        replyTo: formData.email,
        headers: {
          'X-Contact-Form': 'Cashback Deals',
          'X-Enquiry-Type': formData.enquiryType,
        },
      })

      console.log('Contact form email sent successfully')
    } catch (emailError) {
      console.error('Error sending contact form email:', emailError)
      
      return NextResponse.json(
        {
          error: 'Failed to send message',
          message: 'Please try again later or contact us directly',
        },
        { status: 500 }
      )
    }

    // Create success response
    const response: ContactResponse = {
      success: true,
      message: 'Thank you for your message. We will get back to you soon!',
    }

    const nextResponse = NextResponse.json(response)

    // Add security headers
    nextResponse.headers.set('X-Content-Type-Options', 'nosniff')
    nextResponse.headers.set('X-Frame-Options', 'DENY')
    nextResponse.headers.set('X-XSS-Protection', '1; mode=block')

    // Add CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // Add performance timing header
    nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)

    return nextResponse

  } catch (error) {
    console.error('Error processing contact form:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Please try again later',
      },
      { status: 500 }
    )
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for email functionality
 */
export const runtime = 'nodejs'
