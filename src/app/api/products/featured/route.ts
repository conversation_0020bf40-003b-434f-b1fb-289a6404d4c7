/**
 * Refactored Featured Promotions API Route - Phase 2A Step 2
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Note: This endpoint was originally named "featured products" but actually
 * returns featured promotions. Maintaining backward compatibility.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Reduced code duplication
 */

import { NextRequest, NextResponse } from 'next/server'
import { getFeaturedPromotions } from '@/lib/data'
import type { ApiResponse, TransformedPromotion } from '@/lib/data/types'

/**
 * Legacy interface for backward compatibility
 */
interface FeaturedPromotion {
  id: string
  title: string
  description: string
  purchase_end_date: string
  brand: {
    id: string
    name: string
  }
  category: {
    name: string
  }
}

interface FeaturedPromotionsResponse {
  data: FeaturedPromotion[]
  error?: string
}

/**
 * GET /api/products/featured
 * 
 * Fetches featured promotions (maintains legacy naming for backward compatibility)
 * 
 * Query Parameters:
 * - limit: Number of promotions to return (default: 3, max: 10)
 * 
 * Returns:
 * - data: Array of featured promotions with brand and category information
 */
export async function GET(request: NextRequest): Promise<NextResponse<FeaturedPromotionsResponse>> {
  const startTime = Date.now()
  
  try {
    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const limit = Math.min(10, Math.max(1, parseInt(searchParams.get('limit') || '3')))
    
    // Fetch featured promotions using shared data layer
    const promotions = await getFeaturedPromotions(limit)
    
    // Transform to legacy format for backward compatibility
    const featuredPromotions: FeaturedPromotion[] = promotions.map(promotion => ({
      id: promotion.id,
      title: promotion.title,
      description: promotion.description || '',
      purchase_end_date: promotion.purchaseEndDate,
      brand: {
        id: promotion.brand?.id || '',
        name: promotion.brand?.name || '',
      },
      category: {
        name: promotion.category?.name || '',
      },
    }))
    
    // Create response in legacy format for backward compatibility
    const response: FeaturedPromotionsResponse = {
      data: featuredPromotions,
    }
    
    // Create Next.js response with proper caching headers
    const nextResponse = NextResponse.json(response)
    
    // Set cache headers for optimal performance
    // Featured promotions can be cached for a moderate time
    nextResponse.headers.set(
      'Cache-Control', 
      'public, s-maxage=300, stale-while-revalidate=60'
    )
    
    // Add CORS headers for API access
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')
    
    // Add performance timing header for monitoring
    nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)
    
    return nextResponse
    
  } catch (error) {
    console.error('Error in featured promotions API route:', error)
    
    // Return standardized error response in legacy format
    const errorResponse: FeaturedPromotionsResponse = {
      data: [],
      error: error instanceof Error ? error.message : 'Failed to fetch featured promotions',
    }
    
    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 */
export const revalidate = 300 // Revalidate every 5 minutes
