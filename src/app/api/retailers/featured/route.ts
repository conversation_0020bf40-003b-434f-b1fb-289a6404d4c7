/**
 * Featured Retailers API Route - Phase 2C Implementation
 * 
 * This route provides access to featured and sponsored retailers,
 * optimized for homepage and landing page usage.
 * 
 * Key features:
 * - Uses shared server-side data layer for security and performance
 * - Returns featured/sponsored retailers
 * - Configurable limit parameter
 * - Optimized for homepage/landing pages
 * - Consistent error handling and response formats
 * - Proper caching strategy
 */

import { NextRequest, NextResponse } from 'next/server'
import { getFeaturedRetailers } from '@/lib/data'
import type { ApiResponse, TransformedRetailer } from '@/lib/data/types'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'

/**
 * GET /api/retailers/featured
 * 
 * Retrieves featured and sponsored retailers
 * 
 * Query Parameters:
 * - limit: Maximum number of retailers to return (default: 10, max: 50)
 * 
 * Returns:
 * - Array of featured retailers, prioritized by sponsored status
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now()

  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.retailers)
  if (rateLimitResponse) {
    return rateLimitResponse
  }

  try {
    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const limit = Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '10')))

    // Fetch featured retailers using data layer
    const retailers = await getFeaturedRetailers(limit)

    // Prepare API response
    const response: ApiResponse<TransformedRetailer[]> = {
      data: retailers,
      error: null,
    }

    // Create response with proper headers
    const nextResponse = NextResponse.json(response, { status: 200 })

    // Set caching headers - featured retailers can be cached longer
    nextResponse.headers.set('Cache-Control', 'public, max-age=900, s-maxage=1800, stale-while-revalidate=300')
    
    // Set CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // Add performance headers
    const duration = Date.now() - startTime
    nextResponse.headers.set('X-Response-Time', `${duration}ms`)
    nextResponse.headers.set('X-Cache-Status', 'MISS')

    return nextResponse

  } catch (error) {
    console.error('Error in featured retailers API route:', error)

    const errorResponse: ApiResponse<null> = {
      data: null,
      error: error instanceof Error ? error.message : 'Internal server error',
    }

    const nextResponse = NextResponse.json(errorResponse, { status: 500 })
    
    // Set CORS headers even for errors
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // Add performance headers
    const duration = Date.now() - startTime
    nextResponse.headers.set('X-Response-Time', `${duration}ms`)

    return nextResponse
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 * Featured retailers can be cached for longer periods
 */
export const revalidate = 1800 // Revalidate every 30 minutes
