'use client'
export const runtime = 'edge';

import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

export default function AboutUsPage() {
  return (
    <div className="relative flex flex-col min-h-screen">
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
      >
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-3xl"
          >
            <h1 className="text-4xl font-bold text-primary mb-6">About Us</h1>
            <p className="text-lg text-foreground/80">
              The story behind Rebate Ray and our mission to help you find money you didn't know was yours.
            </p>
          </motion.div>
        </div>
      </motion.section>

      <div className="container py-16">
        <div className="max-w-4xl mx-auto space-y-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="prose prose-lg max-w-none space-y-6"
          >
            <h2 className="text-2xl font-bold text-primary mb-6">How RebateRay Began</h2>
            
            <p className="mb-6">
              We started RebateRay with a simple realization: we were missing out on money that was rightfully ours.
            </p>
            
            <p className="mb-6">
              A few years ago, our founder purchased a new Samsung refrigerator. Six months later, while chatting with a neighbor, they mentioned getting £300 cashback on the exact same model. Our founder was shocked - nobody had told him about this cashback offer when he bought his fridge!
            </p>
            
            <p className="mb-6">
              After digging deeper, we discovered an entire world of rebates and cashback offers that brands like Samsung, Bose, Sony, Philips, Dell  and more regularly provide. These weren't small amounts either - some offers went up to £500! The problem? This information was scattered across dozens of websites, buried in fine print, or simply not advertised prominently.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="my-10"
          >
            <div className="relative overflow-hidden rounded-xl bg-secondary/5 p-10">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-primary/10 blur-2xl"></div>
              <h3 className="text-xl font-semibold text-primary mb-6">Our Lightbulb Moment</h3>
              <p className="text-foreground/80 italic text-xl mb-6">
                "If we're missing out on these savings, how many other people are too?"
              </p>
              <p className="mt-6 text-foreground/80">
                That's when RebateRay was born. We decided to build the solution we wished we had - a single, easy-to-use hub that tracks all major cashback offers and rebates in one place.
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="prose prose-lg max-w-none space-y-6"
          >
            <h2 className="text-2xl font-bold text-primary mb-6">What We Do</h2>
            
            <p className="mb-6">
              Today, we scan hundreds of brands and manufacturers daily to find and organize the best rebate cashback or reward offers. We simplify the complex eligibility requirements and claim periods, making it crystal clear:
            </p>
            
            <ul className="list-disc pl-8 space-y-4 mb-6">
              <li>Which products qualify for cashback</li>
              <li>How much you can save</li>
              <li>Where to buy from</li>
              <li>When and how to claim your money</li>
            </ul>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="prose prose-lg max-w-none space-y-6"
          >
            <h2 className="text-2xl font-bold text-primary mb-6">Why We Do It</h2>
            
            <p className="mb-6">
              We believe everyone deserves to know about money that's available to them. Manufacturers set aside millions for these cashback programs each year, but many consumers never claim what they're entitled to simply because they don't know about it.
            </p>
            
            <p className="mb-6">
              Our mission is to change that - helping you save money on purchases you're already making and ensuring no rebate goes unclaimed.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="prose prose-lg max-w-none space-y-6"
          >
            <h2 className="text-2xl font-bold text-primary mb-6">Join Our Community</h2>
            
            <p className="mb-6">
              Thousands of smart shoppers now check Rebate Ray before making major purchases. Our target is to help our community claim back over £1 million in the first year that might otherwise have gone unclaimed.
            </p>
            
            <p className="mb-6">
              We're proud to have scratched our own itch and created something that helps people save money every day.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="rounded-xl bg-primary/5 p-10 border border-primary/10 my-10"
          >
            <h3 className="text-2xl font-semibold text-primary mb-6">Ready to stop leaving money on the table?</h3>
            <p className="mb-8 text-foreground/80 text-lg">
              Start browsing our current cashback offers and join the RebateRay community today!
            </p>
            <Link 
              href="/brands" 
              className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Browse Offers <ArrowRight className="h-4 w-4" />
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
}