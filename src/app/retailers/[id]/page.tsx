import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { constructMetadata } from '@/lib/metadata-utils';
import { getRetailerWithProducts, getRetailerBySlug } from '@/lib/data/retailers';
import { RetailerPageClient } from '@/components/pages/RetailerPageClient';
import { OrganizationStructuredData } from '@/components/seo/StructuredData';

interface RetailerPageProps {
  params: Promise<{ id: string }>;
}

// Check if string is a valid UUID
function isUUID(id: string): boolean {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
}

// Generate dynamic metadata for SEO optimization
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  try {
    const { id } = resolvedParams;
    let retailerData;
    
    if (isUUID(id)) {
      retailerData = await getRetailerWithProducts(id);
    } else {
      const retailer = await getRetailerBySlug(id);
      if (retailer) {
        retailerData = await getRetailerWithProducts(retailer.id);
      }
    }

    if (!retailerData || !retailerData.retailer) {
      return constructMetadata({
        title: 'Retailer Not Found',
        description: 'The requested retailer could not be found.',
        noIndex: true
      });
    }

    const retailer = retailerData.retailer;
    const productCount = retailerData.featuredProducts?.length || 0;

    // Create SEO-optimized metadata
    const title = `${retailer.name} Cashback Deals & Offers`;
    // const description = retailer.description
    //  ? `${retailer.description.substring(0, 155)}...`
    //  : `Shop at ${retailer.name} and earn cashback on your purchases. ${productCount > 0 ? `${productCount} products available with` : 'Discover'} exclusive cashback deals and guaranteed rewards.`;

    const keywords = [
      retailer.name,
      'cashback',
      'deals',
      'offers',
      'discounts',
      `${retailer.name} cashback`,
      'save money',
      'rewards',
      'rebates'
    ].join(', ');

    return constructMetadata({
      title,
      //description,
      image: retailer.logoUrl || undefined,
      pathname: `/retailers/${retailer.slug || retailer.id}`
    });
  } catch (error) {
    console.error('Error generating retailer metadata:', error);
    return constructMetadata({
      title: 'Retailer Not Found',
      description: 'The requested retailer could not be found.',
      noIndex: true
    });
  }
}

// Loading skeleton component for better UX during data fetching
function RetailerPageSkeleton() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Back button skeleton */}
      <div className="container py-4">
        <div className="h-6 bg-gray-300 rounded w-32 animate-pulse"></div>
      </div>

      {/* Hero section skeleton */}
      <section className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="h-64 bg-gray-300 rounded-lg animate-pulse"></div>
            <div className="space-y-4">
              <div className="h-10 bg-gray-300 rounded w-3/4 animate-pulse"></div>
              <div className="h-6 bg-gray-300 rounded animate-pulse"></div>
              <div className="h-6 bg-gray-300 rounded w-2/3 animate-pulse"></div>
              <div className="flex gap-4">
                <div className="h-6 bg-gray-300 rounded w-24 animate-pulse"></div>
                <div className="h-6 bg-gray-300 rounded w-24 animate-pulse"></div>
              </div>
              <div className="h-12 bg-gray-300 rounded w-40 animate-pulse"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Content skeleton */}
      <div className="container py-12">
        {/* Cashback info skeleton */}
        <div className="h-48 bg-gray-300 rounded-lg mb-12 animate-pulse"></div>

        {/* Products skeleton */}
        <div className="h-8 bg-gray-300 rounded w-64 mb-8 animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-64 bg-gray-300 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Main retailer page component - Server Component for optimal SEO
export default async function RetailerPage({ params }: RetailerPageProps) {
  const resolvedParams = await params;
  try {
    const { id } = resolvedParams;
    let retailerData = null;
    
    if (isUUID(id)) {
      // Fetch by UUID
      retailerData = await getRetailerWithProducts(id);
    } else {
      // Fetch by slug
      const retailer = await getRetailerBySlug(id);
      if (retailer) {
        retailerData = await getRetailerWithProducts(retailer.id);
      }
    }

    if (!retailerData || !retailerData.retailer) {
      notFound();
    }

    const { retailer, featuredProducts } = retailerData;

    return (
      <>
        {/* Organization structured data for enhanced SEO */}
        <OrganizationStructuredData
          organization={{
            id: retailer.id,
            name: retailer.name,
            logoUrl: retailer.logoUrl,
            //    description: retailer.description,
            websiteUrl: retailer.websiteUrl
          }}
          organizationType="Organization"
        />

        {/* Suspense boundary for progressive loading */}
        <Suspense fallback={<RetailerPageSkeleton />}>
          <RetailerPageClient
            retailer={retailer}
            products={featuredProducts || []}
          />
        </Suspense>
      </>
    );
  } catch (error) {
    console.error('Error loading retailer page:', error);
    notFound();
  }
}
