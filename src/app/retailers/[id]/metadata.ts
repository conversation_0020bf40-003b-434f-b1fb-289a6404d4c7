// src/app/products/[id]/metadata.ts
// This file handles dynamic metadata generation for product pages

import { constructMetadata } from '@/lib/metadata-utils';
import { Metadata, ResolvingMetadata } from 'next';
import { supabase } from '@/lib/supabase';

// Define the props for generateMetadata
type Props = {
  params: { id: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  console.log("generateMetadata called at:", new Date().toISOString());
  console.log("generateMetadata called with params:", params);
  console.time("generateMetadata");

  // Fetch the product data from Supabase
  const { data: product, error } = await supabase
    .from('products')
    .select('name, description, image_url, slug')
    .eq('id', params.id)
    .single();

  console.log("generateMetadata - fetched product:", product);
  console.log("generateMetadata - error:", error);

  // If there's an error fetching the product, return default metadata
  if (error || !product) {
    console.timeEnd("generateMetadata");
    console.log("Returning default metadata due to error or missing product");
    return constructMetadata({
      title: 'Product Not Found',
      description: 'The requested product could not be found.',
      noIndex: true, // We don't want to index 404 pages
    });
  }

  // Construct the product-specific metadata
  const metadata = constructMetadata({
    title: product.name,
    description: product.description || `Get cashback deals on ${product.name} and save on your purchase.`,
    image: product.image_url,
    pathname: `/products/${params.id}`,
  });

  console.timeEnd("generateMetadata");
  console.log("generateMetadata returning metadata:", metadata);
  return metadata;
}