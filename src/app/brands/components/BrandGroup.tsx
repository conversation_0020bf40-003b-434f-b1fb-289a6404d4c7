'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { ArrowRight } from 'lucide-react'
import { BrandWithDetails } from '@/types/brand'

interface BrandGroupProps {
  letter: string
  brands: BrandWithDetails[]
}

/**
 * Client component that renders a group of brands for a specific letter
 * Handles animations and responsive grid layout
 * 
 * @component
 * @example
 * ```tsx
 * <BrandGroup letter="A" brands={brands} />
 * ```
 * 
 * @note All brand properties should use camelCase (e.g., logoUrl, createdAt, updatedAt)
 * to match the Brand interface from '@/types/database'.
 */
export function BrandGroup({ letter, brands }: BrandGroupProps) {
  if (!brands || brands.length === 0) {
    return null
  }

  return (
    <div id={`section-${letter}`} className="scroll-mt-[136px] mb-12">
      <h2 className="text-2xl font-bold text-primary mb-6">{letter}</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {brands.map((brand) => (
          <Link 
            href={`/brands/${brand.slug}`} 
            key={brand.id}
            className="block group"
            aria-label={`View deals for ${brand.name}`}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -5 }}
              className="h-full bg-card rounded-lg border border-border overflow-hidden shadow-sm hover:shadow-md transition-all duration-200"
            >
              <div className="h-40 bg-muted/30 relative flex items-center justify-center p-4">
                {brand.logoUrl ? (
                  <Image
                    src={brand.logoUrl}
                    alt={brand.name}
                    width={160}
                    height={80}
                    className="object-contain max-h-[80%] w-auto"
                    loading="lazy"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(
                        brand.name
                      )}`
                    }}
                  />
                ) : (
                  <div className="text-2xl font-bold text-muted-foreground/40 text-center">
                    {brand.name}
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-foreground mb-2 line-clamp-2">
                  {brand.name}
                </h3>
                {brand.categories && brand.categories.length > 0 && (
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {brand.categories.map((c) => c.name).join(' • ')}
                  </p>
                )}
                <div className="flex items-center text-sm font-medium text-primary group-hover:underline">
                  View Deals
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </div>
              </div>
            </motion.div>
          </Link>
        ))}
      </div>
    </div>
  )
}
