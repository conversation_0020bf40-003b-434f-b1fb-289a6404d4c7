'use client'

import { useState, useEffect } from 'react'
import { Search } from 'lucide-react'
import { useRouter, useSearchParams } from 'next/navigation'

/**
 * Client-side search input component for filtering brands
 * Uses URL search params to maintain state across navigation
 */
export function SearchInput() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '')

  // Debounce search to prevent too many re-renders
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      const params = new URLSearchParams(searchParams)
      
      if (searchQuery) {
        params.set('q', searchQuery)
      } else {
        params.delete('q')
      }
      
      // Update URL without page reload
      router.push(`?${params.toString()}`, { scroll: false })
    }, 300)

    return () => clearTimeout(delayDebounceFn)
  }, [searchQuery, router, searchParams])

  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-foreground/70" />
      </div>
      <input
        type="text"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="block w-full pl-10 pr-3 py-3 border border-foreground/20 rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent"
        placeholder="Search brands..."
        aria-label="Search brands"
      />
    </div>
  )
}

export default SearchInput
