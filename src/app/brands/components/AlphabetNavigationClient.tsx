'use client'

import { useState, useEffect } from 'react'
import { AlphabetNavigation } from '@/components/ui/alphabet-navigation'
import { BrandWithDetails } from '@/types/brand'

interface AlphabetNavigationClientProps {
  groupedBrands: Record<string, BrandWithDetails[]>
}

/**
 * Client-side alphabet navigation component
 * Handles smooth scrolling to letter sections and highlights active letter
 */
export function AlphabetNavigationClient({ 
  groupedBrands 
}: AlphabetNavigationClientProps) {
  const [activeLetter, setActiveLetter] = useState<string>('A')
  const [isScrolling, setIsScrolling] = useState(false)

  // Handle scroll events to update active letter
  useEffect(() => {
    const handleScroll = () => {
      if (isScrolling) return
      
      const scrollPosition = window.scrollY + 150 // Offset for header
      const letters = Object.keys(groupedBrands)
      
      for (const letter of letters) {
        const section = document.getElementById(`section-${letter}`)
        if (!section) continue
        
        const { top, bottom } = section.getBoundingClientRect()
        const sectionTop = top + window.scrollY
        const sectionBottom = bottom + window.scrollY
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
          setActiveLetter(letter)
          break
        }
      }
    }

    // Initial check
    handleScroll()
    
    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [groupedBrands, isScrolling])

  const handleLetterClick = (letter: string) => {
    // Skip if no brands for this letter
    if (!groupedBrands[letter]?.length) return
    
    setActiveLetter(letter)
    setIsScrolling(true)
    
    const section = document.getElementById(`section-${letter}`)
    if (section) {
      const headerOffset = 120 // Adjust based on your header height
      const elementPosition = section.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset
      
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
      
      // Reset scrolling state after scroll completes
      setTimeout(() => setIsScrolling(false), 1000)
    }
  }

  // Get available letters that have brands
  const availableLetters = Object.keys(groupedBrands).filter(
    (letter) => groupedBrands[letter]?.length > 0
  )

  return (
    <div className="sticky top-[64px] z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border shadow-sm">
      <div className="container">
        <AlphabetNavigation
          onLetterClickAction={handleLetterClick}
          activeLetter={activeLetter}
          className="py-2"
        />
      </div>
    </div>
  )
}
