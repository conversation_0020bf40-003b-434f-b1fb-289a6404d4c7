import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getBrandPageData } from '@/lib/data/brands';
import BrandClient from './BrandClient';

export const revalidate = 3600; // Revalidate every hour

export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  const data = await getBrandPageData(params.id);
  
  if (!data) {
    return {
      title: 'Brand Not Found',
      description: 'The requested brand could not be found.',
    };
  }

  const { brand } = data;
  const title = `${brand.name} Promotions & Cashback Deals`;
  const description = brand.description || `Find the latest ${brand.name} cashback offers and promotions.`;
  const url = `https://yourdomain.com/brands/${brand.slug || brand.id}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url,
      siteName: 'Cashback Deals',
      images: brand.logoUrl ? [{
        url: brand.logoUrl,
        width: 200,
        height: 200,
        alt: `${brand.name} logo`,
      }] : [],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: brand.logoUrl ? [brand.logoUrl] : [],
    },
    alternates: {
      canonical: url,
    },
  };
}

// Generate static params for the top 100 brands (for SSG)
export async function generateStaticParams() {
  // We'll pre-render the most popular brands at build time
  // For now, return an empty array and rely on ISR for dynamic routes
  // In production, you might want to fetch the top 100 brands here
  return [];
}

export default async function BrandPage({ params }: { params: { id: string } }) {
  // Fetch data directly in the server component
  const data = await getBrandPageData(params.id);

  // Handle not found
  if (!data) {
    notFound();
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Link
        href="/brands"
        className="container py-4 inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/90"
      >
        <ArrowLeft className="h-4 w-4" /> Back to Brands
      </Link>
						</h1>
						<p className="text-gray-600 mb-4">
							{isNotFound ? `We couldn't find the brand you're looking for.` :
								isInvalidFormat ? `The brand ID format is invalid.` :
									`Sorry, we encountered an issue while loading this brand.`}
						</p>
						<Link
							href="/brands"
							className="inline-flex items-center gap-2 text-primary hover:text-primary/90"
						>
							Browse all brands <ArrowRight className="h-4 w-4" />
						</Link>
					</div>
				</div>
			</div>
		);
	}

	if (!data) {
		return (
			<div className="container py-12">
				<div className="max-w-4xl mx-auto text-center">
					<h1 className="text-2xl font-bold text-primary">Brand not found</h1>
				</div>
			</div>
		);
	}

	const currentDate = new Date();

	currentDate.setHours(0, 0, 0, 0); // Set to start of day for consistent comparison

	const allPromotions = data.data.activePromotions || [];

	const activePromotions = allPromotions.filter(promo => {
		const validUntil = new Date(promo.purchaseEndDate);
		validUntil.setHours(23, 59, 59, 999); // Set to end of day
		return validUntil >= currentDate && promo.status === 'active';
	});

	const inactivePromotions = allPromotions.filter(promo => {
		const validUntil = new Date(promo.purchaseEndDate);
		validUntil.setHours(23, 59, 59, 999); // Set to end of day
		return validUntil < currentDate || promo.status === 'expired';
	});

	return (
		<div className="flex flex-col min-h-screen">
			<Link
				href="/brands"
				className="container py-4 inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/90"
			>
				<ArrowLeft className="h-4 w-4" /> Back to Brands
			</Link>

			{/* Hero Section */}
			<motion.section
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
				role="banner"
				aria-label="Brand Information"
			>
				<div className="container">
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.2 }}
						className="grid md:grid-cols-2 gap-8 items-center"
					>
						<div className="h-64 bg-secondary/10 rounded-lg flex items-center justify-center overflow-hidden p-8">
							{data.data.brand.logoUrl ? (
								<div className="relative w-full h-full max-w-[200px] mx-auto flex items-center justify-center">
									<Image
										src={data.data.brand.logoUrl}
										alt={data.data.brand.name}
										width={200}
										height={200}
										className="object-contain max-w-full max-h-full"
										style={{ width: 'auto', height: 'auto' }}
										loading="eager"
										priority={true}
										onError={(e) => {
											const target = e.target as HTMLImageElement;
											target.src = `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(data.data.brand.name)}`;
										}}
									/>
								</div>
							) : (
								<div className="text-2xl font-bold text-secondary/40">{data.data.brand.name}</div>
							)}
						</div>
						<div>
							<h1 className="text-4xl font-bold text-primary mb-6">{data.data.brand.name}</h1>
							<p className="text-lg text-foreground/70 mb-6">
								{data.data.brand.description}
							</p>
							<div className="flex flex-wrap gap-4">
								<div className="flex items-center gap-2 text-sm text-foreground/70">
									<Tag className="h-4 w-4 text-primary" />
									<span>{allPromotions.length} Total Promotion(s)</span>
									<Clock className="h-4 w-4 text-primary" />
									<span>{activePromotions.length} Active</span>
								</div>
							</div>
						</div>
					</motion.div>
				</div>
			</motion.section>

			{/* Content */}
			<div className="container py-12">
				{/* Active Promotions */}
				<h2 className="text-2xl font-bold text-primary mb-6">{data.data.brand.name} Cashback Current Promotions</h2>
				<div className="space-y-4 mb-12">
					{activePromotions.map((promo, i) => (
						<motion.div
							key={i}
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{ delay: i * 0.1 }}
							className="card p-6"
						>
							<div className="flex items-center justify-between">
								<div>
									<h3 className="font-semibold text-primary mb-2">{promo.title}</h3>
									<div className="flex items-center gap-2 mb-2">
										<Tag className="h-4 w-4 text-primary" />
										<span className="text-sm text-foreground/70">
											{promo.category?.name || 'Category not available'}
										</span>
									</div>
									<p className="text-sm text-foreground/70">
										Valid until {new Date(promo.purchaseEndDate).toLocaleDateString()}
									</p>
								</div>
								<Link
									href={`/products?promotion_id=${promo.id}`}
									className="inline-flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90"
								>
									View Products <ArrowRight className="h-4 w-4" />
								</Link>
							</div>
						</motion.div>
					))}
				</div>

				{/* Inactive Promotions */}
				{inactivePromotions.length > 0 && (
					<>
						<h4 className="text-xl font-semibold text-gray-500 mb-6">
							{data.data.brand.name} Cashback Missed Promotions
						</h4>
						<div className="space-y-4">
							{inactivePromotions.map((promo, i) => (
								<motion.div
									key={i}
									initial={{ opacity: 0, x: -20 }}
									animate={{ opacity: 1, x: 0 }}
									transition={{ delay: i * 0.1 }}
									className="card p-6"
								>
									<div className="flex items-center justify-between">
										<div>
											<h3 className="font-semibold text-gray-400 mb-2">{promo.title}</h3>
											<div className="flex items-center gap-2 mb-2">
												<Tag className="h-4 w-4 text-gray-400" />
												<span className="text-sm text-gray-400">
													{promo.category?.name || 'Category not available'}
												</span>
											</div>
											<p className="text-sm text-gray-400">
												Expired on {new Date(promo.purchaseEndDate).toLocaleDateString()}
											</p>
										</div>
										<div className="inline-flex items-center gap-2 rounded-lg bg-gray-200 px-4 py-2 text-sm font-medium text-gray-500">
											Expired <ArrowRight className="h-4 w-4" />
										</div>
									</div>
								</motion.div>
							))}
						</div>
					</>
				)}

				{/* Debug Panel */}
				{isDebugEnabled() && (
					<div className="mt-8">
						<DebugPanel
							data={{
								timing: { start: Date.now(), end: Date.now(), duration: 0 },
								queries: [`Fetching brand: ${params.id}`],
								params: { ...params },
							}}
							title="Brand Debug Info"
						/>
					</div>
				)}
			</div>
		</div>
	);


}
