'use client'

// Simple skeleton component
const Skeleton = ({ className = '', ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div 
    className={`bg-gray-200 dark:bg-gray-800 rounded-md animate-pulse ${className}`}
    {...props} 
  />
)

/**
 * Loading skeleton for the brands page
 * Shows while the page is being statically generated or revalidated
 */
export default function BrandsLoading() {
  return (
    <div className="container py-12">
      {/* Search bar skeleton */}
      <div className="max-w-2xl mb-12">
        <Skeleton className="h-12 w-full rounded-lg" />
      </div>

      {/* Alphabet navigation skeleton */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          {Array.from({ length: 26 }).map((_, i) => (
            <Skeleton key={i} className="h-10 w-10 rounded-md" />
          ))}
        </div>
      </div>

      {/* Brand grid skeleton */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="space-y-4">
            <Skeleton className="h-40 w-full rounded-lg" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-1/3 mt-2" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
