'use client'

import { useEffect } from 'react'
import { AlertCircle } from 'lucide-react'

// Simple alert component since the UI library is not available
const Alert = ({ children, className = '', variant = 'default', ...props }: any) => (
  <div 
    className={`p-4 mb-4 rounded-lg ${
      variant === 'destructive' 
        ? 'bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-200' 
        : 'bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200'
    } ${className}`}
    {...props}
  >
    {children}
  </div>
)

const AlertTitle = ({ children, className = '', ...props }: any) => (
  <h3 className={`font-medium leading-none tracking-tight mb-1 ${className}`} {...props}>
    {children}
  </h3>
)

const AlertDescription = ({ children, className = '', ...props }: any) => (
  <div className={`text-sm [&_p]:leading-relaxed ${className}`} {...props}>
    {children}
  </div>
)

const Button = ({ 
  children, 
  className = '', 
  variant = 'default',
  onClick,
  ...props 
}: any) => (
  <button
    onClick={onClick}
    className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background ${
      variant === 'default' 
        ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
        : 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
    } h-10 py-2 px-4 ${className}`}
    {...props}
  >
    {children}
  </button>
)

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

/**
 * Error boundary for the brands page
 * Handles both client and server-side errors
 */
export default function BrandsError({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Brands page error:', error)
  }, [error])

  return (
    <div className="container py-12">
      <Alert variant="destructive" className="max-w-3xl mx-auto">
        <AlertCircle className="h-5 w-5" />
        <AlertTitle>Something went wrong</AlertTitle>
        <AlertDescription className="space-y-4">
          <p>
            We're having trouble loading the brands page. Please try refreshing the page or check back
            later.
          </p>
          <div className="bg-muted/50 p-4 rounded-md overflow-auto max-h-40">
            <code className="text-sm text-muted-foreground break-words">
              {error.message || 'Unknown error occurred'}
            </code>
          </div>
          <Button
            variant="outline"
            onClick={() => reset()}
            className="mt-4"
            aria-label="Retry loading brands"
          >
            Try Again
          </Button>
        </AlertDescription>
      </Alert>
    </div>
  )
}
