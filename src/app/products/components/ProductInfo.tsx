'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tag, Store, Clock, AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { ResilientImage } from '@/components/ui/ResilientImage';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "../../../components/ui/dialog";
import { Button } from "../../../components/ui/button";
import { formatPrice } from "../../../lib/utils";
import { formatDate } from '@/app/utils/date';
import { ProductStructuredData } from '@/components/seo/StructuredData';

interface ProductInfoProps {
    product: any; // Accepts Product or TransformedProduct
}

export function ProductInfo({ product }: ProductInfoProps) {
    const transformedProduct = product;
    const [isClaimDetailsExpanded, setIsClaimDetailsExpanded] = useState(false);
    const [contentHeight, setContentHeight] = useState(0);
    const contentRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (contentRef.current) {
            setContentHeight(contentRef.current.scrollHeight);
        }
    }, [isClaimDetailsExpanded]);

    const toggleClaimDetails = () => {
        setIsClaimDetailsExpanded(!isClaimDetailsExpanded);
    };

    // Log detailed promotion info for debugging
    console.log('Product details:', {
        productId: transformedProduct.id,
        productName: transformedProduct.name,
        hasPromotion: !!transformedProduct.promotion,
        promotion: transformedProduct.promotion ? {
            id: transformedProduct.promotion.id,
            purchaseEndDate: transformedProduct.promotion.purchaseEndDate,
            claimStartOffsetDays: transformedProduct.promotion.claimStartOffsetDays,
            claimWindowDays: transformedProduct.promotion.claimWindowDays,
            claimPeriod: transformedProduct.promotion.claimPeriod,
            hasClaimPeriod: !!transformedProduct.promotion.claimPeriod,
            rawPromotion: transformedProduct.promotion
        } : null,
        hasClaimPeriodInRoot: !!transformedProduct.claimPeriod,
        claimPeriodInRoot: transformedProduct.claimPeriod,
        hasPromotionClaimPeriod: !!transformedProduct.promotion?.claimPeriod,
        promotionClaimPeriod: transformedProduct.promotion?.claimPeriod,
        environment: {
            isClient: typeof window !== 'undefined',
            nodeEnv: process.env.NODE_ENV,
            supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL
        }
    });

    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [imageError, setImageError] = useState(false);

    const getImageUrl = (image: string) => {
        if (imageError || !image) {
            return transformedProduct.brand?.logo_url || 
                `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`;
        }

        // Define common invalid image path patterns
        const invalidPatterns = [
            'URL_to_image_1',
            'image_url_1.jpg',
            'example.com',
            'blank.html',
            'placeholder-product.png' // Assuming this is a generic placeholder that shouldn't be treated as a real image
        ];

        // Check if the imagePath is invalid
        if (invalidPatterns.some(pattern => image.includes(pattern))) {
            return transformedProduct.brand?.logo_url || 
                `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`;
        }

        if (image.startsWith('http')) {
            return image;
        }

        const fullUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;

        console.log('Image URL:', {
            input: image,
            output: fullUrl,
            supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL
        });

        return fullUrl;
    };

    const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        const target = e.target as HTMLImageElement;
        console.error('Image load error:', {
            productId: transformedProduct.id,
            productName: transformedProduct.name,
            attemptedSrc: target.src,
            currentImageIndex,
            totalImages: transformedProduct.images?.length || 0
        });
        setImageError(true);
    };

    const productImages = transformedProduct.images || [];
    const hasMultipleImages = productImages.length > 1;

    const currentImage = productImages.length > 0 
        ? productImages[currentImageIndex]
        : (transformedProduct.brand?.logo_url || `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`);

    const nextImage = () => {
        if (productImages.length > 0) {
            setCurrentImageIndex((prev) => (prev + 1) % productImages.length);
        }
    };

    const previousImage = () => {
        if (productImages.length > 0) {
            setCurrentImageIndex((prev) => (prev - 1 + productImages.length) % productImages.length);
        }
    };

    console.log('Promotion data:', transformedProduct.promotion);
    console.log('Product data:', transformedProduct);

    const daysUntilEnd = transformedProduct.promotion?.purchaseEndDate
        ? Math.ceil((new Date(transformedProduct.promotion.purchaseEndDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        : 0;

    return (
        <>
            <ProductStructuredData product={transformedProduct} />
            
            <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="grid md:grid-cols-2 gap-8 mb-12"
            >
                <div className="space-y-4">
                    <div className="aspect-square bg-secondary/10 rounded-lg flex items-center justify-center overflow-hidden relative">
                        <ResilientImage
                            src={getImageUrl(currentImage)}
                            alt={transformedProduct.name}
                            width={600} // Placeholder width
                            height={600} // Placeholder height
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-contain"
                            onError={(error) => {
                                console.warn(`Product main image error for ${transformedProduct.name}:`, error);
                                // Note: handleImageError expects a React event, but ResilientImage onError provides a string
                                // We'll just log the error here since ResilientImage handles fallbacks internally
                            }}
                            priority
                            productName={transformedProduct.name}
                            brandName={transformedProduct.brand?.name}
                            enableValidation={true}
                            showLoadingState={true}
                            retryOnError={true}
                        />
                        
                        {hasMultipleImages && (
                            <>
                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="absolute left-2 top-1/2 -translate-y-1/2 z-10"
                                    onClick={previousImage}
                                    aria-label="Previous image"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="absolute right-2 top-1/2 -translate-y-1/2 z-10"
                                    onClick={nextImage}
                                    aria-label="Next image"
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </>
                        )}

                        <div className="absolute top-4 right-4 bg-secondary text-white px-4 py-2 rounded-lg font-medium">
                            {transformedProduct.cashbackAmount > 0
                                ? `Claim ${formatPrice(transformedProduct.cashbackAmount)} cashback`
                                : 'No Cashback Available'
                            }
                        </div>
                    </div>

                    {hasMultipleImages && (
                        <div className="flex justify-center gap-2 py-10">
                            <div className="flex gap-2 overflow-x-auto max-w-full px-2">
                                {productImages.map((image: string, index: number) => (
                                    <button
                                        key={index}
                                        onClick={() => setCurrentImageIndex(index)}
                                        className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 
                                            ${index === currentImageIndex ? 'ring-2 ring-primary' : ''}`}
                                        aria-label={`View image ${index + 1} of ${productImages.length}`}
                                    >
                                        <ResilientImage
                                            src={getImageUrl(image)}
                                            alt={`${transformedProduct.name} - View ${index + 1}`}
                                            width={80} // Placeholder width
                                            height={80} // Placeholder height
                                            sizes="80px"
                                            className="object-cover"
                                            priority={index === 0}
                                            onError={(error) => {
                                                console.warn(`Product thumbnail error for ${transformedProduct.name} (image ${index + 1}):`, error);
                                                // Note: ResilientImage handles fallbacks internally, no need to call handleImageError
                                            }}
                                            productName={transformedProduct.name}
                                            brandName={transformedProduct.brand?.name}
                                            enableValidation={true}
                                            showLoadingState={false} // Don't show loading state for thumbnails
                                            retryOnError={false} // Don't retry thumbnails to avoid UI jank
                                        />
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                <div>
                    <h1 className="text-3xl font-bold text-primary mb-2">{transformedProduct.name}</h1>
                    {transformedProduct.brand && (
                        <Link href={`/brands/${transformedProduct.brand.id}`} className="inline-block">
                            <p className="text-xl text-foreground/70 mb-4 hover:text-primary transition-colors">
                                {transformedProduct.brand.name}
                            </p>
                        </Link>
                    )}
                    <div className="flex flex-wrap gap-4 mb-6">
                        {transformedProduct.category && (
                            <div className="flex items-center gap-2 text-sm text-foreground/70">
                                <Tag className="h-4 w-4 text-primary" />
                                <span>{transformedProduct.category.name}</span>
                            </div>
                        )}

                        <div className="flex items-center gap-2 text-sm text-foreground/70">
                            <Store className="h-4 w-4 text-primary" />
                            <span>{
                                (transformedProduct.retailerOffers?.length || 0) > 0 
                                    ? `${transformedProduct.retailerOffers.length} Retailers offering cashback`
                                    : 'No retailers available'
                            }</span>
                        </div>
                        {daysUntilEnd !== null && daysUntilEnd > 0 && (
                        <div className="flex items-center gap-2 text-sm text-foreground/70">
                            <Clock className="h-4 w-4 text-primary" />
                            <span>Ends in {daysUntilEnd} days</span>
                        </div>
                    )}
                    </div>

                    <div className="bg-secondary text-white p-4 rounded-lg mb-6">
                        <h3 className="font-semibold mb-2">
                            {transformedProduct.cashbackAmount > 0
                                ? `Claim ${formatPrice(transformedProduct.cashbackAmount)} Cashback`
                                : 'No Cashback Available'
                            }
                        </h3>
                        {transformedProduct.promotion && (
                            <div className="flex items-start gap-2 mt-2">
                                <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                                <div className="text-sm">
                                    {transformedProduct.promotion?.purchaseEndDate && (
                                        <div className="flex items-center gap-2">
                                            <p>Valid until: {formatDate(transformedProduct.promotion.purchaseEndDate)}</p>
                                            <button 
                                                onClick={toggleClaimDetails}
                                                className="text-sm text-blue-600 hover:underline focus:outline-none"
                                            >
                                                {isClaimDetailsExpanded ? 'See less' : 'See more'}
                                            </button>
                                        </div>
                                    )}
                                    {transformedProduct.promotion && (
                                        <div 
                                            className={`overflow-hidden transition-all duration-300 ease-in-out ${
                                                isClaimDetailsExpanded ? 'max-h-[500px]' : 'max-h-0'
                                            }`}
                                        >
                                            <div 
                                                ref={contentRef}
                                                className="mt-4 p-4 bg-gray-50 rounded-lg"
                                            >
                                                <h3 className="font-medium text-gray-900 mb-2">Claim Period Details</h3>
                                                <div className="space-y-2">
                                                    <p className="text-sm text-gray-600">
                                                        <span className="font-medium">Purchase by:</span> {transformedProduct.promotion.purchaseEndDate ? 
                                                            formatDate(transformedProduct.promotion.purchaseEndDate) : 
                                                            'Not specified'}
                                                    </p>
                                                    <p className="text-sm text-gray-600">
                                                        <span className="font-medium">Claim starts:</span> {transformedProduct.promotion.claimStartOffsetDays != null ? 
                                                            `${transformedProduct.promotion.claimStartOffsetDays} days after purchase` : 
                                                            'Not specified'}
                                                    </p>
                                                    <p className="text-sm text-gray-600">
                                                        <span className="font-medium">Claim window:</span> {transformedProduct.promotion.claimWindowDays != null ? 
                                                            `${transformedProduct.promotion.claimWindowDays} days` : 
                                                            'Not specified'}
                                                    </p>
                                                </div>
                                                <button 
                                                    onClick={toggleClaimDetails}
                                                    className="mt-2 text-sm text-blue-600 hover:underline focus:outline-none"
                                                >
                                                    See less
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                        {transformedProduct.promotion?.termsUrl && (
                            <div className="mt-4">
                                <Dialog>
                                    <DialogTrigger asChild>
                                        <span className="text-white text-sm underline hover:text-white/90 cursor-pointer">
                                            View the {transformedProduct.promotion.title} Terms & Conditions
                                        </span>
                                    </DialogTrigger>
                                    <DialogContent>
                                        <DialogHeader>
                                            <DialogTitle>Cashback Terms & Conditions</DialogTitle>
                                            <DialogDescription>
                                                {transformedProduct.promotion.termsDescription || 'No terms available'}
                                                {transformedProduct.promotion.termsUrl && (
                                                    <Link
                                                        href={transformedProduct.promotion.termsUrl}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="block mt-2 text-blue-300 hover:text-white"
                                                    >
                                                        View Full Terms
                                                    </Link>
                                                )}
                                            </DialogDescription>
                                        </DialogHeader>
                                    </DialogContent>
                                </Dialog>
                            </div>
                        )}
                    </div>
                </div>
            </motion.div>
        </>
    );
}
