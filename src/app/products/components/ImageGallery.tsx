import React, { useEffect } from 'react'; // Add useEffect import
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from "../../../components/ui/button";

interface ImageGalleryProps {
    images: string[];
    currentIndex: number;
    onIndexChange: (index: number) => void;
    productName: string;
    getImageUrl: (image: string) => string;
    onImageError: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
    cashbackAmount?: number;
}

/**
 * ImageGallery Component
 * 
 * Displays a product's images with navigation controls and thumbnails.
 * Handles both main image display and thumbnail gallery.
 */
export function ImageGallery({ 
    images, 
    currentIndex, 
    onIndexChange, 
    productName, 
    getImageUrl, 
    onImageError,
    cashbackAmount
}: ImageGalleryProps) {
    const hasMultipleImages = images.length > 1;
    
    // Add this useEffect hook to preload all images
    useEffect(() => {
        // Preload all images to prevent new HTTP requests when navigating
        if (hasMultipleImages) {
            images.forEach((image, index) => {
                // Skip the current image as it's already loaded with priority
                if (index !== currentIndex) {
                    // Fix: Create a proper HTML Image element
                    const img = document.createElement('img');
                    img.src = getImageUrl(image);
                }
            });
        }
    }, [images, currentIndex, getImageUrl, hasMultipleImages]);
    
    return (
        <div className="space-y-4">
            {/* Rest of your component remains unchanged */}
            {/* Main Image Display */}
            <div className="aspect-square bg-secondary/10 rounded-lg flex items-center justify-center overflow-hidden relative">
                <Image
                    src={getImageUrl(images[currentIndex])}
                    alt={productName}
                    fill
                    className="object-contain"
                    onError={onImageError}
                    priority
                />
                
                {/* Navigation Arrows */}
                {hasMultipleImages && (
                    <>
                        <Button
                            variant="secondary"
                            size="icon"
                            className="absolute left-2 top-1/2 -translate-y-1/2 z-10"
                            onClick={() => onIndexChange((currentIndex - 1 + images.length) % images.length)}
                        >
                            <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <Button
                            variant="secondary"
                            size="icon"
                            className="absolute right-2 top-1/2 -translate-y-1/2 z-10"
                            onClick={() => onIndexChange((currentIndex + 1) % images.length)}
                        >
                            <ChevronRight className="h-4 w-4" />
                        </Button>
                    </>
                )}

                {/* Cashback Badge */}
                {cashbackAmount !== undefined && (
                    <div className="absolute top-4 right-4 bg-secondary text-white px-4 py-2 rounded-lg font-medium">
                        {cashbackAmount > 0
                            ? `Claim £${cashbackAmount.toFixed(2)} cashback`
                            : 'No Cashback Available'
                        }
                    </div>
                )}
            </div>

            {/* Thumbnail Gallery */}
            {hasMultipleImages && (
                <div className="flex justify-center gap-2 py-10">
                    <div className="flex gap-2 overflow-x-auto max-w-full px-2">
                        {images.map((image, index) => (
                            <button
                                key={index}
                                onClick={() => onIndexChange(index)}
                                className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 
                                    ${index === currentIndex ? 'ring-2 ring-primary' : ''}`}
                            >
                                <Image
                                    src={getImageUrl(image)}
                                    alt={`${productName} - View ${index + 1}`}
                                    fill
                                    className="object-cover"
                                    loading={index === 0 ? "eager" : "lazy"}
                                    onError={onImageError}
                                />
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}