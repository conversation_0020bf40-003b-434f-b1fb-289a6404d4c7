import React from 'react';
import { motion } from 'framer-motion';
import { 
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from "../../../components/ui/accordion";


interface Specification {
    [key: string]: string | undefined;
}

interface ProductDetailsProps {
    description: string;
    specifications: Specification | null;
}

export function ProductDetailsSection({ description, specifications }: ProductDetailsProps) {
    // Function to format specification keys for display
    const formatSpecKey = (key: string): string => {
        return key
            .replace(/_/g, ' ')
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    };

    // Function to format specification values for display
    const formatSpecValue = (value: string | undefined): React.ReactNode => {
        // Handle undefined values
        if (!value) return '';        // If value starts with # characters, render as list items
        if (value.includes('# ')) {
            const items = value.split('\n').filter(item => item.trim().startsWith('# '));
            return (
                <ul className="list-disc pl-5 space-y-1 mt-1">
                    {items.map((item, i) => (
                        <li key={i} className="text-foreground/80">
                            {item.replace('# ', '')}
                        </li>
                    ))}
                </ul>
            );
        }
        
        // If value contains numbered list format (1., 2., etc.)
        if (/^\d+\.\s/.test(value)) {
            const items = value.split('\n').filter(item => /^\d+\.\s/.test(item.trim()));
            return (
                <ol className="list-decimal pl-5 space-y-1 mt-1">
                    {items.map((item, i) => (
                        <li key={i} className="text-foreground/80">
                            {item.replace(/^\d+\.\s/, '')}
                        </li>
                    ))}
                </ol>
            );
        }
        
        // Otherwise, return as plain text with line breaks preserved
        return value.split('\n').map((line, i) => (
            <React.Fragment key={i}>
                {line}
                {i < value.split('\n').length - 1 && <br />}
            </React.Fragment>
        ));
    };

    // Group specifications into logical categories for better organization
    const specCategories = React.useMemo(() => {
        if (!specifications) return {};
        
        const categories: Record<string, Record<string, string>> = {
            'Main Features': {},
            'Technical Specifications': {},
            'Physical Specifications': {},
            'Additional Information': {}
        };
        
        // Inside the forEach loop where you're categorizing specifications:
        Object.entries(specifications).forEach(([key, value]) => {
            // Skip undefined values
            if (value === undefined) return;
            
            // Skip price and product image fields
            if (key === 'price' || key.startsWith('product_image_')) return;
            
            // Now value is definitely a string
            const stringValue: string = value;
            
            if (['features', 'smart_features', 'highlights', 'benefits'].includes(key)) {
                categories['Main Features'][key] = stringValue;
            } 
            else if (['dimensions', 'color_finish', 'materials', 'weight'].includes(key)) {
                categories['Physical Specifications'][key] = stringValue;
            }
            else if (['technical_specs', 'energy_rating', 'model_number'].includes(key)) {
                categories['Technical Specifications'][key] = stringValue;
            }
            else if (key !== 'product_description') {
                categories['Additional Information'][key] = stringValue;
            }
        });    
        
        // Remove empty categories
        Object.keys(categories).forEach(category => {
            if (Object.keys(categories[category]).length === 0) {
                delete categories[category];
            }
        });
        
        return categories;
    }, [specifications]);

    return (
        <motion.section
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-12"
            aria-labelledby="product-details-heading"
        >
            <h2 id="product-details-heading" className="text-2xl font-bold text-primary mb-6">Product Details</h2>
            
            <Accordion type="single" collapsible defaultValue="description" className="space-y-4">
                {/* Product Description - Open by default */}
                <AccordionItem value="description" className="bg-white border rounded-lg overflow-hidden">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <h3 className="text-lg font-semibold">Description</h3>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 py-4 border-t">
                        <div className="prose max-w-none text-foreground/80">
                            <p>{description}</p>
                        </div>
                    </AccordionContent>
                </AccordionItem>
                
                {/* Specifications Sections */}
                {specifications && Object.entries(specCategories).map(([category, specs]) => (
                    <AccordionItem 
                        key={category} 
                        value={category.toLowerCase().replace(/\s+/g, '-')}
                        className="bg-white border rounded-lg overflow-hidden"
                    >
                        <AccordionTrigger className="px-6 py-4 hover:no-underline">
                            <h3 className="text-lg font-semibold">{category}</h3>
                        </AccordionTrigger>
                        <AccordionContent className="px-6 py-4 border-t">
                            <dl className="space-y-6">
                                {Object.entries(specs).map(([key, value]) => (
                                    <div key={key} className="grid sm:grid-cols-3 gap-2">
                                        <dt className="font-medium text-foreground sm:col-span-1">
                                            {formatSpecKey(key)}
                                        </dt>
                                        <dd className="text-foreground/80 sm:col-span-2">
                                            {formatSpecValue(value)}
                                        </dd>
                                    </div>
                                ))}
                            </dl>
                        </AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        </motion.section>
    );
}