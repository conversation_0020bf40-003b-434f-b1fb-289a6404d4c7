import { motion } from 'framer-motion'
import { Tag, ExternalLink } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { featureFlags } from '@config/features'
import { calculatePriceAfterCashback, formatPrice, sortOptions, defaultSortOption, type SortOption } from '@lib/utils'

interface RetailerOffer {
    retailer: {
        name: string;
        logoUrl: string;
    };
    price: number;
    stockStatus: string;
    url: string;
    createdAt: string;
}

interface PriceComparisonProps {
    retailerOffers: RetailerOffer[];
}

export function PriceComparison({ retailerOffers }: PriceComparisonProps) {
    const [sortBy, setSortBy] = React.useState<SortOption>(defaultSortOption);

    const sortedOffers = React.useMemo(() => {
        const sorted = [...retailerOffers];
        switch (sortBy) {
            case 'price_desc':
                return sorted.sort((a, b) => b.price - a.price);
            case 'price_asc':
                return sorted.sort((a, b) => a.price - b.price);
            case 'newest':
                return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            case 'oldest':
                return sorted.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
            case 'recommended':
                return featureFlags.search.recommendedFilter
                    ? sorted
                        .filter(offer => ['in_stock', 'low_stock'].includes(offer.stockStatus))
                        .sort((a, b) => a.price - b.price)
                    : sorted;

            default:
                return sorted;
        }
    }, [retailerOffers, sortBy]);

    return (
        <>
            <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-primary">Available Retailer Offers</h2>
                <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as SortOption)}
                    className="text-sm border rounded-lg px-3 py-2 bg-white"
                >
                    {Object.entries(sortOptions).map(([value, label]: [string, string]) => {
                        if ((value !== 'recommended' || featureFlags.search.recommendedFilter) &&
                            value !== 'cashback_desc' &&
                            value !== 'cashback_asc') {
                            return (
                                <option key={value} value={value}>
                                    {label}
                                </option>
                            );
                        }
                        return null;
                    })}
                </select>
            </div>
            <div className="space-y-4">
                {sortedOffers.map((offer, i) => {
                    const isInStock = ['in_stock', 'low_stock'].includes(offer.stockStatus);
                    return (
                        <motion.div
                            key={i}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: i * 0.1 }}
                            className="card p-6 bg-white"
                        >
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    <div className="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center overflow-hidden">
                                        {offer.retailer.logoUrl ? (
                                            <Image
                                                src={offer.retailer.logoUrl}
                                                alt={offer.retailer.name}
                                                width={24}
                                                height={24}
                                                className="object-contain"
                                                style={{ width: '100%', height: 'auto' }}
                                                loading="lazy"
                                                onError={(e) => {
                                                    const target = e.target as HTMLImageElement;
                                                    target.src = `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(offer.retailer.name)}`;
                                                }}
                                            />
                                        ) : (
                                            <Tag className="h-6 w-6 text-accent" />
                                        )}
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-primary">{offer.retailer.name}</h3>
                                    </div>
                                </div>
                                <div className="text-right space-y-1">
                                    <p className="text-lg font-bold text-primary">
                                        {formatPrice(offer.price)}
                                    </p>
                                    <p
                                        className={`text-sm ${isInStock ? 'text-gray-800 bg-green-100' : 'text-gray-600 bg-red-100'
                                            } px-2 py-0.5 rounded-md text-center`}
                                    >
                                        {isInStock ? 'In Stock' :
                                            offer.stockStatus === 'low_stock' ? 'Low Stock' :
                                                offer.stockStatus === 'discontinued' ? 'Discontinued' :
                                                    offer.stockStatus === 'pre_order' ? 'Pre Order' :
                                                        'Out of Stock'}
                                    </p>
                                </div>
                            </div>
                            <div className="mt-4 pt-4 border-t flex items-center justify-end">
                                <Link href={offer.url} target="_blank" rel="noopener noreferrer">
                                    <motion.div
                                        whileHover={{ scale: 1.05 }}
                                        className="inline-flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90"
                                    >
                                        Shop Now <ExternalLink className="h-4 w-4" />
                                    </motion.div>
                                </Link>
                            </div>
                        </motion.div>
                    );
                })}
            </div>
        </>
    );
}
