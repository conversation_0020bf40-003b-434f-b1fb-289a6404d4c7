// src/app/products/[id]/page.tsx - Product detail page with Server-Side Rendering
// Converted from client component to server component for improved SEO and Core Web Vitals

import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { constructMetadata } from '@/lib/metadata-utils';
import { getProduct, getProductWithSimilar, getProductBySlug, getSimilarProducts } from '@/lib/data/products';
import { ProductPageClient } from '@/components/pages/ProductPageClient';
import { ProductStructuredData } from '@/components/seo/StructuredData';

interface ProductPageProps {
  params: { id: string };
}

// Generate dynamic metadata for SEO optimization
export async function generateMetadata({ params }: ProductPageProps) {
  try {
    const { id } = params;
    let product;
    
    // Check if the ID is a UUID (with hyphens) or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
    
    if (isUUID) {
      product = await getProduct(id);
    } else {
      product = await getProductBySlug(id);
    }
    
    if (!product) {
      console.error(`Product not found for ID/slug: ${id}`);
      return constructMetadata({
        title: 'Product Not Found',
        description: 'The requested product could not be found.',
        noIndex: true
      });
    }
    
    // Create SEO-optimized metadata
    const title = `${product.name} - Best Cashback Deals`;
    const description = product.description 
      ? `${product.description.substring(0, 155)}...`
      : `Get cashback on ${product.name} from ${product.brand?.name || 'top retailers'}. Compare prices and save money with our exclusive offers.`;
    
    const keywords = [
      product.name,
      product.brand?.name,
      'cashback',
      'deals',
      product.category?.name,
      'save money',
      'best price'
    ].filter(Boolean).join(', ');

    return constructMetadata({
      title,
      description,
      image: (product.images && product.images.length > 0
        ? product.images[0]
        : product.brand?.logoUrl) || undefined,
      pathname: `/products/${product.slug || product.id}`
    });
  } catch (error) {
    console.error('Error generating product metadata:', error);
    return constructMetadata({
      title: 'Product Not Found',
      description: 'The requested product could not be found.',
      noIndex: true
    });
  }
}

// Loading skeleton component for better UX during data fetching
function ProductPageSkeleton() {
  return (
    <div className="container py-12">
      <div className="max-w-4xl mx-auto">
        {/* Back button skeleton */}
        <div className="h-6 bg-gray-300 rounded w-32 mb-8 animate-pulse"></div>
        
        {/* Product info skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Image skeleton */}
          <div className="h-96 bg-gray-300 rounded animate-pulse"></div>
          
          {/* Product details skeleton */}
          <div className="space-y-4">
            <div className="h-8 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2 animate-pulse"></div>
            <div className="h-16 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-12 bg-gray-300 rounded w-40 animate-pulse"></div>
          </div>
        </div>

        {/* Price comparison skeleton */}
        <div className="mb-8">
          <div className="h-6 bg-gray-300 rounded w-48 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>

        {/* Similar products skeleton */}
        <div>
          <div className="h-6 bg-gray-300 rounded w-40 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-64 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Main product page component - Server Component for optimal SEO
export default async function ProductPage({ params }: ProductPageProps) {
  try {
    const { id } = params;

    // Determine if id is UUID or slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
    let productData = null;

    if (isUUID) {
      // Fetch by UUID with similar products
      productData = await getProductWithSimilar(id);
    } else {
      // Fetch by slug, then get similar products using the product ID
      const product = await getProductBySlug(id);
      if (product) {
        const similarProducts = await getSimilarProducts(product.id).catch((err: Error) => {
          console.error('Error fetching similar products:', err);
          return [];
        });
        productData = { product, similarProducts };
      }
    }

    if (!productData || !productData.product) {
      notFound();
    }

    const { product, similarProducts } = productData;

    return (
      <>
        {/* Product structured data for enhanced SEO and rich snippets */}
        <ProductStructuredData 
          product={product} 
          retailerOffers={product.retailerOffers}
        />
        
        {/* Suspense boundary for progressive loading */}
        <Suspense fallback={<ProductPageSkeleton />}>
          <ProductPageClient 
            product={product}
            similarProducts={similarProducts || []}
          />
        </Suspense>
      </>
    );
  } catch (error) {
    console.error('Error loading product page:', error);
    notFound();
  }
}
