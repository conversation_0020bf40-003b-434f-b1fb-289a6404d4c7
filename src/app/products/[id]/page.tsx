// src/app/products/[id]/page.tsx - Product detail page with Server-Side Rendering
// Converted from client component to server component for improved SEO and Core Web Vitals

import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { constructMetadata } from '@/lib/metadata-utils';
import { getProductPageData } from '@/lib/data/products';
import { ProductPageClient } from '@/components/pages/ProductPageClient';
import { ProductStructuredData } from '@/components/seo/StructuredData';

interface ProductPageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ returnTo?: string }>;
}

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  try {
    const result = await getProductPageData(resolvedParams.id);

    if (!result?.product) {
      return constructMetadata({
        title: 'Product Not Found',
        description: 'The requested product could not be found.',
        noIndex: true,
      });
    }

    const product = result.product;

    const title = `${product.name} - Best Cashback Deals`;
    const description = product.description
      ? `${product.description.substring(0, 155)}...`
      : `Get cashback on ${product.name} from ${product.brand?.name || 'top retailers'}. Compare prices and save money with our exclusive offers.`;

    return constructMetadata({
      title,
      description,
      image:
        (product.images && product.images.length > 0
          ? product.images[0]
          : product.brand?.logoUrl) || undefined,
      pathname: `/products/${product.slug || product.id}`,
    });
  } catch {
    return constructMetadata({
      title: 'Product Not Found',
      description: 'The requested product could not be found.',
      noIndex: true,
    });
  }
}

function ProductPageSkeleton() {
  return (
    <div className="container py-12">
      <div className="max-w-4xl mx-auto">
        <div className="h-6 bg-gray-300 rounded w-32 mb-8 animate-pulse"></div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="h-96 bg-gray-300 rounded animate-pulse"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2 animate-pulse"></div>
            <div className="h-16 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-12 bg-gray-300 rounded w-40 animate-pulse"></div>
          </div>
        </div>
        <div className="mb-8">
          <div className="h-6 bg-gray-300 rounded w-48 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
        <div>
          <div className="h-6 bg-gray-300 rounded w-40 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-64 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default async function ProductPage({ params, searchParams }: ProductPageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;

  try {
    const result = await getProductPageData(resolvedParams.id);

    if (!result?.product) {
      notFound();
    }

    return (
      <>
        <ProductStructuredData product={result.product} retailerOffers={result.product.retailerOffers} />
        <Suspense fallback={<ProductPageSkeleton />}>
          <ProductPageClient
            product={result.product}
            similarProducts={result.similarProducts || []}
            returnTo={resolvedSearchParams.returnTo}
          />
        </Suspense>
      </>
    );
  } catch {
    notFound();
  }
}
