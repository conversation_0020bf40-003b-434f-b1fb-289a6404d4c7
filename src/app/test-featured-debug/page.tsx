import { notFound } from 'next/navigation'

/**
 * Dedicated test page to fetch and render featured products and promotions
 * using the same data layer functions as the homepage.
 * 
 * This page helps isolate and debug the issue with featured cashback promotions
 * without modifying existing test pages or homepage code.
 */

import { Suspense } from 'react'
import { getFeaturedProducts, getFeaturedPromotions } from '@/lib/data'

/**
 * Loading components for Suspense boundaries
 */
function Loading() {
  return (
    <div className="space-y-4 p-6">
      <div className="h-6 bg-gray-300 rounded animate-pulse w-3/4"></div>
      <div className="h-6 bg-gray-300 rounded animate-pulse w-1/2"></div>
    </div>
  )
}

/**
 * Server component to fetch and render featured products and promotions
 */
async function FeaturedDebug() {
  if (process.env.NEXT_PUBLIC_ENV === 'production') {
    notFound()
  }
  const [featuredProducts, featuredPromotions] = await Promise.all([
    getFeaturedProducts(5),
    getFeaturedPromotions(5)
  ])

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Featured Debug Test</h1>

      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Featured Products</h2>
        {featuredProducts.length === 0 ? (
          <p className="text-gray-600">No featured products found</p>
        ) : (
          <ul className="list-disc list-inside space-y-1">
            {featuredProducts.map(product => (
              <li key={product.id}>
                <strong>{product.name}</strong> - Brand: {product.brand?.name || 'N/A'} - Promotion: {product.promotion?.title || 'None'}
              </li>
            ))}
          </ul>
        )}
      </section>

      <section>
        <h2 className="text-xl font-semibold mb-2">Featured Promotions</h2>
        {featuredPromotions.length === 0 ? (
          <p className="text-gray-600">No featured promotions found</p>
        ) : (
          <ul className="list-disc list-inside space-y-1">
            {featuredPromotions.map(promotion => (
              <li key={promotion.id}>
                <strong>{promotion.title}</strong> - Brand: {promotion.brand?.name || 'N/A'} - Valid Until: {promotion.purchaseEndDate}
              </li>
            ))}
          </ul>
        )}
      </section>
    </div>
  )
}

export default async function TestFeaturedDebugPage() {
  return (
    <Suspense fallback={<Loading />}>
      <FeaturedDebug />
    </Suspense>
  )
}

/**
 * Metadata for the test page
 */
export const metadata = {
  title: 'Featured Debug Test | CashbackDeals',
  description: 'Debug page for featured products and promotions data',
  robots: 'noindex,nofollow',
}
