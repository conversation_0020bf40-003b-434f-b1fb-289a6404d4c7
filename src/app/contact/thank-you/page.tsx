'use client'
export const runtime = 'edge';

import { motion } from 'framer-motion'
import { CheckCircle, Home, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'

export default function ThankYouPage() {
  const searchParams = useSearchParams();
  const name = searchParams?.get('name') || 'there';
  const inquiryType = searchParams?.get('type') || 'inquiry';

  return (
    <div className="relative flex flex-col min-h-screen">
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
      >
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-3xl mx-auto text-center"
          >
            <div className="inline-flex justify-center items-center mb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ 
                  type: "spring",
                  stiffness: 260,
                  damping: 20,
                  delay: 0.5 
                }}
              >
                <CheckCircle className="h-16 w-16 text-primary" />
              </motion.div>
            </div>
            <h1 className="text-4xl font-bold text-primary mb-6">Thank You!</h1>
            <p className="text-lg text-foreground/80">
              Your message has been received. We'll be in touch soon.
            </p>
          </motion.div>
        </div>
      </motion.section>

      <div className="container py-16">
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="rounded-xl bg-primary/5 p-10 border border-primary/10 mb-10"
          >
            <h2 className="text-2xl font-semibold text-primary mb-6">Thank you, {name}!</h2>
            <p className="mb-6 text-foreground/80">
              We've received your {inquiryType.toLowerCase()} and our team is looking into it. You can expect to hear back from us within 24 hours during business days.
            </p>
            
            <p className="mb-6 text-foreground/80">
              While you wait, why not check out our latest rebate offers? You might discover savings you didn't know were available.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="grid md:grid-cols-2 gap-6 mb-10"
          >
            <div className="bg-white rounded-xl shadow-sm border border-primary/10 p-8 hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold text-primary mb-4">Browse Latest Offers</h3>
              <p className="text-foreground/80 mb-6">
                Explore our current rebate opportunities and start saving on products you love.
              </p>
              <Link 
                href="/brands" 
                className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
              >
                View Offers <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
            
            <div className="bg-white rounded-xl shadow-sm border border-primary/10 p-8 hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold text-primary mb-4">How It Works</h3>
              <p className="text-foreground/80 mb-6">
                Learn more about how CashbackHunter can help you claim the money you deserve.
              </p>
              <Link 
                href="/about" 
                className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
              >
                Learn More <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-center"
          >
            <Link 
              href="/" 
              className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Home className="h-4 w-4" /> Return to Homepage
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
}