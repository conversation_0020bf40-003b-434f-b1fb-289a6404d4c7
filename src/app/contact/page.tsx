// Contact page with proper Static Site Generation (SSG) implementation
import { Metadata } from 'next'; // Import Metadata type for static generation
import ContactStructuredData from '@components/seo/ContactStructuredData'; // Structured data for SEO
import ContactPageContent from './ContactPageContent'; // Importing client component with animations and form

// Define metadata for the page - this implements the App Router metadata API
// This enables proper Static Site Generation (SSG) for improved SEO and Google indexing
export const metadata: Metadata = {
  title: 'Contact Us - RebateRay',
  description: 'Get in touch with the RebateRay team for any inquiries, support needs, or feedback about our cashback deals and services.',
  alternates: {
    canonical: 'https://www.rebateray.com/contact',
  },
  openGraph: {
    title: 'Contact Us - RebateRay',
    description: 'Reach out to our team with any questions about cashback offers or rebate opportunities.',
    url: 'https://www.rebateray.com/contact',
    siteName: 'RebateRay',
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Us - RebateRay',
    description: 'Get in touch with us for any inquiries or support with cashback deals.',
  },
  robots: {
    index: true,
    follow: true,
  }
};

// Define ContactPage component as a Server Component
// Using the App Router pattern for Static Site Generation
export default function ContactPage() {
  // This is a server component with no interactive elements
  // All client-side interactivity is delegated to the ContactPageContent component
  // This approach ensures proper SSG while maintaining the existing UI/UX
  return (
    <>
      {/* Structured data for enhanced SEO visibility */}
      <ContactStructuredData 
        organizationName="RebateRay"
        contactUrl="https://www.rebateray.com/contact"
      /> 
      
      {/* Include the client component that has all the interactive elements */}
      <ContactPageContent />
    </> 
  );
}