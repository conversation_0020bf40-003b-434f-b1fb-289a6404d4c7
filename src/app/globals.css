@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 195 98% 93%;
    --foreground: 0 0% 0%;
    
    --primary: 212 35% 37%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 201 45% 72%;
    --secondary-foreground: 0 0% 0%;
    
    --accent: 12 84% 62%;
    --accent-foreground: 0 0% 100%;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .animate-in {
    @apply animate-fade-in;
  }
  
  .card {
    @apply rounded-lg border bg-white shadow-sm transition-all hover:shadow-md;
  }
}

