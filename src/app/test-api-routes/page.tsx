"use client"

/**
 * Test page for Phase 2 refactored API routes
 * 
 * This page tests the refactored API routes to ensure they work correctly
 * with the new shared data layer implementation.
 */

import { notFound } from 'next/navigation'
import { useState, useEffect } from 'react'

interface TestResult {
  endpoint: string
  status: 'loading' | 'success' | 'error'
  data?: any
  error?: string
  responseTime?: number
}

export default function TestApiRoutesPage() {
  if (process.env.NEXT_PUBLIC_ENV === 'production') {
    notFound()
  }

  const [results, setResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const endpoints = [
    { name: 'Products API', url: '/api/products?limit=5' },
    { name: 'Products API (with filters)', url: '/api/products?limit=3&is_featured=true' },
    { name: 'Brands API', url: '/api/brands?limit=5' },
    { name: 'Featured Promotions API', url: '/api/products/featured?limit=3' },
    { name: 'Retailers API', url: '/api/retailers?limit=5' },
    { name: 'Retailers API (featured)', url: '/api/retailers?limit=3&featured=true' },
    { name: 'Featured Retailers API', url: '/api/retailers/featured?limit=3' },
    { name: 'Search API', url: '/api/search?q=phone&limit=3' },
    { name: 'Search Suggestions API', url: '/api/search/suggestions?q=phone&limit=3' },
    { name: 'Brand Detail API (404 expected)', url: '/api/brands/test-brand-id' },
    { name: 'Product Detail API (404 expected)', url: '/api/products/test-product-id' },
    { name: 'Retailer Detail API (404 expected)', url: '/api/retailers/test-retailer-id' },
  ]

  const testEndpoint = async (endpoint: { name: string; url: string }): Promise<TestResult> => {
    const startTime = Date.now()
    
    try {
      const response = await fetch(endpoint.url)
      const responseTime = Date.now() - startTime
      
      if (!response.ok) {
        return {
          endpoint: endpoint.name,
          status: 'error',
          error: `HTTP ${response.status}: ${response.statusText}`,
          responseTime,
        }
      }
      
      const data = await response.json()
      
      return {
        endpoint: endpoint.name,
        status: 'success',
        data,
        responseTime,
      }
    } catch (error) {
      return {
        endpoint: endpoint.name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime,
      }
    }
  }

  const runTests = async () => {
    setIsRunning(true)
    setResults([])
    
    // Initialize results with loading state
    const initialResults = endpoints.map(endpoint => ({
      endpoint: endpoint.name,
      status: 'loading' as const,
    }))
    setResults(initialResults)
    
    // Test each endpoint
    for (let i = 0; i < endpoints.length; i++) {
      const result = await testEndpoint(endpoints[i])
      
      setResults(prev => prev.map((r, index) => 
        index === i ? result : r
      ))
    }
    
    setIsRunning(false)
  }

  useEffect(() => {
    runTests()
  }, [])

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'loading':
        return '⏳'
      case 'success':
        return '✅'
      case 'error':
        return '❌'
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'loading':
        return 'text-yellow-600'
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Phase 2C API Routes Test
          </h1>
          <p className="text-gray-600">
            Testing refactored API routes with shared data layer including new Retailers API
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">API Endpoint Tests</h2>
            <button
              onClick={runTests}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? 'Running Tests...' : 'Run Tests Again'}
            </button>
          </div>

          <div className="space-y-4">
            {results.map((result, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{getStatusIcon(result.status)}</span>
                    <span className="font-medium">{result.endpoint}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    {result.responseTime && (
                      <span className="text-sm text-gray-500">
                        {result.responseTime}ms
                      </span>
                    )}
                    <span className={`font-medium ${getStatusColor(result.status)}`}>
                      {result.status.toUpperCase()}
                    </span>
                  </div>
                </div>

                {result.error && (
                  <div className="bg-red-50 border border-red-200 rounded p-3 mb-2">
                    <p className="text-red-700 text-sm">{result.error}</p>
                  </div>
                )}

                {result.data && (
                  <div className="bg-gray-50 rounded p-3">
                    <p className="text-sm text-gray-600 mb-2">Response Summary:</p>
                    <ul className="text-sm space-y-1">
                      <li>
                        <span className="font-medium">Data Count:</span>{' '}
                        {Array.isArray(result.data.data) ? result.data.data.length : 'N/A'}
                      </li>
                      {result.data.pagination && (
                        <>
                          <li>
                            <span className="font-medium">Total:</span>{' '}
                            {result.data.pagination.total}
                          </li>
                          <li>
                            <span className="font-medium">Page:</span>{' '}
                            {result.data.pagination.page} of {result.data.pagination.totalPages}
                          </li>
                        </>
                      )}
                      <li>
                        <span className="font-medium">Error:</span>{' '}
                        {result.data.error || 'None'}
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            🧪 Phase 2C Test Information
          </h2>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• These tests verify the refactored API routes are working correctly</li>
            <li>• All routes now use the shared server-side data layer</li>
            <li>• <strong>NEW:</strong> Retailers API infrastructure (3 routes) now implemented</li>
            <li>• Security improved by eliminating public key usage in API routes</li>
            <li>• Performance optimized with better caching strategies</li>
            <li>• Response formats standardized across all endpoints</li>
            <li>• Complete API coverage: 11/11 routes (100%)</li>
          </ul>
        </div>

        <div className="mt-6 text-center space-x-4">
          <a 
            href="/test-data-layer" 
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Test Data Layer
          </a>
          <a 
            href="/" 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            ← Back to Homepage
          </a>
        </div>
      </div>
    </div>
  )
}


