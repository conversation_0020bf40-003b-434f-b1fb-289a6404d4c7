import fs from 'fs';
import path from 'path';
import { debugConfig } from '@/config/debug.config';

const LOG_PATH = './logs/debug.log';

// Ensure log directory exists
try {
	if (!fs.existsSync('./logs')) {
		fs.mkdirSync('./logs', { recursive: true });
	}
} catch (error) {
	console.error('Failed to create logs directory:', error);
}

interface LogContext {
	component: string;
	action?: string;
	data?: Record<string, unknown>;
}

const logToFile = (message: string, data?: unknown) => {
	if (!debugConfig.enabled || !debugConfig.features?.fileLogging) return;

	const timestamp = new Date().toISOString();
	const logEntry = `[${timestamp}] ${message}\n${data ? JSON.stringify(data, null, 2) + '\n' : ''}`;

	try {
		fs.appendFileSync(LOG_PATH, logEntry);
	} catch (error) {
		console.error('Failed to write to log file:', error);
	}
};

export const debugLogger = {
	log: (message: string, context: LogContext) => {
		if (!debugConfig.enabled) return;

		const timestamp = new Date().toISOString();
		const prefix = `[${timestamp}] [${context.component}]${context.action ? ` [${context.action}]` : ''}`;

		console.log(`${prefix} ${message}`);
		if (context.data) {
			console.log(`${prefix} Data:`, context.data);
		}
		
		// Write to file if enabled
		logToFile(`${prefix} ${message}`, context.data);
	},

	error: (message: string, context: LogContext, error?: unknown) => {
		if (!debugConfig.enabled) return;

		const timestamp = new Date().toISOString();
		const prefix = `[${timestamp}] [${context.component}]${context.action ? ` [${context.action}]` : ''}`;

		console.error(`${prefix} Error: ${message}`);
		if (error) {
			console.error(`${prefix} Details:`, error);
		}
		if (context.data) {
			console.error(`${prefix} Context:`, context.data);
		}

		// Write to file if enabled
		logToFile(`${prefix} Error: ${message}`, {
			error,
			context: context.data
		});
	},

	group: (name: string, context: LogContext, fn: () => void) => {
		if (!debugConfig.enabled) return;

		const timestamp = new Date().toISOString();
		const prefix = `[${timestamp}] [${context.component}]`;
		
		const startMessage = `${prefix} ====== ${name} Start ======`;
		console.log(startMessage);
		logToFile(startMessage);
		
		fn();
		
		const endMessage = `${prefix} ====== ${name} End ======`;
		console.log(endMessage);
		logToFile(endMessage);
	}
};