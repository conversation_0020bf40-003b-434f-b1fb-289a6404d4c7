import { useState } from 'react';

interface UseProductImagesProps {
    images?: string[];
    brandLogoUrl?: string;
    productName: string;
}

interface UseProductImagesReturn {
    currentImageIndex: number;
    setCurrentImageIndex: (index: number) => void;
    imageError: boolean;
    getImageUrl: (image: string) => string;
    handleImageError: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
    hasMultipleImages: boolean;
    productImages: string[];
}

/**
 * Custom hook for managing product images
 * 
 * Handles:
 * - Image URL construction
 * - Error handling
 * - Image navigation
 * - Fallback images
 */
export function useProductImages({
    images = [],
    brandLogoUrl,
    productName
}: UseProductImagesProps): UseProductImagesReturn {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [imageError, setImageError] = useState(false);

    // Get array of available images or empty array
    const productImages = images || [];
    const hasMultipleImages = productImages.length > 1;

    // Generate fallback image URL
    const fallbackImage = brandLogoUrl || 
        `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(productName)}`;

    /**
     * Constructs the full URL for an image
     * Uses Supabase storage URL for product images
     * Falls back to brand logo or placeholder if there's an error
     */
    const getImageUrl = (image: string): string => {
        if (imageError || !image) {
            return fallbackImage;
        }

        // If it's already a full URL, return it as is
        if (image.startsWith('http')) {
            return image;
        }

        // Construct Supabase storage URL
        return `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;
    };

    /**
     * Handles image loading errors
     * Sets error state and logs details for debugging
     */
    const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        const target = e.target as HTMLImageElement;
        console.error('Image load error:', {
            attemptedSrc: target.src,
            productName
        });
        setImageError(true);
    };

    return {
        currentImageIndex,
        setCurrentImageIndex,
        imageError,
        getImageUrl,
        handleImageError,
        hasMultipleImages,
        productImages: productImages.length > 0 ? productImages : [fallbackImage]
    };
}
