import { Brand } from './database'

/**
 * Extended brand type with additional details
 * 
 * @note This type extends the base Brand interface with additional fields
 * that might be included when fetching brand details.
 * 
 * All properties use camelCase to maintain consistency with the API.
 */
export interface BrandWithDetails extends Brand {
  /**
   * Categories this brand belongs to
   */
  categories?: Array<{
    id: string
    name: string
    slug: string
  }>
  
  /**
   * Additional metadata or computed fields can be added here
   */
  [key: string]: unknown
}
