export interface RetailerOffer {
    retailer: {
        name: string;
        logo_url: string;
    };
    price: number;
    url: string;
    stock_status: string;
    created_at: string;
}

export interface ProductSpecifications {
    sku: string;
    price: string;
    features: string;
    warranty: string;
    materials: string;
    dimensions: string;
    color_finish: string;
    model_number: string;
    product_name: string;
    product_type: string;
    energy_rating: string;
    product_series: string;
    smart_features: string;
    product_image_1?: string;
    product_image_2?: string;
    product_image_3?: string;
    product_image_4?: string;
    technical_specs: string;
    product_category: string;
    brand_manufacturer: string;
    product_description: string;
    product_sub_category: string;
    product_sub_sub_category: string;
    [key: string]: string | undefined;
}

export interface Product {
    id: string;
    name: string;
    description: string;
    slug: string; // Add this line
    images?: string[];
    specifications?: ProductSpecifications;
    brand?: {
        id: string;
        name: string;
        logo_url: string;
    } | null; // Allow null
    category: {
        id: string;
        name: string;
    } | null;
    promotion: {
        id: string;
        title: string;
        terms_url: string;
        terms_description: string;
        purchase_start_date: string;
        purchase_end_date: string;
        claim_start_offset_days: number;
        claim_window_days: number;
    } | null;
    retailerOffers: RetailerOffer[];
    cashbackAmount: number;
}
