// Database table interfaces
/**
 * Brand interface representing a company or manufacturer
 * 
 * IMPORTANT: This interface represents the transformed data structure from the API.
 * All properties use camelCase to maintain consistency with the transformed data.
 * 
 * @see TransformedBrand in @/lib/data/types for the complete type with all fields.
 */
export interface Brand {
  id: string;
  name: string;
  slug: string;
  logoUrl: string | null;
  description: string | null;
  featured: boolean;
  sponsored: boolean;
  createdAt: string;
  updatedAt: string;
  version: number;
  // Additional fields that might be present in the transformed data
  productsCount?: number;
  activePromotions?: Array<{
    id: string;
    title: string;
    description: string | null;
    maxCashbackAmount: number;
    purchaseStartDate: string;
    purchaseEndDate: string;
    termsUrl: string | null;
    termsDescription: string | null;
    status: string;
    isFeatured: boolean;
  }>;
}

export interface Category {
	id: string;
	name: string;
	slug: string;
	parent_id?: string;
	featured: boolean;
	sponsored: boolean;
	created_at: string;
	version: number;
}

export interface Product {
	id: string;
	name: string;
	slug: string;
	brand_id: string;
	category_id: string;
	promotion_id?: string;  // Optional as products may not be part of a promotion
	cashback_amount?: number;  // Optional cashback amount specific to this product
	description?: string;
	specifications?: Record<string, unknown>;
	images?: string[];
	status: 'active' | 'inactive' | 'discontinued';
	is_featured: boolean;
	is_sponsored: boolean;
	created_at: string;
	updated_at: string;
	version: number;
	search_vector?: unknown;
}

export interface Retailer {
	id: string;
	name: string;
	slug: string;
	logo_url?: string;
	status: string;
	featured: boolean;
	sponsored: boolean;
	created_at: string;
	version: number;
}

export interface Promotion {
	id: string;
	brand_id?: string;
	category_id?: string;
	title: string;
	description?: string;
	cashback_amount: number;
	purchase_start_date: string;
	purchase_end_date: string;
	claim_start_offset_days: number;
	claim_window_days: number;
	terms_url?: string;
	terms_description?: string;
	status: 'draft' | 'scheduled' | 'active' | 'paused' | 'expired' | 'cancelled';
	last_validated_at?: string;
	created_at: string;
	version: number;
}

export interface ProductRetailerPromotion {
	id: string;
	product_id: string;
	retailer_id: string;
	promotion_id?: string;
	sku_id: string;
	price: number;
	stock_status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'discontinued' | 'pre_order';
	url?: string;
	valid_from: string;
	valid_until: string;
	created_at: string;
	updated_at: string;
	version: number;
}

// Utility type for database responses
export type DatabaseResponse<T> = {
	data: T | null;
	error: string | null;
};