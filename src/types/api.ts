import { Brand, Category, Promotion } from './database'
import { DebugData } from '@/config/debug.config'

export interface FeaturedProduct {
	brand: Pick<Brand, 'id' | 'name'>;
	category: Pick<Category, 'name'>;
	promotion: Pick<Promotion, 'id' | 'description' | 'purchase_end_date'>;
}

export interface ApiResponse<T> {
	data: T;
	error?: string;
	debug?: DebugData;
}

export interface FeaturedProductsResponse extends ApiResponse<FeaturedProduct[]> {}