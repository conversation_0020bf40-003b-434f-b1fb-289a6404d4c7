import React from 'react';
import { motion } from 'framer-motion';
import { ProductCard } from './ProductCard'; // Assuming ProductCard is in the same directory or accessible
import { TransformedProduct } from '@/lib/data/types'; // Adjust path as necessary

interface ProductGridProps {
  products: TransformedProduct[];
  currentPage: number; // Pass currentPage for ProductCard
  previousProductCount?: number;
}

export function ProductGrid({ products, currentPage, previousProductCount = 0 }: ProductGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map((product, index) => (
        <React.Fragment key={product.id}>
          {index === previousProductCount && previousProductCount > 0 && (
            <div className="col-span-full my-4">
              <hr />
              <div className="text-center text-sm text-gray-500 py-2">Page {currentPage}</div>
            </div>
          )}
          <motion.div
            data-product-index={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: (index - previousProductCount) * 0.1 }}
            whileHover={{ y: -5 }}
          >
            <ProductCard product={product} currentPage={currentPage} />
          </motion.div>
        </React.Fragment>
      ))}
    </div>
  );
}
