import Link from 'next/link';
import { motion } from 'framer-motion';

interface Brand {
    id: string;
    name: string;
}

interface Category {
    name: string;
}

interface Promotion {
    id: string;
    description: string | null;
    purchase_end_date: string;
}

interface FeaturedPromotionCardProps {
    brand: Brand | null;
    category: Category | null;
    promotion: Promotion;
}

export function FeaturedPromotionCard({ brand, category, promotion }: FeaturedPromotionCardProps) {
    return (
        <Link href={`/products?promotion_id=${promotion.id}`} className="block h-full">
            <motion.div
                whileHover={{ y: -2 }}
                className="group relative overflow-hidden rounded-lg border bg-white p-4 shadow-sm transition-all hover:shadow-md h-full flex flex-col"
            >
                <div className="flex items-center gap-3 mb-3">
                    <div className="h-9 w-9 rounded-full bg-accent flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-sm font-medium">
                            {brand?.name?.[0]?.toUpperCase() || '?'}
                        </span>
                    </div>
                    <div className="min-w-0">
                        <h3 className="text-sm font-medium text-primary truncate">{brand?.name || 'Special Offer'}</h3>
                        {category?.name && (
                            <p className="text-xs text-foreground/60 truncate">{category.name}</p>
                        )}
                    </div>
                </div>
                <div className="space-y-1.5 flex-1 flex flex-col">
                    {promotion.description ? (
                        <p className="text-sm text-foreground/80 line-clamp-3 flex-1">{promotion.description}</p>
                    ) : (
                        <p className="text-sm text-foreground/60 italic flex-1">No description available</p>
                    )}
                    <p className="text-xs text-foreground/60 mt-2 pt-2 border-t border-gray-100">
                        Valid until: {new Date(promotion.purchase_end_date).toLocaleDateString('en-GB', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric'
                        })}
                    </p>
                </div>
            </motion.div>
        </Link>
    );
}
