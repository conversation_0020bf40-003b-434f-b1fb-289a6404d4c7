'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { ChevronLeft, ChevronRight } from 'lucide-react'

const ALPHABET = '#ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('')

interface AlphabetNavigationProps extends React.HTMLAttributes<HTMLDivElement> {
	onLetterClickAction: (letter: string) => void
	activeLetter?: string
}

export function AlphabetNavigation({
	className,
	onLetterClickAction,
	activeLetter,
	...props
}: AlphabetNavigationProps) {
	const scrollContainerRef = React.useRef<HTMLDivElement>(null)
	const [showLeftArrow, setShowLeftArrow] = React.useState(false)
	const [showRightArrow, setShowRightArrow] = React.useState(false)

	const checkScroll = React.useCallback(() => {
		const container = scrollContainerRef.current
		if (container) {
			setShowLeftArrow(container.scrollLeft > 0)
			setShowRightArrow(
				container.scrollLeft < container.scrollWidth - container.clientWidth
			)
		}
	}, [])

	React.useEffect(() => {
		const container = scrollContainerRef.current
		if (container) {
			checkScroll()
			container.addEventListener('scroll', checkScroll)
			window.addEventListener('resize', checkScroll)
		}

		return () => {
			if (container) {
				container.removeEventListener('scroll', checkScroll)
				window.removeEventListener('resize', checkScroll)
			}
		}
	}, [checkScroll])

	const scroll = (direction: 'left' | 'right') => {
		const container = scrollContainerRef.current
		if (container) {
			const scrollAmount = container.clientWidth / 2
			container.scrollBy({
				left: direction === 'left' ? -scrollAmount : scrollAmount,
				behavior: 'smooth',
			})
		}
	}

	return (
		<div
			className={cn(
				'flex items-center w-full min-h-[3rem]',
				className
			)}
			{...props}
		>
			{showLeftArrow && (
				<button
					onClick={() => scroll('left')}
					className="p-2 hover:bg-accent rounded-full flex-shrink-0"
					aria-label="Scroll left"
				>
					<ChevronLeft className="h-4 w-4" />
				</button>
			)}
			<div
				ref={scrollContainerRef}
				className="flex-1 overflow-x-auto flex items-center scrollbar-hide snap-x snap-mandatory"
			>
				<div className="flex min-w-full px-4 py-2 justify-between md:justify-start md:gap-2">
					{ALPHABET.map((letter) => (
						<button
							key={letter}
							onClick={() => onLetterClickAction(letter)}

							className={cn(
								'min-w-[2rem] h-8 flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-white flex-shrink-0',
								activeLetter === letter && 'bg-primary text-white'
							)}
						>
							{letter}
						</button>
					))}
				</div>
			</div>
			{showRightArrow && (
				<button
					onClick={() => scroll('right')}
					className="p-2 hover:bg-accent rounded-full flex-shrink-0"
					aria-label="Scroll right"
				>
					<ChevronRight className="h-4 w-4" />
				</button>
			)}
		</div>
	)
}