// src/components/ui/OptimizedImage.tsx
// Enhanced image component with SEO optimization, accessibility, and performance features

import Image from 'next/image';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
    src: string;
    alt: string;
    width?: number;
    height?: number;
    fill?: boolean;
    priority?: boolean;
    className?: string;
    sizes?: string;
    quality?: number;
    placeholder?: 'blur' | 'empty';
    blurDataURL?: string;
    onLoad?: () => void;
    onError?: () => void;
    fallbackSrc?: string;
    // SEO and accessibility enhancements
    title?: string;
    loading?: 'lazy' | 'eager';
    // Product-specific props for better alt text generation
    productName?: string;
    brandName?: string;
    imageType?: 'product' | 'brand-logo' | 'thumbnail' | 'hero' | 'icon';
    imageIndex?: number;
    totalImages?: number;
}

/**
 * Enhanced image component with automatic alt text generation, 
 * fallback handling, and SEO optimization
 */
export function OptimizedImage({
    src,
    alt,
    width,
    height,
    fill = false,
    priority = false,
    className,
    sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
    quality = 85,
    placeholder = 'blur',
    blurDataURL = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
    onLoad,
    onError,
    fallbackSrc,
    title,
    loading,
    productName,
    brandName,
    imageType = 'product',
    imageIndex,
    totalImages
}: OptimizedImageProps) {
    const [hasError, setHasError] = useState(false);
    const [isLoaded, setIsLoaded] = useState(false);

    // Generate enhanced alt text based on context
    const generateAltText = (): string => {
        if (alt && alt.trim() !== '') {
            return alt;
        }

        // Generate contextual alt text based on image type
        switch (imageType) {
            case 'product':
                if (productName && brandName) {
                    const imagePosition = imageIndex !== undefined && totalImages !== undefined
                        ? ` - Image ${imageIndex + 1} of ${totalImages}`
                        : '';
                    return `${productName} by ${brandName}${imagePosition}`;
                }
                return productName || 'Product image';

            case 'brand-logo':
                return brandName ? `${brandName} logo` : 'Brand logo';

            case 'thumbnail':
                if (productName && imageIndex !== undefined) {
                    return `${productName} thumbnail ${imageIndex + 1}`;
                }
                return 'Product thumbnail';

            case 'hero':
                return productName ? `${productName} hero image` : 'Hero image';

            case 'icon':
                return brandName ? `${brandName} icon` : 'Icon';

            default:
                return alt || 'Image';
        }
    };

    // Generate title attribute for additional context
    const generateTitle = (): string | undefined => {
        if (title) return title;

        switch (imageType) {
            case 'product':
                return productName && brandName
                    ? `View ${productName} by ${brandName}`
                    : undefined;
            case 'brand-logo':
                return brandName ? `${brandName} brand` : undefined;
            default:
                return undefined;
        }
    };

    // Handle image loading errors with fallback
    const handleError = () => {
        setHasError(true);
        onError?.();
    };

    // Handle successful image loading
    const handleLoad = () => {
        setIsLoaded(true);
        onLoad?.();
    };

    // Determine the image source (with fallback)
    const imageSrc = hasError && fallbackSrc ? fallbackSrc : src;

    // Generate responsive sizes based on image type
    const getResponsiveSizes = (): string => {
        if (sizes !== "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw") {
            return sizes;
        }

        switch (imageType) {
            case 'hero':
                return "(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 60vw";
            case 'product':
                return "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw";
            case 'thumbnail':
                return "(max-width: 768px) 25vw, (max-width: 1200px) 15vw, 10vw";
            case 'brand-logo':
                return "(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 15vw";
            case 'icon':
                return "48px";
            default:
                return sizes;
        }
    };

    const imageProps = {
        src: imageSrc,
        alt: generateAltText(),
        title: generateTitle(),
        quality,
        placeholder,
        blurDataURL,
        onLoad: handleLoad,
        onError: handleError,
        loading: loading || (priority ? 'eager' : 'lazy'),
        sizes: getResponsiveSizes(),
        className: cn(
            'transition-opacity duration-300',
            !isLoaded && 'opacity-0',
            isLoaded && 'opacity-100',
            className
        ),
        ...(fill ? { fill: true } : { width, height }),
        priority
    };

    return <Image {...imageProps} />;
}

// Specialized components for common use cases
export function ProductImage(props: Omit<OptimizedImageProps, 'imageType'>) {
    return <OptimizedImage {...props} imageType="product" />;
}

export function BrandLogo(props: Omit<OptimizedImageProps, 'imageType'>) {
    return <OptimizedImage {...props} imageType="brand-logo" />;
}

export function ProductThumbnail(props: Omit<OptimizedImageProps, 'imageType'>) {
    return <OptimizedImage {...props} imageType="thumbnail" />;
}

export function HeroImage(props: Omit<OptimizedImageProps, 'imageType'>) {
    return <OptimizedImage {...props} imageType="hero" />;
}
