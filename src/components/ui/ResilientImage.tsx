/**
 * Resilient Image Component with timeout handling and fallback mechanisms
 * Specifically designed to handle Samsung image server timeouts and failures
 */

'use client';

import Image from 'next/image';
import { useState, useEffect, useCallback } from 'react';
import { validateImageUrl, getCircuitBreakerStatus } from '@/lib/imageLoader';
import {
    getFallbackImageUrl,
    isSamsungImage,
    createImageErrorHandler,
    validateImageWithFallback
} from '@/lib/imageUtils';
import { cn } from '@/lib/utils';

interface ResilientImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  priority?: boolean;
  className?: string;
  sizes?: string;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: (error: string) => void;
  fallbackSrc?: string;
  // Product-specific props for better fallback generation
  productName?: string;
  brandName?: string;
  // Resilience configuration
  enableValidation?: boolean;
  showLoadingState?: boolean;
  retryOnError?: boolean;
}

/**
 * Enhanced image component with resilience features for external image loading
 */
export function ResilientImage({
  src,
  alt,
  width = 600,
  height = 600,
  fill = false,
  priority = false,
  className,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  quality = 85,
  placeholder = 'blur',
  blurDataURL = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
  onLoad,
  onError,
  fallbackSrc,
  productName,
  brandName,
  enableValidation = true,
  showLoadingState = true,
  retryOnError = true,
}: ResilientImageProps) {
  const [imageSrc, setImageSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isValidating, setIsValidating] = useState(false);

  // Maximum retry attempts
  const MAX_RETRIES = 2;

  /**
   * Generate enhanced fallback image URL based on product information
   */
  const generateFallbackUrl = useCallback((level: 'primary' | 'secondary' | 'tertiary' = 'primary') => {
    if (fallbackSrc) return fallbackSrc;

    return getFallbackImageUrl({
      brandLogoUrl: fallbackSrc,
      productName,
      brandName,
      originalUrl: src,
      width,
      height,
      quality,
      fallbackLevel: level
    });
  }, [fallbackSrc, productName, brandName, src, width, height, quality]);

  /**
   * Enhanced validation for image URLs with detailed error handling
   */
  const validateImage = useCallback(async (imageUrl: string) => {
    if (!enableValidation) {
      return { isValid: true, fallbackUrl: null };
    }

    setIsValidating(true);

    try {
      // Use enhanced validation from imageUtils
      const result = await validateImageWithFallback(imageUrl, {
        productName,
        brandName,
        brandLogoUrl: fallbackSrc,
        timeoutMs: 8000 // 8 second timeout
      });

      setIsValidating(false);

      if (!result.isValid) {
        setValidationError(result.error || 'Image validation failed');

        // Log recommendations in development
        if (process.env.NODE_ENV === 'development' && result.recommendations.length > 0) {
          console.info(`Image optimization recommendations for ${imageUrl}:`, result.recommendations);
        }

        return {
          isValid: false,
          fallbackUrl: result.fallbackUrl || generateFallbackUrl('secondary')
        };
      }

      return { isValid: true, fallbackUrl: null };
    } catch (error) {
      setIsValidating(false);
      const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';
      setValidationError(errorMessage);

      // Use tertiary fallback for unexpected errors
      return { isValid: false, fallbackUrl: generateFallbackUrl('tertiary') };
    }
  }, [enableValidation, productName, brandName, fallbackSrc, generateFallbackUrl]);

  /**
   * Handle image load success
   */
  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
    setValidationError(null);
    onLoad?.();
  }, [onLoad]);

  /**
   * Handle image load error with retry logic
   */
  const handleError = useCallback(async () => {
    console.warn(`Image load error for: ${imageSrc}`);
    
    setIsLoading(false);
    setHasError(true);

    // Try to retry if enabled and we haven't exceeded max retries
    if (retryOnError && retryCount < MAX_RETRIES && imageSrc === src) {
      console.log(`Retrying image load (attempt ${retryCount + 1}/${MAX_RETRIES})`);
      setRetryCount(prev => prev + 1);
      
      // Wait a bit before retrying
      setTimeout(() => {
        setIsLoading(true);
        setHasError(false);
        // Force re-render by updating the src with a cache-busting parameter
        setImageSrc(`${src}?retry=${retryCount + 1}`);
      }, 1000 * (retryCount + 1)); // Exponential backoff
      
      return;
    }

    // If retries exhausted or retry disabled, use fallback
    const fallbackUrl = generateFallbackUrl();
    setImageSrc(fallbackUrl);
    
    const errorMessage = validationError || `Failed to load image after ${retryCount} retries`;
    onError?.(errorMessage);
  }, [imageSrc, src, retryOnError, retryCount, generateFallbackUrl, validationError, onError]);

  /**
   * Validate image on mount and when src changes
   */
  useEffect(() => {
    let isMounted = true;

    const validateAndSetImage = async () => {
      // Reset state when src changes
      setRetryCount(0);
      setHasError(false);
      setValidationError(null);
      setIsLoading(true);

      const validation = await validateImage(src);
      
      if (!isMounted) return;

      if (validation.isValid) {
        setImageSrc(src);
      } else {
        console.warn(`Image validation failed for ${src}, using fallback`);
        setImageSrc(validation.fallbackUrl || generateFallbackUrl());
        setValidationError(validation.error || 'Validation failed');
      }
    };

    validateAndSetImage();

    return () => {
      isMounted = false;
    };
  }, [src, validateImage, generateFallbackUrl]);

  /**
   * Generate loading placeholder
   */
  const renderLoadingState = () => {
    if (!showLoadingState || !isLoading) return null;
    
    return (
      <div 
        className={cn(
          "absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center",
          className
        )}
        style={{ width: fill ? '100%' : width, height: fill ? '100%' : height }}
      >
        <div className="text-gray-400 text-sm">
          {isValidating ? 'Validating...' : 'Loading...'}
        </div>
      </div>
    );
  };

  /**
   * Generate alt text with error information
   */
  const getAltText = () => {
    if (hasError || validationError) {
      return `${alt} (fallback image)`;
    }
    return alt;
  };

  const imageProps = {
    src: imageSrc,
    alt: getAltText(),
    quality,
    placeholder,
    blurDataURL,
    onLoad: handleLoad,
    onError: handleError,
    sizes,
    className: cn(
      'transition-opacity duration-300',
      isLoading && 'opacity-0',
      !isLoading && 'opacity-100',
      hasError && 'opacity-90', // Slightly faded for fallback images
      className
    ),
    ...(fill ? { fill: true } : { width, height }),
    priority
  };

  return (
    <div className="relative">
      {renderLoadingState()}
      <Image {...imageProps} />
      
      {/* Debug information in development */}
      {process.env.NODE_ENV === 'development' && (validationError || hasError) && (
        <div className="absolute bottom-0 left-0 right-0 bg-red-500 bg-opacity-75 text-white text-xs p-1">
          {validationError || 'Image load error'} (Retries: {retryCount})
        </div>
      )}
    </div>
  );
}

/**
 * Specialized component for product images with Samsung-specific handling
 */
export function ProductImage(props: Omit<ResilientImageProps, 'enableValidation'>) {
  return <ResilientImage {...props} enableValidation={true} />;
}

/**
 * Hook to get circuit breaker status for monitoring
 */
export function useImageCircuitBreakerStatus() {
  const [status, setStatus] = useState({});

  useEffect(() => {
    const updateStatus = () => {
      setStatus(getCircuitBreakerStatus());
    };

    updateStatus();
    const interval = setInterval(updateStatus, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, []);

  return status;
}
