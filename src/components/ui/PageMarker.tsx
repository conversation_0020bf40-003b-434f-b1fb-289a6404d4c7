import { motion } from 'framer-motion';
import { ArrowUp } from 'lucide-react';

interface PageMarkerProps {
  pageNumber?: number;
  className?: string;
}

export function PageMarker({ pageNumber, className = '' }: PageMarkerProps) {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  return (
    <div className={`flex flex-col items-center space-y-2 my-4 ${className}`}>
      <div className="flex items-center w-full">
        <div className="flex-grow border-t border-gray-200"></div>
        {pageNumber && (
          <motion.button
            onClick={scrollToTop}
            className="flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-full"
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.98 }}
            aria-label={`Page ${pageNumber} - Back to top`}
            title={`Back to top from page ${pageNumber}`}
          >
            <span>Page {pageNumber}</span>
            <ArrowUp className="h-4 w-4" />
          </motion.button>
        )}
        {!pageNumber && (
          <motion.button
            onClick={scrollToTop}
            className="flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-full"
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.98 }}
            aria-label="Back to top"
            title="Back to top"
          >
            <span>Back to top</span>
            <ArrowUp className="h-4 w-4" />
          </motion.button>
        )}
        <div className="flex-grow border-t border-gray-200"></div>
      </div>
    </div>
  );
}
