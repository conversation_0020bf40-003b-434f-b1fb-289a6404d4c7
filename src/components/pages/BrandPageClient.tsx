'use client';

import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight, Tag, Clock } from 'lucide-react';
import { isDebugEnabled } from '@/config/debug.config';
import { DebugPanel } from '@/components/debug/DebugPanel';
import Link from 'next/link';
import { BrandLogo, ProductImage } from '@/components/ui/OptimizedImage';
import { TransformedBrand, TransformedProduct, TransformedPromotion } from '@/lib/data/types';

interface BrandPageClientProps {
    brand: TransformedBrand;
    products: TransformedProduct[];
    promotions: TransformedPromotion[];
}

import { useState, useEffect } from 'react';

export function BrandPageClient({ brand, products, promotions }: BrandPageClientProps) {
    const [promotionsCount, setPromotionsCount] = useState(0);
    const [activePromotionsCount, setActivePromotionsCount] = useState(0);

    useEffect(() => {
        setPromotionsCount(promotions.length);
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0); // Set to start of day for consistent comparison

        const activePromos = promotions.filter(promo => {
            const validUntil = new Date(promo.purchaseEndDate);
            validUntil.setHours(23, 59, 59, 999); // Set to end of day
            return validUntil >= currentDate && promo.status === 'active';
        });

        setActivePromotionsCount(activePromos.length);
    }, [promotions]);

    const inactivePromotions = promotions.filter(promo => {
        const validUntil = new Date(promo.purchaseEndDate);
        validUntil.setHours(23, 59, 59, 999); // Set to end of day
        return validUntil < new Date() || promo.status === 'expired';
    });

    return (
        <div className="flex flex-col min-h-screen">
            <Link
                href="/brands"
                className="container py-4 inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/90"
            >
                <ArrowLeft className="h-4 w-4" /> Back to Brands
            </Link>

            {/* Hero Section */}
            <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
                role="banner"
                aria-label="Brand Information"
            >
                <div className="container">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="grid md:grid-cols-2 gap-8 items-center"
                    >
                        <div className="h-64 bg-secondary/10 rounded-lg flex items-center justify-center overflow-hidden p-8">
                            {brand.logoUrl ? (
                                <div className="relative w-full h-full max-w-[200px] mx-auto flex items-center justify-center">
                                    <BrandLogo
                                        src={brand.logoUrl}
                                        alt={`${brand.name} brand logo`}
                                        width={200}
                                        height={200}
                                        className="object-contain max-w-full max-h-full w-auto h-auto"
                                        loading="eager"
                                        priority={true}
                                        brandName={brand.name}
                                        fallbackSrc={`https://placehold.co/600x600/f1f5f5/64748b.png?text=${encodeURIComponent(brand.name)}`}
                                    />
                                </div>
                            ) : (
                                <div className="text-2xl font-bold text-secondary/40">{brand.name}</div>
                            )}
                        </div>
                        <div>
                            <h1 className="text-4xl font-bold text-primary mb-6">{brand.name}</h1>
                            <p className="text-lg text-foreground/70 mb-6">
                                {brand.description || `Discover cashback deals and offers from ${brand.name}. Save money on your purchases with our exclusive cashback opportunities.`}
                            </p>
                            <div className="flex flex-wrap gap-4">
                                <div className="flex items-center gap-2 text-sm text-foreground/70">
                                    <Tag className="h-4 w-4 text-primary" />
                                    <span>{promotionsCount} Total Promotion(s)</span>
                                    <Clock className="h-4 w-4 text-primary" />
                                    <span>{activePromotionsCount} Active</span>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </motion.section>

            {/* Content */}
            <div className="container py-12">
                {/* Active Promotions */}
                <h2 className="text-2xl font-bold text-primary mb-6">{brand.name} Cashback Current Promotions</h2>
                <div className="space-y-4 mb-12">
                    {promotions.length > 0 ? (
                        promotions.map((promo, i) => (
                            <motion.div
                                key={promo.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: i * 0.1 }}
                                className="card p-6"
                            >
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h3 className="font-semibold text-primary mb-2">{promo.title}</h3>
                                        <div className="flex items-center gap-2 mb-2">
                                            <Tag className="h-4 w-4 text-primary" />
                                            <span className="text-sm text-foreground/70">
                                                {promo.category?.name || 'Category not available'}
                                            </span>
                                        </div>
                                        <p className="text-sm text-foreground/70">
                                            Valid until {new Date(promo.purchaseEndDate).toLocaleDateString('en-GB')}
                                        </p>
                                    </div>
                                    <Link
                                        href={`/products?promotion_id=${promo.id}`}
                                        className="inline-flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90"
                                    >
                                        View Products <ArrowRight className="h-4 w-4" />
                                    </Link>
                                </div>
                            </motion.div>
                        ))
                    ) : (
                        <div className="text-center py-12">
                            <p className="text-foreground/70">No active promotions available for {brand.name} at the moment.</p>
                        </div>
                    )}
                </div>

                {/* Featured Products */}
                {products.length > 0 && (
                    <>
                        <h3 className="text-xl font-bold text-primary mb-6">Featured {brand.name} Products</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                            {products.slice(0, 6).map((product) => (
                                <Link
                                    key={product.id}
                                    href={`/products/${product.slug || product.id}`}
                                    className="group"
                                >
                                    <div className="card p-4 hover:shadow-lg transition-shadow">
                                        {product.images && product.images.length > 0 && (
                                            <ProductImage
                                                src={product.images[0]}
                                                alt={`${product.name} by ${brand.name}`}
                                                width={300}
                                                height={128}
                                                className="h-32 w-full object-cover rounded mb-3"
                                                productName={product.name}
                                                brandName={brand.name}
                                                loading="lazy"
                                            />
                                        )}
                                        <h4 className="font-medium text-sm group-hover:text-primary transition-colors mb-2">
                                            {product.name}
                                        </h4>
                                        {product.cashbackAmount && (
                                            <p className="text-xs text-green-600 font-medium">
                                                £{product.cashbackAmount.toFixed(2)} Cashback
                                            </p>
                                        )}
                                    </div>
                                </Link>
                            ))}
                        </div>
                    </>
                )}

                {/* Inactive Promotions */}
                {inactivePromotions.length > 0 && (
                    <>
                        <h4 className="text-xl font-semibold text-gray-500 mb-6">
                            {brand.name} Cashback Missed Promotions
                        </h4>
                        <div className="space-y-4">
                            {inactivePromotions.map((promo, i) => (
                                <motion.div
                                    key={promo.id}
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: i * 0.1 }}
                                    className="card p-6"
                                >
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h3 className="font-semibold text-gray-400 mb-2">{promo.title}</h3>
                                            <div className="flex items-center gap-2 mb-2">
                                                <Tag className="h-4 w-4 text-gray-400" />
                                                <span className="text-sm text-gray-400">
                                                    {promo.category?.name || 'Category not available'}
                                                </span>
                                            </div>
                                            <p className="text-sm text-gray-400">
                                                Expired on {new Date(promo.purchaseEndDate).toLocaleDateString('en-GB')}
                                            </p>
                                        </div>
                                        <div className="inline-flex items-center gap-2 rounded-lg bg-gray-200 px-4 py-2 text-sm font-medium text-gray-500">
                                            Expired <ArrowRight className="h-4 w-4" />
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </>
                )}

                {/* Debug Panel */}
                {isDebugEnabled() && (
                    <div className="mt-8">
                        <DebugPanel
                            data={{
                                timing: { start: Date.now(), end: Date.now(), duration: 0 },
                                queries: [`Fetching brand: ${brand.id}`],
                                params: { id: brand.id },
                            }}
                            title="Brand Debug Info"
                        />
                    </div>
                )}
            </div>
        </div>
    );
}
