'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Star, Store, TrendingUp, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { BrandLogo } from '@/components/ui/OptimizedImage';
import { TransformedRetailer } from '@/lib/data/types';

interface RetailersPageClientProps {
    retailers: TransformedRetailer[];
    totalCount?: number;
    currentPage: number;
    searchQuery: string;
    featuredOnly: boolean;
}

export function RetailersPageClient({
    retailers,
    totalCount,
    currentPage,
    searchQuery,
    featuredOnly
}: RetailersPageClientProps) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [localSearch, setLocalSearch] = useState(searchQuery);

    const totalPages = Math.ceil(totalCount || 0 / 24);
    const featuredCount = retailers.filter(r => r.featured).length;

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        const params = new URLSearchParams(searchParams);

        if (localSearch.trim()) {
            params.set('search', localSearch.trim());
        } else {
            params.delete('search');
        }
        params.delete('page'); // Reset to first page on new search

        router.push(`/retailers?${params.toString()}`);
    };

    const handleFeaturedFilter = () => {
        const params = new URLSearchParams(searchParams);

        if (featuredOnly) {
            params.delete('featured');
        } else {
            params.set('featured', 'true');
        }
        params.delete('page'); // Reset to first page on filter change

        router.push(`/retailers?${params.toString()}`);
    };

    const handlePageChange = (page: number) => {
        const params = new URLSearchParams(searchParams);
        params.set('page', page.toString());
        router.push(`/retailers?${params.toString()}`);
    };

    return (
        <div className="container py-12">
            {/* Header */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center mb-12"
            >
                <h1 className="text-4xl font-bold text-primary mb-4">
                    All Retailers
                </h1>
                <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
                    {`Browse over ${(totalCount || 0).toLocaleString()} retailers offering cashback deals and exclusive offers.`}                    Find your favorite stores and start earning money back on your purchases.
                </p>
            </motion.div>

            {/* Search and Filters */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="mb-8"
            >
                <form onSubmit={handleSearch} className="mb-4">
                    <div className="relative max-w-md mx-auto">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/40" />
                        <input
                            type="text"
                            placeholder="Search retailers..."
                            value={localSearch}
                            onChange={(e) => setLocalSearch(e.target.value)}
                            className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                        />
                    </div>
                </form>

                <div className="flex justify-center gap-4">
                    <button
                        onClick={handleFeaturedFilter}
                        className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${featuredOnly
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-secondary/10 text-foreground hover:bg-secondary/20'
                            }`}
                    >
                        <Star className="h-4 w-4" />
                        Featured Only
                    </button>
                </div>
            </motion.div>

            {/* Stats */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
            >
                <div className="bg-primary/5 rounded-lg p-6 text-center">
                    <Store className="h-8 w-8 text-primary mx-auto mb-2" />
                    <div className="text-2xl font-bold text-primary">{totalCount?.toLocaleString() || 0}</div>
                    <div className="text-sm text-foreground/70">Total Retailers</div>
                </div>
                <div className="bg-secondary/5 rounded-lg p-6 text-center">
                    <Star className="h-8 w-8 text-secondary mx-auto mb-2" />
                    <div className="text-2xl font-bold text-secondary">{featuredCount}</div>
                    <div className="text-sm text-foreground/70">Featured Retailers</div>
                </div>
                <div className="bg-green-500/5 rounded-lg p-6 text-center">
                    <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">100%</div>
                    <div className="text-sm text-foreground/70">Cashback Guaranteed</div>
                </div>
            </motion.div>

            {/* Results Info */}
            {(searchQuery || featuredOnly) && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="mb-6 text-center text-foreground/70"
                >
                    Showing {retailers.length} of {totalCount} retailers
                    {searchQuery && ` for "${searchQuery}"`}
                    {featuredOnly && ' (featured only)'}
                </motion.div>
            )}

            {/* Retailers Grid */}
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12"
            >
                {retailers.map((retailer, index) => (
                    <motion.div
                        key={retailer.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                    >
                        <Link
                            href={`/retailers/${retailer.slug || retailer.id}`}
                            className="group block"
                        >
                            <div className="card p-6 hover:shadow-lg transition-shadow relative">
                                {retailer.featured && (
                                    <div className="absolute top-2 right-2">
                                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                    </div>
                                )}

                                <div className="flex flex-col items-center text-center">
                                    <div className="h-16 w-16 mb-4 flex items-center justify-center">
                                        {retailer.logoUrl ? (
                                            <BrandLogo
                                                src={retailer.logoUrl}
                                                alt={`${retailer.name} logo`}
                                                width={64}
                                                height={64}
                                                className="max-h-full max-w-full object-contain"
                                                brandName={retailer.name}
                                                loading="lazy"
                                            />
                                        ) : (
                                            <div className="h-16 w-16 bg-secondary/10 rounded-lg flex items-center justify-center">
                                                <Store className="h-8 w-8 text-secondary/40" />
                                            </div>
                                        )}
                                    </div>

                                    <h3 className="font-semibold text-primary group-hover:text-primary/80 transition-colors mb-2">
                                        {retailer.name}
                                    </h3>

                                    {retailer.name && (
                                        <p className="text-sm text-foreground/60 mb-3 line-clamp-2">
                                            {retailer.name}
                                        </p>
                                    )}

                                    <div className="text-xs text-foreground/50">
                                        {retailer.claimPeriod || 'Cashback Available'}
                                    </div>
                                </div>
                            </div>
                        </Link>
                    </motion.div>
                ))}
            </motion.div>

            {/* No Results */}
            {retailers.length === 0 && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-12"
                >
                    <Store className="h-16 w-16 text-foreground/20 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-foreground/70 mb-2">
                        No retailers found
                    </h3>
                    <p className="text-foreground/60 mb-6">
                        Try adjusting your search terms or removing filters.
                    </p>
                    <Link
                        href="/retailers"
                        className="inline-flex items-center gap-2 text-primary hover:text-primary/90 font-medium"
                    >
                        View All Retailers
                    </Link>
                </motion.div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    className="flex justify-center"
                >
                    <div className="flex gap-2">
                        {/* Previous button */}
                        {currentPage > 1 && (
                            <button
                                onClick={() => handlePageChange(currentPage - 1)}
                                className="px-3 py-2 rounded-lg border border-border hover:bg-secondary/10 transition-colors"
                            >
                                Previous
                            </button>
                        )}

                        {/* Page numbers */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            const pageNum = Math.max(1, currentPage - 2) + i;
                            if (pageNum > totalPages) return null;

                            return (
                                <button
                                    key={pageNum}
                                    onClick={() => handlePageChange(pageNum)}
                                    className={`px-3 py-2 rounded-lg transition-colors ${pageNum === currentPage
                                        ? 'bg-primary text-primary-foreground'
                                        : 'border border-border hover:bg-secondary/10'
                                        }`}
                                >
                                    {pageNum}
                                </button>
                            );
                        })}

                        {/* Next button */}
                        {currentPage < totalPages && (
                            <button
                                onClick={() => handlePageChange(currentPage + 1)}
                                className="px-3 py-2 rounded-lg border border-border hover:bg-secondary/10 transition-colors"
                            >
                                Next
                            </button>
                        )}
                    </div>
                </motion.div>
            )}
        </div>
    );
}
