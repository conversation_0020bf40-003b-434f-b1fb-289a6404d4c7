'use client';

import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { ArrowUp, Search, Filter, SlidersHorizontal } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { ProductCard } from '@/components/ProductCard';
import { ProductGrid } from '@/components/ProductGrid';
import { TransformedProduct } from '@/lib/data/types';
import { logger } from '@/lib/utils/logger';

import { SearchResultsStructuredData } from '@/components/seo/StructuredData';

interface SearchPageClientProps {
    initialProducts: TransformedProduct[];
    totalCount: number;
    currentPage: number;
    searchQuery: string;
    category?: string;
    subcategory?: string;
}

type SortOption = 'recommended' | 'price_asc' | 'price_desc' | 'cashback_desc' | 'cashback_asc' | 'newest';

const sortOptions: Record<SortOption, string> = {
    recommended: 'Recommended',
    price_asc: 'Price: Low to High',
    price_desc: 'Price: High to Low',
    cashback_desc: 'Cashback: High to Low',
    cashback_asc: 'Cashback: Low to High',
    newest: 'Newest First',
};

export function SearchPageClient({
    initialProducts,
    totalCount,
    currentPage: initialPage,
    searchQuery,
    category,
    subcategory
}: SearchPageClientProps) {
    // [2025-07-02 15:04+01:00] Add refs for scroll position management
    const loadMoreButtonRef = useRef<HTMLButtonElement>(null);
    const scrollPositionRef = useRef<{ x: number; y: number } | null>(null);
    console.log('SearchPageClient: Props received - initialProducts.length:', initialProducts.length, 'searchQuery:', searchQuery, 'initialPage:', initialPage);
    const router = useRouter();
    const searchParams = useSearchParams();
    const [products, setProducts] = useState(initialProducts);
    const [currentPage, setCurrentPage] = useState(initialPage);
    const [hasMore, setHasMore] = useState(initialProducts.length < totalCount);
    const [isLoading, setIsLoading] = useState(false);
    const [localSearch, setLocalSearch] = useState(searchQuery);
    const [selectedSort, setSelectedSort] = useState<SortOption>('recommended');
    const [showFilters, setShowFilters] = useState(false);
    const [showBackToTop, setShowBackToTop] = useState(false);
    const productsPerPage = 20; // Match this with your API's page size
    const requestIdRef = useRef<string>(Math.random().toString(36).substring(2, 10));

    useEffect(() => {
        logger.debug('SearchPageClient: Initializing with props', {
            requestId: requestIdRef.current,
            initialProductsCount: initialProducts.length,
            totalCount,
            currentPage: initialPage,
            searchQuery: searchQuery ? '[REDACTED]' : '',
            hasQuery: !!searchQuery,
            hasCategory: !!category,
            hasSubcategory: !!subcategory
        });

        setProducts(initialProducts);
        setCurrentPage(initialPage);
        setHasMore(initialProducts.length < totalCount);
        setLocalSearch(searchQuery);

        // Log initial page view
        logger.info('Search page viewed', {
            requestId: requestIdRef.current,
            pageType: 'search',
            searchQuery: searchQuery ? '[REDACTED]' : '',
            category,
            subcategory,
            initialPage,
            productsCount: initialProducts.length,
            totalCount,
            sort: selectedSort
        });
    }, [initialProducts, initialPage, totalCount, searchQuery, category, subcategory, selectedSort]);

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 300) {
                setShowBackToTop(true);
            } else {
                setShowBackToTop(false);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    // Handle loading more products
    const handleLoadMore = useCallback(async () => {
        if (isLoading || !hasMore) return;
        
        // Get the current scroll position and the position of the load more button
        const loadMoreButton = loadMoreButtonRef.current;
        const buttonPosition = loadMoreButton ? loadMoreButton.getBoundingClientRect().top + window.scrollY : null;
        const loadStartTime = Date.now();
        
        logger.info('Loading more products', {
            requestId: requestIdRef.current,
            currentPage,
            currentProductsCount: products.length,
            totalCount,
            isLoading,
            hasMore,
            scrollY: window.scrollY,
            buttonPosition
        });
        
        setIsLoading(true);
        
        try {
            const nextPage = currentPage + 1;
            const params = new URLSearchParams(searchParams);
            params.set('page', nextPage.toString());
            
            const apiStartTime = Date.now();
            const response = await fetch(`/api/search/more?${params.toString()}`);
            const apiDuration = Date.now() - apiStartTime;
            
            if (!response.ok) {
                const errorMessage = `API request failed with status ${response.status}`;
                logger.error('Failed to load more products', new Error(errorMessage), {
                    requestId: requestIdRef.current,
                    status: response.status,
                    statusText: response.statusText,
                    url: response.url,
                    apiDuration
                });
                setHasMore(false);
                return;
            }
            
            const { products: newProducts } = await response.json();
            const loadDuration = Date.now() - loadStartTime;
            
            if (newProducts && newProducts.length > 0) {
                logger.info('Products loaded successfully', {
                    requestId: requestIdRef.current,
                    newProductsCount: newProducts.length,
                    totalLoadedProducts: products.length + newProducts.length,
                    currentPage,
                    nextPage: currentPage + 1,
                    apiDuration,
                    totalLoadDuration: loadDuration,
                    hasMore: newProducts.length >= productsPerPage
                });
                
                // Update products state with new products
                setProducts(prev => {
                    // Create a Set of existing product IDs for quick lookup
                    const existingProductIds = new Set(prev.map(p => p.id));
                    
                    // Filter out any duplicates that might already be in the list
                    const uniqueNewProducts = newProducts.filter((p: TransformedProduct) => 
                        !existingProductIds.has(p.id)
                    );
                
                    return [...prev, ...uniqueNewProducts];
                });

                // Update the URL without causing a scroll
                const newParams = new URLSearchParams(searchParams);
                newParams.set('page', nextPage.toString());
                router.push(`/search?${newParams.toString()}`, { scroll: false });
                
                setCurrentPage(nextPage);
            } else {
                logger.info('No more products available', {
                    requestId: requestIdRef.current,
                    currentPage,
                    totalLoadedProducts: products.length,
                    totalCount,
                    loadDuration: Date.now() - loadStartTime
                });
                setHasMore(false);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorForLog = error instanceof Error ? error : new Error(errorMessage);
            logger.error('Error loading more products', errorForLog, {
                requestId: requestIdRef.current,
                currentPage,
                productsCount: products.length,
                loadDuration: Date.now() - loadStartTime,
                error: {
                    name: error instanceof Error ? error.name : 'UnknownError',
                    message: errorMessage,
                    stack: error instanceof Error ? error.stack : undefined
                }
            });
        } finally {
            setIsLoading(false);
            
            logger.debug('Load more operation completed', {
                requestId: requestIdRef.current,
                currentPage,
                productsCount: products.length,
                hasMore,
                isLoading: false,
                totalDuration: Date.now() - loadStartTime
            });
            
            // After the DOM updates, scroll to show the new content
            requestAnimationFrame(() => {
                if (buttonPosition) {
                    // Get the sticky header height (assuming it's around 80px including padding)
                    const headerHeight = 80; // Adjust this value to match your actual header height
                    
                    // Calculate the scroll position to account for the sticky header
                    // Add some extra padding (16px) to ensure the page divider is fully visible
                    const scrollToPosition = buttonPosition - headerHeight - 16;
                    
                    logger.debug('Scrolling to show new content', {
                        requestId: requestIdRef.current,
                        buttonPosition,
                        headerHeight,
                        scrollToPosition,
                        currentScrollY: window.scrollY,
                        windowHeight: window.innerHeight,
                        documentHeight: document.documentElement.scrollHeight
                    });
                    
                    // Scroll to the adjusted position
                    window.scrollTo({
                        top: Math.max(0, scrollToPosition), // Ensure we don't scroll above the page
                        behavior: 'smooth'
                    });
                }
            });
        }
    }, [isLoading, hasMore, currentPage, searchQuery, category, subcategory, router, products.length]);

    // Sort products client-side for immediate feedback
    const sortedProducts = useMemo(() => {
        const sortStartTime = performance.now();
        
        const result = [...products].sort((a, b) => {
            switch (selectedSort) {
                case 'price_asc':
                    const aMinPrice = Math.min(...(a.retailerOffers?.map(o => o.price) || [0]));
                    const bMinPrice = Math.min(...(b.retailerOffers?.map(o => o.price) || [0]));
                    return aMinPrice - bMinPrice;
                case 'price_desc':
                    const aMaxPrice = Math.max(...(a.retailerOffers?.map(o => o.price) || [0]));
                    const bMaxPrice = Math.max(...(b.retailerOffers?.map(o => o.price) || [0]));
                    return bMaxPrice - aMaxPrice;
                case 'cashback_desc':
                    return (b.cashbackAmount || 0) - (a.cashbackAmount || 0);
                case 'cashback_asc':
                    return (a.cashbackAmount || 0) - (b.cashbackAmount || 0);
                case 'newest':
                    return new Date(b.updatedAt || 0).getTime() - new Date(a.updatedAt || 0).getTime();
                case 'recommended':
                default:
                    // Recommended: prioritize featured products and higher cashback
                    const aScore = (a.isFeatured ? 100 : 0) + (a.cashbackAmount || 0);
                    const bScore = (b.isFeatured ? 100 : 0) + (b.cashbackAmount || 0);
                    return bScore - aScore;
            }
        });
        
        const sortDuration = performance.now() - sortStartTime;
        
        if (sortDuration > 50) { // Only log if sorting takes significant time
            logger.debug('Products sorted', {
                requestId: requestIdRef.current,
                sortOption: selectedSort,
                productCount: products.length,
                sortDuration: `${sortDuration.toFixed(2)}ms`,
                performanceImpact: sortDuration > 100 ? 'high' : sortDuration > 50 ? 'medium' : 'low'
            });
        }
        
        return result;
    }, [products, selectedSort]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        const params = new URLSearchParams(searchParams);

        if (localSearch.trim()) {
            params.set('q', localSearch.trim());
        } else {
            params.delete('q');
        }
        params.delete('page'); // Reset to first page on new search

        router.push(`/search?${params.toString()}`);
    };

    const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedSort(e.target.value as SortOption);
    };

    const clearFilters = () => {
        router.push('/search');
    };

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    return (
        <div className="container py-12">
            <SearchResultsStructuredData
                query={searchQuery}
                results={products}
                totalResults={totalCount}
            />
            {/* Header */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
            >
                <h1 className="text-4xl font-bold text-primary mb-4">
                    {searchQuery ? `Search Results for "${searchQuery}"` : 'Search Products'}
                </h1>
                {(category || subcategory) && (
                    <div className="flex items-center gap-2 text-sm text-foreground/70 mb-4">
                        <span>Filtered by:</span>
                        {category && (
                            <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                                {category}
                            </span>
                        )}
                        {subcategory && (
                            <span className="bg-secondary/10 text-secondary px-2 py-1 rounded">
                                {subcategory}
                            </span>
                        )}
                        <button
                            onClick={clearFilters}
                            className="text-primary hover:text-primary/80 underline"
                        >
                            Clear filters
                        </button>
                    </div>
                )}
            </motion.div>

            {/* Search Bar */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="mb-8"
            >
                <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
                    <div className="relative">
                        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-foreground/40" />
                        <input
                            type="text"
                            placeholder="Search for products, brands, or categories..."
                            value={localSearch}
                            onChange={(e) => setLocalSearch(e.target.value)}
                            className="w-full pl-12 pr-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-lg"
                        />
                        <button
                            type="submit"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                        >
                            Search
                        </button>
                    </div>
                </form>
            </motion.div>

            {/* Controls */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="flex justify-between items-center mb-8"
            >
                <div className="flex items-center gap-4">
                    <span className="text-foreground/70" data-testid="results-count">
                        {totalCount.toLocaleString()} result{totalCount !== 1 ? 's' : ''} found
                    </span>
                    <button
                        onClick={() => setShowFilters(!showFilters)}
                        className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-foreground/70 bg-secondary/10 rounded-lg hover:bg-secondary/20 transition-colors"
                    >
                        <SlidersHorizontal className="h-4 w-4" />
                        Filters
                    </button>
                </div>

                <div className="flex items-center gap-2">
                    <label htmlFor="sort" className="text-sm text-foreground/70">
                        Sort by:
                    </label>
                    <select
                        id="sort"
                        value={selectedSort}
                        onChange={handleSortChange}
                        className="px-4 py-2 text-sm font-medium text-foreground bg-white border border-border rounded-lg hover:bg-secondary/5 focus:outline-none focus:ring-2 focus:ring-primary/20"
                    >
                        {Object.entries(sortOptions).map(([value, label]) => (
                            <option key={value} value={value}>
                                {label}
                            </option>
                        ))}
                    </select>
                </div>
            </motion.div>

            {/* Results */}
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
            >
                {sortedProducts.length > 0 ? (
                    <>
                        <ProductGrid 
                            products={sortedProducts} 
                            currentPage={currentPage}
                            productsPerPage={productsPerPage}
                            showPageDividers={true}
                        />

                        {/* Load More Button */}
                        {hasMore && (
                            <div className="flex justify-center">
                                <button
                                    ref={loadMoreButtonRef}
                                    onClick={handleLoadMore}
                                    disabled={isLoading}
                                    className="bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors disabled:bg-gray-400 flex items-center justify-center min-w-[120px]"
                                >
                                    {isLoading ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Loading...
                                        </>
                                    ) : 'Load More'}
                                </button>
                            </div>
                        )}
                        <noscript>
                            <div className="flex justify-center mt-8">
                                {
                                    Array.from({ length: Math.ceil(totalCount / 20) }, (_, i) => i + 1).map(page => (
                                        <a key={page} href={`/search?q=${searchQuery}&page=${page}`} className="px-4 py-2 border rounded-md mr-2">
                                            {page}
                                        </a>
                                    ))
                                }
                            </div>
                        </noscript>
                    </>
                ) : (
                    <div className="text-center py-12">
                        <Search className="h-16 w-16 text-foreground/20 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-foreground/70 mb-2">
                            No products found
                        </h3>
                        <p className="text-foreground/60 mb-6">
                            Try adjusting your search terms or browse our categories.
                        </p>
                        <Link
                            href="/products"
                            className="inline-flex items-center gap-2 text-primary hover:text-primary/90 font-medium"
                        >
                            Browse All Products
                        </Link>
                    </div>
                )}
            </motion.div>
            {showBackToTop && (
                <button
                    onClick={scrollToTop}
                    className="fixed bottom-8 right-8 bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:bg-primary/90 transition-colors z-50"
                >
                    <ArrowUp className="h-6 w-6" />
                </button>
            )}
        </div>
    );
}