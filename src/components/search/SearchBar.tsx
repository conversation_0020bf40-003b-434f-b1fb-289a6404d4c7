'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation';
import { Search as SearchIcon } from 'lucide-react'
import { useDebounce } from '../../hooks/useDebounce'
import { SearchSuggestions } from './SearchSuggestions'

interface Suggestion {
  id: string
  name: string
}

interface SearchBarProps {
  onSearch?: (query: string) => void
  suggestions?: Suggestion[]
  isLoading?: boolean
  error?: string | null
  isOpen?: boolean
  onOpenChange?: (isOpen: boolean) => void
  inHeader?: boolean
}

export function SearchBar({ 
  onSearch, 
  suggestions, 
  isLoading = false, 
  error = null, 
  isOpen, 
  onOpenChange 
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [internalIsOpen, setIsOpen] = useState(false)
  const isOpenControlled = isOpen !== undefined ? isOpen : internalIsOpen
  
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    onOpenChange?.(open)
  }
  const searchRef = useRef<HTMLDivElement>(null)

  const debouncedQuery = useDebounce(query, 300)

  useEffect(() => {
    onSearch?.(debouncedQuery)
  }, [debouncedQuery, onSearch])

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const router = useRouter();
  const searchInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const searchTerm = query.trim();
    if (searchTerm) {
      // Navigate to search page with the query
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
      // Close suggestions and blur the input
      handleOpenChange(false);
      searchInputRef.current?.blur();
    }
  };

  const handleSuggestionSelect = (suggestion: string) => {
    const searchTerm = suggestion.trim();
    if (searchTerm) {
      setQuery(searchTerm);
      // Give UI a moment to update before navigation
      setTimeout(() => {
        router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
      }, 100);
      // Close suggestions
      handleOpenChange(false);
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(e.target as Node)) {
        handleOpenChange(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative w-full max-w-md" ref={searchRef}>
      <form 
        onSubmit={handleSubmit} 
        className="w-full"
        role="search"
        aria-label="Search products"
      >
        <div className="relative flex items-center">
          <input
            ref={searchInputRef}
            type="search"
            placeholder="Search products, brands, or categories..."
            className="w-full pl-4 pr-10 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              handleOpenChange(true);
            }}
            onFocus={() => {
              if (query.length > 0) {
                handleOpenChange(true);
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                handleOpenChange(false);
                searchInputRef.current?.blur();
              }
            }}
            aria-label="Search products"
            aria-expanded={isOpenControlled && query.length > 0}
            aria-controls="search-suggestions"
            aria-autocomplete="list"
            autoComplete="off"
            autoCorrect="off"
            autoCapitalize="off"
            spellCheck="false"
          />
          <button
            type="submit"
            className="absolute right-0 top-0 h-full px-3 text-primary hover:text-primary/80 transition-colors"
            aria-label="Submit search"
          >
            <SearchIcon className="h-5 w-5" />
          </button>
        </div>
      </form>

      {isOpenControlled && query.length > 0 && (
        <div 
          id="search-suggestions" 
          role="listbox" 
          aria-label="Search suggestions"
          className="z-50"
        >
          <SearchSuggestions 
            query={query} 
            onSelect={handleSuggestionSelect} 
          />
        </div>
      )}
    </div>
  )
}
