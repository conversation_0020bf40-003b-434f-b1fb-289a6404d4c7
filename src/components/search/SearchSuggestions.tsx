'use client';

import { useEffect, useState, useRef } from 'react';
import { useDebounce } from '@/hooks/useDebounce';

interface Suggestion {
  name: string;
  type: 'category' | 'brand' | 'product';
}

interface SearchSuggestionsProps {
  query: string;
  onSelect?: (suggestion: string) => void;
}

export function SearchSuggestions({ query, onSelect }: SearchSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const suggestionsRef = useRef<HTMLUListElement>(null);
  const debouncedQuery = useDebounce(query, 300);

  // Fetch suggestions
  useEffect(() => {
    if (debouncedQuery.length > 2) {
      setIsLoading(true);
      fetch(`/api/search/suggestions?q=${encodeURIComponent(debouncedQuery)}`)
        .then(res => res.json())
        .then(data => {
          const formattedSuggestions = [
            ...data.categories.map((c: any) => ({ ...c, type: 'category' as const })),
            ...data.brands.map((b: any) => ({ ...b, type: 'brand' as const })),
            ...data.products.map((p: any) => ({ ...p, type: 'product' as const }))
          ];
          setSuggestions(formattedSuggestions.slice(0, 6));
          setSelectedIndex(-1); // Reset selection when suggestions change
        })
        .catch(error => {
          console.error('Error fetching suggestions:', error);
          setSuggestions([]);
        })
        .finally(() => setIsLoading(false));
    } else {
      setSuggestions([]);
      setSelectedIndex(-1);
    }
  }, [debouncedQuery]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!suggestions.length) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        case 'Enter':
          if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
            e.preventDefault();
            const selectedSuggestion = suggestions[selectedIndex];
            onSelect?.(selectedSuggestion.name);
          }
          break;
        case 'Escape':
          onSelect?.(query);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [suggestions, selectedIndex, onSelect, query]);

  // Scroll selected suggestion into view
  useEffect(() => {
    if (selectedIndex >= 0 && suggestionsRef.current) {
      const selectedItem = suggestionsRef.current.children[selectedIndex] as HTMLElement;
      if (selectedItem) {
        selectedItem.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex]);

  if (!query || query.length < 3) return null;

  const handleSuggestionClick = (e: React.MouseEvent, suggestion: string) => {
    e.preventDefault();
    onSelect?.(suggestion);
  };

  return (
    <div className="absolute top-full mt-1 w-full bg-white shadow-lg rounded-lg overflow-hidden z-50">
      {isLoading ? (
        <div className="p-4 text-gray-500 text-sm">Loading suggestions...</div>
      ) : suggestions.length > 0 ? (
        <ul ref={suggestionsRef}>
          {suggestions.map((suggestion, index) => (
            <li 
              key={`${suggestion.type}-${suggestion.name}-${index}`}
              className={`px-4 py-2 cursor-pointer transition-colors ${
                index === selectedIndex 
                  ? 'bg-primary/10 text-primary' 
                  : 'hover:bg-gray-100 text-gray-700'
              }`}
              onClick={(e) => handleSuggestionClick(e, suggestion.name)}
              onMouseEnter={() => setSelectedIndex(index)}
              onMouseDown={(e) => e.preventDefault()} // Prevent input blur on click
              role="option"
              aria-selected={index === selectedIndex}
            >
              <div className="flex items-center">
                <span className="capitalize text-xs text-gray-500 w-20 flex-shrink-0">
                  {suggestion.type}:
                </span>
                <span className="truncate">{suggestion.name}</span>
              </div>
            </li>
          ))}
        </ul>
      ) : debouncedQuery.length > 2 ? (
        <div className="p-4 text-gray-500 text-sm">No suggestions found for "{debouncedQuery}"</div>
      ) : null}
    </div>
  );
}
