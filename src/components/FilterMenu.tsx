'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { X } from 'lucide-react'

interface Brand {
    id: string;
    name: string;
}

interface Promotion {
    id: string;
    title: string;
    brand_id: string;
    brand_name: string;
}

interface FilterMenuProps {
    // Price range
    priceRange: [number, number];
    onPriceRangeChange: (range: [number, number]) => void;
    onApplyPriceRange: () => void;
    
    // Brand selection
    selectedBrands: string[];
    onBrandToggle: (brandId: string) => void;
    
    // Promotion selection
    selectedPromotions: string[];
    onPromotionToggle: (promotionId: string) => void;
    
    // Clear all filters
    onClearFilters: () => void;
    
    // Data
    brands: Brand[];
    promotions: Promotion[];
    
    // Optional close handler for mobile
    onCloseAction?: () => void;
}

export function FilterMenu({
    priceRange,
    onPriceRangeChange,
    onApplyPriceRange,
    selectedBrands,
    onBrandToggle,
    selectedPromotions = [],
    onPromotionToggle,
    onClearFilters,
    brands,
    promotions,
    onCloseAction
}: FilterMenuProps) {
    const [localRange, setLocalRange] = React.useState<[number, number]>(priceRange);
    const [isPriceValid, setIsPriceValid] = React.useState(true);
    
    // Update local state when prop changes
    React.useEffect(() => {
        setLocalRange(priceRange);
    }, [priceRange]);
    
    const handlePriceChange = (index: number, value: string) => {
        const numValue = value === '' ? (index === 0 ? 0 : 99999) : parseInt(value) || 0;
        const newRange = [...localRange] as [number, number];
        newRange[index] = numValue;
        setLocalRange(newRange);
        
        // Validate min is not greater than max
        if (index === 0 && numValue > newRange[1]) {
            setIsPriceValid(false);
        } else if (index === 1 && numValue < newRange[0]) {
            setIsPriceValid(false);
        } else {
            setIsPriceValid(true);
        }
    };
    
    const handleApplyClick = () => {
        if (!isPriceValid) return;
        onPriceRangeChange(localRange);
        onApplyPriceRange();
    };
    
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleApplyClick();
        }
    }
    
    const hasActiveFilters = 
        selectedBrands.length > 0 || 
        selectedPromotions.length > 0 ||
        priceRange[0] !== 0 || 
        priceRange[1] !== 99999
        
    const handleBrandToggle = (brandId: string) => {
        onBrandToggle(brandId)
    }
    
    const handlePromotionToggle = (promotionId: string) => {
        onPromotionToggle(promotionId)
    }
    
    const handleClearAllFilters = () => {
        onClearFilters();
        const defaultRange: [number, number] = [0, 99999];
        setLocalRange(defaultRange);
        setIsPriceValid(true);
    }

    return (
        <motion.div
            initial={false}
            animate={{ x: 0, opacity: 1 }}
            className="h-full w-full bg-white border rounded-lg md:border-gray-200"
        >
            <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-medium">Filters</h2>
                <div className="flex items-center space-x-4">
                    {hasActiveFilters && (
                        <button
                            onClick={onClearFilters}
                            className="text-sm text-blue-600 hover:text-blue-800"
                        >
                            Clear all
                        </button>
                    )}
                    {onCloseAction && (
                        <button
                            onClick={onCloseAction}
                            className="text-gray-500 hover:text-gray-700 md:hidden"
                            aria-label="Close filters"
                        >
                            <X className="w-5 h-5" />
                        </button>
                    )}
                </div>
            </div>
            <div className="p-4 space-y-6">
                {/* Price Range Filter */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900">Price Range</h3>
                        {!isPriceValid && (
                            <span className="text-sm text-red-600">Min must be less than max</span>
                        )}
                    </div>
                    <div className="space-y-3">
                        <div className="flex items-center space-x-4">
                            <div className="flex-1">
                                <label htmlFor="min-price" className="block text-sm font-medium text-gray-700 mb-1">
                                    Min
                                </label>
                                <div className="relative rounded-md shadow-sm">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span className="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input
                                        type="number"
                                        name="min-price"
                                        id="min-price"
                                        className={`focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-3 sm:text-sm border ${
                                            isPriceValid ? 'border-gray-300' : 'border-red-300'
                                        } rounded-md`}
                                        placeholder="0"
                                        value={localRange[0] === 0 ? '' : localRange[0]}
                                        onChange={(e) => handlePriceChange(0, e.target.value)}
                                        onKeyDown={handleKeyDown}
                                        min="0"
                                        step="1"
                                    />
                                </div>
                            </div>
                            <div className="flex-1">
                                <label htmlFor="max-price" className="block text-sm font-medium text-gray-700 mb-1">
                                    Max
                                </label>
                                <div className="relative rounded-md shadow-sm">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span className="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input
                                        type="number"
                                        name="max-price"
                                        id="max-price"
                                        className={`focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-3 sm:text-sm border ${
                                            isPriceValid ? 'border-gray-300' : 'border-red-300'
                                        } rounded-md`}
                                        placeholder="1000+"
                                        value={localRange[1] === 99999 ? '' : localRange[1]}
                                        onChange={(e) => handlePriceChange(1, e.target.value)}
                                        onKeyDown={handleKeyDown}
                                        min={localRange[0]}
                                        step="1"
                                    />
                                </div>
                            </div>
                        </div>
                        <button
                            onClick={handleApplyClick}
                            disabled={!isPriceValid}
                            className={`w-full py-2 px-4 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                                isPriceValid 
                                    ? 'bg-primary-600 text-white hover:bg-primary-700' 
                                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            }`}
                        >
                            Apply Price Range
                        </button>
                    </div>
                </div>

                {/* Brands Filter */}
                <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Brands</h3>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                        {brands.map((brand) => (
                            <label key={brand.id} className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    checked={selectedBrands.includes(brand.id)}
                                    onChange={() => handleBrandToggle(brand.id)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    disabled={selectedPromotions.length > 0}
                                />
                                <span className="text-sm text-gray-700">{brand.name}</span>
                            </label>
                        ))}
                        {brands.length === 0 && (
                            <p className="text-sm text-gray-500">No brands available</p>
                        )}
                    </div>
                </div>

                {/* Promotions Filter */}
                {promotions.length > 0 && (
                    <div>
                        <h3 className="text-sm font-medium text-gray-700 mb-2">Promotions</h3>
                        <div className="space-y-2 max-h-60 overflow-y-auto">
                            {promotions.map((promotion) => (
                                <label key={promotion.id} className={`flex items-center space-x-2 ${selectedBrands.length > 0 ? 'opacity-50' : ''}`}>
                                    <input
                                        type="checkbox"
                                        checked={selectedPromotions.includes(promotion.id)}
                                        onChange={() => handlePromotionToggle(promotion.id)}
                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        disabled={selectedBrands.length > 0}
                                    />
                                    <span className="text-sm text-gray-700">{promotion.title}</span>
                                </label>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </motion.div>
    )
}
