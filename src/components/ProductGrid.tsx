import React from 'react';
import { motion } from 'framer-motion';
import { ProductCard } from './ProductCard';
import { TransformedProduct } from '@/lib/data/types';
import { PageMarker } from './ui/PageMarker';

interface ProductGridProps {
  products: TransformedProduct[];
  currentPage: number;
  productsPerPage?: number;
  showPageDividers?: boolean;
}

export function ProductGrid({ 
  products, 
  currentPage, 
  productsPerPage = 20,
  showPageDividers = true 
}: ProductGridProps) {
  console.log('ProductGrid: products.length:', products.length, 'currentPage:', currentPage);
  
  // Group products by their actual page from the API
  const productsByPage: {[key: number]: TransformedProduct[]} = {};
  
  // Calculate total pages based on current page and products per page
  const totalPages = Math.ceil(products.length / productsPerPage);
  
  // Group products by their actual page
  for (let i = 0; i < totalPages; i++) {
    const startIdx = i * productsPerPage;
    const endIdx = startIdx + productsPerPage;
    productsByPage[i + 1] = products.slice(startIdx, endIdx);
  }

  return (
    <div className="space-y-8">
      {Object.entries(productsByPage).map(([page, pageProducts]) => {
        const pageNum = Number(page);
        return (
          <div key={`page-${pageNum}`} className="space-y-6">
            {/* Combined page marker and scroll-to-top button */}
            {showPageDividers && pageNum > 1 && (
              <PageMarker pageNumber={pageNum} />
            )}
          
            {/* Products grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {pageProducts.map((product, index) => {
                // Calculate the actual index in the full products array
                const globalIndex = ((pageNum - 1) * productsPerPage) + index;
                return (
                  <motion.div
                    key={product.id}
                    data-product-index={globalIndex}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    whileHover={{ y: -5 }}
                  >
                    <ProductCard 
                      product={product} 
                      currentPage={currentPage} 
                    />
                  </motion.div>
                );
              })}
            </div>
          </div>
        );
      })}
      
      {/* Final page marker without page number at the end */}
      {products.length > 0 && <PageMarker className="mt-8" />}
    </div>
  );
}