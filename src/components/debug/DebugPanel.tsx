import { useState } from 'react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { isDebugEnabled } from '@/config/debug.config';
import type { DebugData } from '@/config/debug.config';

interface DebugPanelProps {
	data: DebugData;
	title?: string;
}

export const DebugPanel = ({ data, title = 'Debug Information' }: DebugPanelProps) => {
	const [isOpen, setIsOpen] = useState(false);

	if (!isDebugEnabled()) {
		return null;
	}

	console.log('DebugPanel:', { isDebugEnabled: isDebugEnabled(), data });

	return (
		<Collapsible
			open={isOpen}
			onOpenChange={setIsOpen}
			className="w-full bg-gray-100 p-2 rounded-md mt-4"
		>
			<CollapsibleTrigger className="w-full text-left font-medium">
				{title}
			</CollapsibleTrigger>
			<CollapsibleContent className="space-y-2 mt-2">
				<div>
					<h4 className="font-medium">Timing:</h4>
					<p>Duration: {data.timing.duration}ms</p>
				</div>

				{data.queries && (
					<div>
						<h4 className="font-medium">Queries:</h4>
						<pre className="text-sm bg-gray-200 p-2 rounded">
							{data.queries.join('\n')}
						</pre>
					</div>
				)}
				{data.params && (
					<div>
						<h4 className="font-medium">Parameters:</h4>
						<pre className="text-sm bg-gray-200 p-2 rounded">
							{JSON.stringify(data.params, null, 2)}
						</pre>
					</div>
				)}
			</CollapsibleContent>
		</Collapsible>
	);
};