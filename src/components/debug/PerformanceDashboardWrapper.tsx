/**
 * Performance Dashboard Wrapper Component
 * Client-side wrapper for the ImagePerformanceDashboard to be used in server components
 */

'use client';

import React from 'react';
import { ImagePerformanceDashboard, useImagePerformanceDashboard } from './ImagePerformanceDashboard';

export function PerformanceDashboardWrapper() {
  const { isVisible, toggle } = useImagePerformanceDashboard();

  return (
    <ImagePerformanceDashboard
      isVisible={isVisible}
      onToggle={toggle}
    />
  );
}
