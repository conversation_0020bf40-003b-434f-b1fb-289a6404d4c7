'use client'
import { motion } from 'framer-motion'
import Link from 'next/link'

export function Footer() {
	return (
		<motion.footer 
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			className="border-t bg-primary/5"
		>
			<div className="container py-6 md:py-12">
				<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 px-4 md:px-0">
					<motion.div 
						whileHover={{ scale: 1.02 }}
						className="space-y-3"
					>
						<h4 className="text-base font-semibold text-primary">
								<Link href="/about" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
								About Us	
								</Link>
						</h4>
						{/*
						<ul className="space-y-3 text-sm">
							<!--<li>
								<a href="#" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
									How it Works
								</a>
							</li>
						
							<li>
				
								
							</li>
							<li>
								<Link href="/contact" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
 						 		Contact Us
								</Link>
							</li>
						</ul>
						*/}
					</motion.div>

					<motion.div 
						whileHover={{ scale: 1.02 }}
						className="space-y-3"
					>
						<h4 className="text-base font-semibold text-primary">
						<Link href="/contact" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
 						 		Contact Us
								</Link>
						</h4>
						{/*<ul className="space-y-3 text-sm">
							<li>
								<a href="#" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
									Privacy Policy
								</a>
							</li>
							<li>
								<a href="#" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
									Terms of Service
								</a>
							</li>
						</ul>*/}
					</motion.div>

					<motion.div 
						whileHover={{ scale: 1.02 }}
						className="space-y-3"
					>
						<h4 className="text-base font-semibold text-primary">
						<Link href="/terms" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
 						 		Terms of Service
						</Link>
						</h4>
						{/*<ul className="space-y-3 text-sm">
							<li>
								<a href="#" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
									FAQ
								</a>
							</li>
							<li>
								<a href="#" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
									Help Center
								</a>
							</li>
						</ul>
						*/}
					</motion.div>

					<motion.div 
						whileHover={{ scale: 1.02 }}
						className="space-y-3"
					>
						<h4 className="text-base font-semibold text-primary">
						<Link href="/privacy" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
 						 		Privacy Policy
						</Link>
						</h4>
						{/*<ul className="space-y-3 text-sm">
							<li>
								<a href="#" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
									Twitter
								</a>
							</li>
							<li>
								<a href="#" className="block py-1 text-foreground/70 hover:text-primary transition-colors">
									LinkedIn
								</a>
							</li>
						</ul>
						*/}
					</motion.div>
				</div>
				<div className="mt-8 pt-6 border-t text-center text-sm text-foreground/70 px-4 md:px-0">
					<p>© 2024 RebateRay. All rights reserved.</p>
				</div>
			</div>
		</motion.footer>
	)
}
