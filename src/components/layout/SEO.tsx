// SEO.tsx
import Head from 'next/head'
import React from 'react'

export interface SEOProps {
  title?: string;
  description?: string;
  canonical?: string;
  ogImage?: string;
  noindex?: boolean;
}

const SEO: React.FC<SEOProps> = ({
  title,
  description,
  canonical,
  ogImage,
  noindex = false
}) => {
  // Construct full title with your site name
  const fullTitle = title ? `${title} | RebateRay` : 'RebateRay - Find the Best Cashback Deals';
  
  // Default description if none provided
  const metaDescription = description || 'Discover and compare cashback deals and rebates from top brands in the UK.';

  return (
    <Head>
      <title>{fullTitle}</title>
      <meta name="description" content={metaDescription} />
      
      {noindex && <meta name="robots" content="noindex,nofollow" />}
      
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      {canonical && <meta property="og:url" content={canonical} />}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={metaDescription} />
      {ogImage && <meta property="og:image" content={ogImage} />}
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      {canonical && <meta property="twitter:url" content={canonical} />}
      <meta property="twitter:title" content={fullTitle} />
      <meta property="twitter:description" content={metaDescription} />
      {ogImage && <meta property="twitter:image" content={ogImage} />}
    </Head>
  );
};

export default SEO;