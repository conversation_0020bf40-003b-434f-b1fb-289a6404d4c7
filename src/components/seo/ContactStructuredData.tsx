import React from 'react';

interface ContactStructuredDataProps {
  organizationName: string;
  contactUrl: string;
}

const ContactStructuredData: React.FC<ContactStructuredDataProps> = ({ organizationName, contactUrl }) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Contact Us',
    description: `Get in touch with ${organizationName} for any inquiries or support.`,
    url: contactUrl,
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

export default ContactStructuredData;
