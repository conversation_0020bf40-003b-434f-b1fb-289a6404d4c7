// src/components/seo/StructuredData.tsx
// This component generates JSON-LD structured data for products and product lists
// to improve SEO and enable rich snippets in search results

'use client';

import React from 'react';
import { Product } from '@/types/product';
import { TransformedProduct, TransformedRetailerOffer } from '@/lib/data/types';

interface ProductStructuredDataProps {
  product: TransformedProduct;
  retailerOffers?: TransformedRetailerOffer[];
  fallbackPurchaseEndDate?: string;
}

export const ProductStructuredData: React.FC<ProductStructuredDataProps> = ({
  product,
  retailerOffers,
  fallbackPurchaseEndDate
}) => {
  // Function to get full image URL
  const getFullImageUrl = (image: string) => {
    if (!image) return null;

    // If it's already a full URL, return it as is
    if (image.startsWith('http')) {
      return image;
    }

    // Construct the Supabase storage URL if needed
    return `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;
  };

  // Get all image URLs
  const imageUrls = product.images && product.images.length > 0
    ? product.images.map(img => getFullImageUrl(img)).filter(Boolean)
    : [];

  // Add brand logo if available and no product images
  if (imageUrls.length === 0 && product.brand?.logoUrl) {
    imageUrls.push(product.brand.logoUrl);
  }

  // Use retailerOffers prop or fallback to product.retailerOffers
  const offers = retailerOffers || product.retailerOffers || [];

  // Calculate price range for AggregateOffer
  const prices = offers.map(offer => offer.price).filter(price => price > 0);
  const minPrice = prices.length > 0 ? Math.min(...prices) : null;
  const maxPrice = prices.length > 0 ? Math.max(...prices) : null;

  // Enhanced structured data according to Schema.org's Product schema with rich snippets support
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description || `${product.name} with cashback offer`,
    image: imageUrls.length > 0 ? imageUrls : undefined,
    sku: product.modelNumber || product.id,
    mpn: product.modelNumber,

    // Enhanced Brand schema
    brand: product.brand ? {
      '@type': 'Brand',
      name: product.brand.name,
      logo: product.brand.logoUrl,
      description: product.brand.description
    } : undefined,

    // Category information
    category: product.category?.name,

    // Individual offers from retailers
    offers: offers.length > 0 ? offers.map(offer => ({
      '@type': 'Offer',
      url: offer.url,
      price: offer.price.toString(),
      priceCurrency: 'GBP',
      priceValidUntil: product.promotion?.purchaseEndDate || (fallbackPurchaseEndDate ? fallbackPurchaseEndDate.split('T')[0] : undefined),
      availability: offer.stockStatus === 'in_stock'
        ? 'https://schema.org/InStock'
        : offer.stockStatus === 'out_of_stock'
        ? 'https://schema.org/OutOfStock'
        : 'https://schema.org/LimitedAvailability',
      seller: {
        '@type': 'Organization',
        name: offer.retailer?.name || 'Unknown Retailer',
        url: offer.retailer?.websiteUrl
      },
      itemCondition: 'https://schema.org/NewCondition'
    })) : undefined,

    // AggregateOffer for multiple retailer prices (improves rich snippets)
    ...(offers.length > 1 && minPrice && maxPrice ? {
      aggregateOffer: {
        '@type': 'AggregateOffer',
        lowPrice: minPrice.toString(),
        highPrice: maxPrice.toString(),
        priceCurrency: 'GBP',
        offerCount: offers.length.toString(),
        availability: 'https://schema.org/InStock'
      }
    } : {}),

    // Additional product information for rich snippets
    ...(product.isFeatured ? {
      additionalProperty: {
        '@type': 'PropertyValue',
        name: 'Featured Product',
        value: 'true'
      }
    } : {}),

    // Cashback promotion as additional offer
    ...(product.cashbackAmount && product.cashbackAmount > 0 ? {
      potentialAction: {
        '@type': 'BuyAction',
        target: offers.length > 0 ? offers[0].url : undefined,
        priceSpecification: {
          '@type': 'PriceSpecification',
          price: product.cashbackAmount.toString(),
          priceCurrency: 'GBP',
          name: 'Cashback Amount'
        }
      }
    } : {})
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

// Organization structured data for brands and retailers
interface OrganizationStructuredDataProps {
  organization: {
    id: string;
    name: string;
    logoUrl?: string | null;
    description?: string | null;
    websiteUrl?: string | null;
  };
  organizationType?: 'Brand' | 'Organization';
}

export const OrganizationStructuredData: React.FC<OrganizationStructuredDataProps> = ({
  organization,
  organizationType = 'Organization'
}) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': organizationType,
    name: organization.name,
    description: organization.description,
    logo: organization.logoUrl,
    url: organization.websiteUrl,
    identifier: organization.id
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

// This component is for generating structured data for a list of products (e.g., on category pages)
interface ProductListStructuredDataProps {
  products: TransformedProduct[];
  listName?: string;
  listDescription?: string;
}

export const ProductListStructuredData: React.FC<ProductListStructuredDataProps> = ({
  products,
  listName = "Products with Cashback Offers",
  listDescription = "Browse our selection of products with cashback offers from top brands"
}) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    name: listName,
    description: listDescription,
    numberOfItems: products.length,
    itemListElement: products.map((product, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@type': 'Product',
        name: product.name,
        description: product.description || `${product.name} with cashback offer`,
        url: `/products/${product.slug || product.id}`,
        image: product.images && product.images.length > 0
          ? product.images[0]?.startsWith('http')
            ? product.images[0]
            : `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${product.images[0]}`
          : (product.brand?.logoUrl || undefined),
        brand: product.brand ? {
          '@type': 'Brand',
          name: product.brand.name,
          logo: product.brand.logoUrl
        } : undefined,
        sku: product.modelNumber || product.id,
        offers: product.retailerOffers && product.retailerOffers.length > 0 ? {
          '@type': 'AggregateOffer',
          lowPrice: Math.min(...product.retailerOffers.map(o => o.price)).toString(),
          highPrice: Math.max(...product.retailerOffers.map(o => o.price)).toString(),
          priceCurrency: 'GBP',
          offerCount: product.retailerOffers.length.toString()
        } : undefined
      },
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

// WebSite structured data for homepage
export function WebSiteStructuredData() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'RebateRay',
    alternateName: 'CashbackDeals',
    description: 'Discover and compare cashback deals and rebates from top brands in the UK.',
    url: 'https://4-2.d3q274urye85k3.amplifyapp.com/',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://4-2.d3q274urye85k3.amplifyapp.com/search?q={search_term_string}'
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'RebateRay',
      url: 'https://4-2.d3q274urye85k3.amplifyapp.com/'
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// SearchResultsPage structured data for search pages
interface SearchResultsStructuredDataProps {
  query: string;
  results: TransformedProduct[];
  totalResults: number;
}

export const SearchResultsStructuredData: React.FC<SearchResultsStructuredDataProps> = ({
  query,
  results,
  totalResults
}) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'SearchResultsPage',
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: totalResults,
      itemListElement: results.map((product, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Product',
          name: product.name,
          description: product.description,
          image: product.images && product.images.length > 0 ? product.images[0] : undefined,
          url: `/products/${product.slug || product.id}`,
          brand: product.brand ? {
            '@type': 'Brand',
            name: product.brand.name
          } : undefined
        }
      }))
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `/search?q=${encodeURIComponent(query)}`,
      'query-input': 'required name=q'
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

// BreadcrumbList structured data for navigation paths
interface BreadcrumbStructuredDataProps {
  items: {
    name: string;
    url: string;
  }[];
}

export const BreadcrumbStructuredData: React.FC<BreadcrumbStructuredDataProps> = ({ items }) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

// Utility function to validate structured data (for development/testing)
export const validateStructuredData = (data: any): boolean => {
  try {
    // Basic validation checks
    if (!data['@context'] || !data['@type']) {
      console.warn('Structured data missing @context or @type');
      return false;
    }

    if (data['@context'] !== 'https://schema.org') {
      console.warn('Structured data should use https://schema.org context');
      return false;
    }

    // Product-specific validation
    if (data['@type'] === 'Product') {
      const requiredFields = ['name'];
      const recommendedFields = ['description', 'image', 'brand', 'offers'];

      for (const field of requiredFields) {
        if (!data[field]) {
          console.warn(`Product structured data missing required field: ${field}`);
          return false;
        }
      }

      for (const field of recommendedFields) {
        if (!data[field]) {
          console.warn(`Product structured data missing recommended field: ${field}`);
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error validating structured data:', error);
    return false;
  }
};

// Helper function to generate rich snippet optimized structured data
export const generateRichSnippetData = (product: TransformedProduct) => {
  const offers = product.retailerOffers || [];
  const hasOffers = offers.length > 0;
  const prices = offers.map(o => o.price).filter(p => p > 0);

  return {
    hasRichSnippetPotential: hasOffers && prices.length > 0,
    missingFields: [
      !product.name && 'name',
      !product.description && 'description',
      (!product.images || product.images.length === 0) && 'image',
      !product.brand && 'brand',
      !hasOffers && 'offers'
    ].filter(Boolean),
    richSnippetScore: Math.round(
      (
        (product.name ? 20 : 0) +
        (product.description ? 20 : 0) +
        (product.images && product.images.length > 0 ? 20 : 0) +
        (product.brand ? 20 : 0) +
        (hasOffers ? 20 : 0)
      )
    )
  };
};

// CollectionPage structured data is now generated in the server component