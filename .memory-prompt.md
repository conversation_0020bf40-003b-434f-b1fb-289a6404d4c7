-Stick to the current scope of the prompt from the user, do not expand beyond what the user's prompt has asked you to do. DO NOT INTRODUCE SCOPE CREEP WIHTOUT PERMISSION.

DO not go looking for any other impacted pages without my confirmation.


Do not update the changelog if i do not tell you to. When you are instructed to upadate changelog.txt always include the following: technical context,implementation details, impact description, files changed.

Any time we have some erorrs that need to be fixed, once the route cause of the error has been identified and i have accepted the changes, and i give you the go ahead to update @.memory-prompt.md file with further notes in markdown format, so these issues are checked on every prompt query.

These patterns should be incorporated into a project-wide TypeScript configuration and ESLint rules, etc. to enforce consistency and prevent recurring issues.

Error Handling Patterns:
- Consistent error response structure across API routes
- Proper error boundary implementation in React components
- Type-safe error handling (instanceof Error checks)
- Proper null/undefined checks before accessing properties

Error Logging Patterns:
- Environment-aware error logging:
	```typescript
	const logError = (error: unknown, context?: Record<string, unknown>) => {
		if (process.env.NODE_ENV === 'development') {
			console.error('Error:', { error, context })
		} else {
			console.error('Operation failed:', {
				type: error instanceof Error ? error.name : 'UnknownError',
				context
			})
		}
	}
	```
- Context-rich error logging:
	- Include relevant state and parameters
	- Add operation type or component name
	- Include user context when relevant
	- Sanitize sensitive information
- Error type handling:
	- Use instanceof checks for Error types
	- Handle unknown error types safely
	- Provide fallback error messages
	- Maintain consistent error structure
- Development vs Production logging:
	- Full error details in development
	- Sanitized errors in production
	- Remove sensitive data automatically
	- Use structured logging format

Environment Variables:
- Check for required environment variables before using them:
	```typescript
	if (!process.env.REQUIRED_VAR) {
		throw new Error('Missing REQUIRED_VAR')
	}
	```
- Proper typing for environment variables:
	```typescript
	declare global {
		namespace NodeJS {
			interface ProcessEnv {
				NEXT_PUBLIC_SUPABASE_URL: string
				NEXT_PUBLIC_SUPABASE_ANON_KEY: string
			}
		}
	}
	```
- Consistent error messages for missing configurations:
	- Use descriptive error messages: "Missing VARIABLE_NAME"
	- Throw errors early in initialization
	- Document required variables in .env.example
- Environment variable validation:
	- Validate format and content where applicable
	- Use zod or similar for runtime validation
	- Handle different environments (development/production)

Database Query Issues:
- Proper error handling for Supabase queries:
	```typescript
	const { data, error } = await supabase.from('table').select()
	if (error) {
		console.error('Query error:', error)
		return { data: null, error: 'Operation failed' }
	}
	```
- Type safety for database responses:
	- Define interfaces for database tables
	- Use type assertions carefully with validation
	- Handle nullable fields explicitly
- Consistent error logging and debugging information:
	- Log full error details in development
	- Sanitize error messages in production
	- Include request context in logs
- Proper null checks for database results:
	- Use optional chaining for nested objects
	- Provide fallback values
	- Validate data shape before processing

Supabase Query Patterns:
- Table relationships:
  ```typescript
  // Use foreign key references in select
  .select(`
    id,
    field,
    related_table!foreign_key (
      field
    )
  `)
  ```
- Full-text search:
  ```typescript
  .textSearch('search_vector', query, {
    type: 'websearch',
    config: 'english'
  })
  ```
- Type safety:
  ```typescript
  interface DatabaseType {
    id: string
    field: string
    related_table: Array<{
      field: string
    }>
  }
  
  const { data, error } = await supabase
    .from('table')
    .select()
  
  const result = (data as unknown as DatabaseType[])
    .map(item => ({
      id: item.id,
      field: item.field,
      related: item.related_table[0]?.field || ''
    }))
  ```
- Error handling:
  - Check for Supabase error object
  - Use type guards for error responses
  - Provide fallback values for nullable fields
  - Handle missing relationships with optional chaining

Search Implementation Patterns:
- Database Indexing:
	```sql
	-- Add appropriate indexes for search columns
	create index idx_name_search on table_name using gin (to_tsvector('english', column_name));
	create index idx_name_ilike on table_name (column_name text_pattern_ops);
	```
- Supabase Search Queries:
	```typescript
	// Combined search across multiple tables
	const query = supabase
		.from('main_table')
		.select(`
			id,
			name,
			related_table!foreign_key (
				id,
				name
			)
		`)
		.or('name.ilike.%${searchTerm}%,related_table.name.ilike.%${searchTerm}%')
	```
- Search Debug Patterns:
	- Log search parameters and results count
	- Include timestamp with search logs
	- Track query performance metrics
	- Monitor search result quality
- Error Handling for Search:
	- Handle empty search results gracefully
	- Provide meaningful feedback for no matches
	- Include search context in error logs
	- Implement proper fallback behavior
- Search Query Optimization:
	- Use appropriate indexes for search columns
	- Implement debouncing for search inputs
	- Limit result set size for performance
	- Cache frequently searched results

TypeScript Type Safety:
Ensure proper type definitions for API responses
Handle nullable values explicitly
Define proper interface/type for all components props
Use proper type guards for error handling
Error Handling Patterns:
Consistent error response structure across API routes
Proper error boundary implementation in React components
Type-safe error handling (instanceof Error checks)
Proper null/undefined checks before accessing properties

Error Logging Patterns:
- Environment-aware error logging:
	```typescript
	const logError = (error: unknown, context?: Record<string, unknown>) => {
		if (process.env.NODE_ENV === 'development') {
			console.error('Error:', { error, context })
		} else {
			console.error('Operation failed:', {
				type: error instanceof Error ? error.name : 'UnknownError',
				context
			})
		}
	}
	```
- Context-rich error logging:
	- Include relevant state and parameters
	- Add operation type or component name
	- Include user context when relevant
	- Sanitize sensitive information
- Error type handling:
	- Use instanceof checks for Error types
	- Handle unknown error types safely
	- Provide fallback error messages
	- Maintain consistent error structure
- Development vs Production logging:
	- Full error details in development
	- Sanitized errors in production
	- Remove sensitive data automatically
	- Use structured logging format

API Response Handling:
Consistent response structure {data, error, status}
Proper loading states management
Error state handling in useQuery hooks
Type safety for API response data

Environment Variables:
Check for required environment variables before using them
Proper typing for environment variables
Consistent error messages for missing configurations

Database Query Issues:
Proper error handling for Supabase queries
Type safety for database responses
Consistent error logging and debugging information
Proper null checks for database results

Component Props:
Required vs optional props clearly defined
Proper default values for optional props
Type-safe prop drilling
Proper children prop typing

Async Operations:
Proper loading state management
Error boundary implementation
Consistent try-catch patterns
Type-safe async/await usage

Debug Information:
Structured debug logs
Consistent error tracking
Step-by-step operation logging
Environment-aware error details

Search Implementation Best Practices:

Database Indexing:
- Create appropriate indexes for search columns:
	```sql
	-- Full-text search index
	create index idx_name_search on table_name using gin (to_tsvector('english', column_name));
	-- Case-insensitive search index
	create index idx_name_ilike on table_name (column_name text_pattern_ops);
	```

Supabase Query Patterns:
- Combined table search:
	```typescript
	const query = supabase
		.from('main_table')
		.select(`
			id,
			name,
			related_table!foreign_key (id, name)
		`)
		.or('name.ilike.%${searchTerm}%,related_table.name.ilike.%${searchTerm}%')
	```

Search Error Handling:
- Log search parameters and results
- Include timestamp with search logs
- Track query performance
- Handle empty results gracefully
- Provide meaningful feedback
- Include search context in error logs

Query Optimization:
- Use appropriate database indexes
- Implement input debouncing
- Limit result set size
- Cache frequent searches
- Monitor query performance

Search Result Processing:
- Validate search parameters
- Sanitize search input
- Handle special characters
- Implement proper fallbacks
- Format results consistently

Search Implementation Patterns:
- Database Search Optimization:
	- Create appropriate indexes for search columns:
		```sql
		-- Full-text search index for better performance
		create index idx_name_search on table_name using gin (to_tsvector('english', column_name));
		-- Case-insensitive search optimization
		create index idx_name_ilike on table_name (column_name text_pattern_ops);
		```
	- Monitor and maintain index performance
	- Regularly update statistics for query planner
	- Consider partitioning for large tables

- Supabase Search Query Patterns:
	- Use proper foreign key relationships:
		```typescript
		const query = supabase
			.from('products')
			.select(`
				id,
				name,
				brands!brand_id (
					id,
					name
				)
			`)
			.or('name.ilike.%${query}%,brands.name.ilike.%${query}%')
		```
	- Handle empty results gracefully
	- Implement proper error handling
	- Use appropriate query filters

- Search Implementation Best Practices:
	- Implement input debouncing (300-500ms)
	- Limit result set size (20-50 items)
	- Add proper loading states
	- Include error boundaries
	- Log search metrics and errors
	- Sanitize search input
	- Format results consistently
	- Cache frequent searches
	- Monitor query performance

Git Version Control Patterns:

Git Repo URL:https://github.com/sanjmirch/cashback-deals.git
Branch: v1

Repository Management:
- Initialize and setup:
	```bash
	git init && \
	git add . && \
	git commit -m "Initial commit" && \
	git branch -M branch_name && \
	git remote add origin repository_url && \
	git push -u origin branch_name
	```

Commit Message Structure:
- Format:
	```
	AcChangetion: Component/Area - Brief summary

	s:
	- Detailed change 1 with technical context
	- Detailed change 2 with implementation details
	- Detailed change 3 with impact description
	
	Impact:
	- Performance implications
	- Security considerations
	- Migration requirements
	```

- Example:
	```
	Created: Database/Types - Schema backup and type system

	Changes:
	- Created schema backup in supabase/backups/schema_backup_20250123.sql
	- Added shared database types in src/types/database.ts
	- Updated API routes with shared types (search, products, brands)
	- Added comprehensive documentation in changelog.txt
	- Improved type safety with proper interfaces

	Impact:
	- Enhanced type safety across application
	- Improved maintainability with shared types
	- Better error handling with type guards
	```

Pre-Push Checklist:
- Code Quality:
	- Run type checks (tsc --noEmit)
	- Execute test suite
	- Verify linting rules
	- Check build process
	- Update documentation

- Security:
	- Remove sensitive data
	- Check environment variables
	- Verify access controls
	- Review security policies

- Database:
	- Validate migrations
	- Check schema changes
	- Test rollback procedures
	- Verify data integrity

Branch Management:
- Naming Convention:
	- feature/feature-name
	- bugfix/issue-description
	- hotfix/urgent-fix
	- release/version-number

- Best Practices:
	- One feature/fix per branch
	- Regular syncs with main
	- Clean commit history
	- Proper merge strategy

Repository Organization:
- Structure:
	- Clear directory hierarchy
	- Consistent file naming
	- Related files grouped together
	- Proper separation of concerns

- Documentation:
	- Updated changelog
	- Technical documentation
	- API documentation
	- Architecture decisions

- Backup Procedures:
	- Regular schema backups
	- Version controlled migrations
	- Documented rollback steps
	- Recovery procedures

