{"[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "css.customData": [".vscode/css-data.json"], "css.validate": true, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "files.associations": {"*.css": "css"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.retool_types/**": true, "**/*tsconfig.json": true, ".cache": true, "retool.config.json": true}}