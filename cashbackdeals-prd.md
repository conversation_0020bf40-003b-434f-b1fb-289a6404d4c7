# Product Requirements Document (PRD)

## Product Overview

### Purpose
Cashback Deals is a **price comparison and cashback discovery platform** designed to help users find and maximize savings on their purchases. The platform focuses on showcasing **limited-time cashback promotions** from brands, allowing users to compare retailer prices and understand cashback claim processes. Our goal is to increase user savings while earning affiliate commissions through outbound links to partner retailers.

### Problem Statement
- Many consumers miss out on cashback rewards due to unclear terms and deadlines.
- Users struggle to compare prices across retailers and understand post-cashback pricing.
- There is a gap in the market for a transparent platform that simplifies the cashback process and provides timely reminders.

### Target Audience
- **Price-conscious shoppers** seeking the best deals.
- **Tech-savvy users (ages 18-45)** interested in cashback rewards.
- **Deal seekers** and **brand-loyal customers**.
- Consumers looking for **transparent cashback processes** and **timely reminders**.

## Objectives and Goals

### Business Objectives
- Increase affiliate revenue by driving users to retailer websites.
- Grow a loyal user base through value-added cashback tools.
- Position Cashback Deals as the go-to platform for cashback promotions.

### Success Metrics (KPIs)
- **User Engagement:** Daily active users, session duration, and return visits.
- **Conversion Rates:** Click-through rates to retailer sites and cashback claim submissions.
- **Affiliate Revenue:** Income generated from outbound affiliate links.
- **Cashback Claim Success Rate:** Percentage of users successfully claiming cashback.
- **Reminder Tool Usage:** Number of users setting reminders for cashback claims.

## Scope and Deliverables

### In Scope
- **Product Listings:** Display of cashback offers with clear terms and conditions.
- **Price Comparison:** Comparison of retailer prices before and after cashback.
- **Affiliate Links:** Redirect users to retailer websites for purchases.
- **Reminder Tools:** Allow users to set reminders for cashback claim deadlines.
- **Search and Filter:** Advanced search with filtering by brand, category, cashback percentage, and retailer.
- **User Profiles:** Enable users to save favorite offers and manage reminders.

### Out of Scope
- Direct product sales or inventory management.
- Cashback processing (handled by brands' own platforms).
- Non-cashback related products or general deals.

## Acceptance Criteria (Per User Story)

Below are testable, measurable criteria for each user story, grouped by epic. These criteria ensure alignment among stakeholders, development, and QA on what “done” means.

---

### Epic 1: Product Discovery

#### User Story 1 
**As a user**, I want to **search for products with cashback offers** so that I can **save money**.

**Acceptance Criteria:**
- **Search Functionality**: 
  - A search bar is visible on the homepage (or primary navigation).
  - When the user enters a query and submits, the system returns a list of relevant products that match the query.
- **Result Details**: 
  - Each product listing includes basic information: product name, brand, retailer, price, and cashback percentage (if available).
- **No Results State**: 
  - If no products match the query, the system displays a “No Results Found” message with suggestions (e.g., try different keywords).

#### User Story 2  
**As a user**, I want to **filter products by category, brand, and cashback percentage** to find **relevant offers**.

**Acceptance Criteria:**
- **Filter Panel**:
  - A filter interface (e.g., side panel or dropdown) is available for categories, brands, and cashback ranges.
- **Filtering Logic**:
  - Selecting a category, brand, or cashback percentage updates the product list in real time or upon clicking “Apply.”
- **Combined Filters**:
  - The user can apply multiple filters simultaneously (e.g., a specific brand within a chosen category).
- **Clear Filters**:
  - The user can remove or reset filters to show all results again.

#### User Story 3  
**As a user**, I want to **compare retailer prices before and after cashback** to make an **informed decision**.

**Acceptance Criteria:**
- **Price Comparison Display**:
  - For each product, show a list of at least two retailers, including:
    - Retailer name
    - Normal retail price
    - Effective price after cashback
- **Clarity**:
  - Cashback amounts or percentages are clearly labeled (e.g., “Cashback: 10% off manufacturer’s rebate”).
- **Sort by Price**:
  - The user can sort or rank offers by lowest effective price (after cashback) or by highest potential savings.

---

### Epic 2: Cashback Transparency

#### User Story 1  
**As a user**, I want to **understand cashback terms clearly** so I don’t **miss out on rewards**.

**Acceptance Criteria:**
- **Visible Terms**:
  - Each offer includes a “View Terms” or “Details” link/button.
- **Clear Language**:
  - Terms & conditions include the claim period, eligibility, and any exclusions.
- **No Hidden Steps**:
  - All required steps (e.g., proof of purchase, receipt submission) are outlined explicitly.

#### User Story 2  
**As a user**, I want to **know the exact claim process and deadlines** for each promotion.

**Acceptance Criteria:**
- **Deadline Reminders**:
  - A deadline or countdown is displayed for each cashback offer (e.g., “Offer ends in 5 days”).
- **Step-by-Step Instructions**:
  - Each promotion outlines how to submit a claim (links, forms, or brand portal).
- **Critical Alerts**:
  - If the offer deadline is near (e.g., < 48 hours), a visual cue or alert is shown.

---

### Epic 3: Reminder Tools

#### User Story 1  
**As a user**, I want to **set reminders** for cashback claims to **avoid missing deadlines**.

**Acceptance Criteria:**
- **Reminder Creation**:
  - Users can click a “Set Reminder” button on any offer page.
  - A date/time field or default reminder window (e.g., 24 hours before expiry) is shown.
- **Notification Options**:
  - User can choose notification channels (e.g., email or in-app).
- **Confirmation**:
  - Once set, the user sees a confirmation message (e.g., “Reminder set for Offer XYZ!”).

#### User Story 2  
**As a user**, I want to **receive notifications** when cashback claim periods are **about to expire**.

**Acceptance Criteria:**
- **Timely Alerts**:
  - An automated email or in-app notification is triggered X days/hours before the claim deadline.
- **Opt-Out**:
  - Users can unsubscribe or turn off reminders for specific offers or globally.
- **Relevant Details**:
  - Notification includes the offer name, brand, and direct link to claim instructions.

---

### Epic 4: User Engagement & Profiles

#### User Story 1  
**As a user**, I want to **create a profile** to **save my favorite products and cashback offers**.

**Acceptance Criteria:**
- **Account Creation**:
  - Users can sign up via email/password or OAuth (e.g., Google).
- **Favorites Management**:
  - Once logged in, the user can “star” or “heart” an offer to save it in their profile.
- **Saved Offers Page**:
  - A dedicated page or tab shows all saved offers and their expiry dates.

#### User Story 2  
**As a user**, I want to **track my cashback claims and their statuses**.

**Acceptance Criteria:**
- **Claims Dashboard**:
  - A section in the user’s profile shows all claimed offers with submission dates and current status (e.g., “Submitted,” “Approved,” “Rejected”).
- **Status Updates**:
  - Users receive an in-app or email notification if the status of a claim changes.
- **Visibility**:
  - If a brand provides updated claim data (e.g., “processed,” “paid out”), the user sees it in near real-time.

---

### Epic 5: Brand & Retailer Exploration

#### User Story 1  
**As a user**, I want to **browse brands** and view their **current promotions**.

**Acceptance Criteria:**
- **Brand Directory**:
  - A page listing all brands, possibly with a brand logo and short description.
- **Promotion Listing**:
  - Clicking a brand shows active and upcoming cashback offers related to that brand.
- **Sorting/Filtering**:
  - Users can sort offers by expiry date, cashback amount, or product category.

#### User Story 2  
**As a user**, I want to **explore multiple retailers** to **find the best cashback deals**.

**Acceptance Criteria:**
- **Retailer Directory**:
  - A separate retailer page or filter to show participating stores.
- **Retailer-Specific Offers**:
  - Users can view all offers from a single retailer to compare in one place.
- **Cross-Comparison**:
  - The user can easily navigate between different retailers to see price differences and associated cashback rates.

---


## Design and UX

### Design Principles
- Clean, intuitive, and responsive design
- Clear display of cashback details and deadlines
- Visual reminders for expiring offers

### Key Components
- **Homepage:** Featured deals, search bar, brand highlights
- **Product Pages:** Price comparison, cashback terms, claim deadlines
- **Reminder Tool UI:** Easy-to-use interface for setting notifications
- **Brand Pages:** Detailed brand promotions and related products


## Data Model

### Core Tables
- **Users:** Authentication and profile information.
- **Products:** Product details, price, and associated brand/category.
- **Brands:** Brand details and associated products.
- **Retailers:** Retailer information offering the product.
- **Promotions:** Cashback offers, terms, deadlines, and conditions.
- **Reminders:** User-set reminders for cashback deadlines.
- **ProductRetailerOffers:** Linking products to retailers and active cashback promotions.


### Relationships
- **Products** ↔ **Brands** (Many-to-One)
- **Products** ↔ **Categories** (Many-to-One)
- **Products** ↔ **Retailers** (Many-to-Many via ProductRetailerOffers)
- **Products** ↔ **Promotions** (Many-to-Many via ProductRetailerOffers)
- **Users** ↔ **Reminders** (One-to-Many)



## Technical Requirements

### Frontend
- **Framework:** Next.js with TypeScript
- **Styling:** Tailwind CSS
- **Components:** Custom UI components and shadcn/ui
- **State Management:** Zustand (or alternative lightweight state manager)
- **SEO Optimization:** Server-Side Rendering (SSR) and Static Site Generation (SSG)

### Backend
- **Database:** Supabase (PostgreSQL)
- **Authentication:** Supabase Auth
- **API Integration:** REST API endpoints for product data, reminders, and user profiles
- **Security:** Row-Level Security (RLS) for user data protection

### Performance & Security
- Page load time < 3 seconds
- Time to interactive < 4 seconds
- GDPR compliance and secure data handling
- Encrypted user data storage


## Expanded Non-Functional Requirements

### 1. Performance
- **Page Load Time**: Aim for all main pages (e.g., homepage, product listing pages) to load in under **3 seconds** on average broadband connections.
- **Time to Interactive (TTI)**: Target **< 4 seconds** for primary user flows (e.g., searching for offers, opening product details).
- **Scalability & Concurrency**:
  - The system should handle **up to X concurrent users** without significant performance degradation.
  - Implement caching (at the CDN or application layer) to optimize repeated requests (e.g., frequently accessed product or brand data).
- **Monitoring & Logging** tools (e.g., Google Analytics, Datadog, or similar) are essential to track performance metrics and quickly respond to issues.


### 2. Reliability & Uptime
- **Uptime Goal**: Strive for a **99.9% uptime** SLA (excluding planned maintenance) for critical services (product listing, user authentication, reminder notifications).
- **Redundancy & Backups**:
  - Maintain regular database backups (e.g., daily, with weekly offsite archiving) to prevent data loss.
  - Have a rollback strategy in place for major releases or schema changes.

### 3. Accessibility
- **Compliance Standards**: Follow **WCAG 2.1 AA** guidelines to ensure usability for people with disabilities.
- **Keyboard Navigation**: All interactive elements must be reachable and operable via keyboard.
- **ARIA Labels**: Proper use of ARIA attributes to facilitate screen reader navigation, especially for complex UI components (e.g., modals, dropdown filters).
- **Color Contrast**: Ensure text and interactive elements meet recommended contrast ratios (e.g., 4.5:1 for normal text).

### 4. Security
- **Encryption**:
  - Enforce **HTTPS/TLS** for all user-facing endpoints to protect data in transit.
  - Use **data encryption at rest** for sensitive user information (e.g., personal details, login credentials).
- **Authentication & Authorization**:
  - Implement secure auth flows (e.g., Supabase Auth, OAuth) and **row-level security (RLS)** to isolate user data.
  - Use **role-based access control** for any administrative tools or data management features.
- **Vulnerability Management**:
  - Regularly run automated vulnerability scans or code audits (e.g., using GitHub Dependabot, OWASP ZAP).
  - Have an incident response plan outlining how to handle and communicate security breaches.
- **Privacy**:
  - Comply with **GDPR** / relevant data protection regulations (e.g., provide a way for users to request data deletion, manage cookies properly, etc.).

### 5. Data Retention & Compliance
- **User Data Retention**:
  - Define how long you store user data (e.g., 12 months for inactive accounts) before archiving or deleting.
  - Ensure compliance with **local data protection laws** regarding data retention and disposal.
- **Audit Logs**:
  - Keep records of user actions (e.g., login, reminder setup) for **X days** to aid in troubleshooting and compliance.
- **Consent Management**:
  - Provide clear consent notices for data collection (e.g., cookies for analytics, email marketing).
  - Allow users to update their preferences or opt out of newsletters and reminders.




## Timeline and Milestones

| **Milestone**                | **Timeline**  |
|------------------------------|---------------|
| Backend Integration (Supabase) | Week 1-2      |
| API Development (Products, Brands) | Week 2-3  |
| UI Implementation (Product Listing) | Week 3-4 |
| Reminder Tool Development     | Week 4-5      |
| Internal Testing (MVP)        | Week 5-6      |
| Beta Launch                   | Week 7        |
| Public Launch                 | Week 8        |

## Risks and Assumptions

### Risks
- **Affiliate Link Tracking:** Potential tracking issues impacting revenue.
- **Data Accuracy:** Risk of outdated or incorrect cashback information.
- **User Trust:** Failure to clearly communicate cashback terms may reduce trust.

### Assumptions
- Partner brands will provide accurate and updated cashback data.
- Users will value reminder tools for cashback claims.
- Affiliate partnerships will be approved and functional.
"""


## Stakeholders

| **Stakeholder**    | **Role**                             |
|--------------------|--------------------------------------|
| **Product Manager** | Oversees product strategy and roadmap|
| **Engineering Team** | Develops frontend, backend, and APIs |
| **Design Team**     | UX/UI design and user experience     |
| **Marketing Team**  | Promotes the platform to users       |
| **Affiliate Partners** | Provides product and cashback data |
| **Users**           | End-users interacting with the platform |

## Competitive Analysis

### Overview
Several established cashback and coupon platforms exist—such as **Rakuten**, **Honey**, and **TopCashback**—alongside various coupon or deal-aggregation websites. These competitors typically focus on one or two areas:

- **Coupon codes** (e.g., Honey)  
- **Price comparison** (e.g., specialized comparison sites)  
- **Affiliate cashback** to users (e.g., Rakuten, TopCashback)

However, **none** of these solutions effectively combine:
1. **Product-level price comparison** across multiple retailers  
2. **Brand/manufacturer-specific cashback claim processes**  
3. **Coupon code availability** where applicable

### Key Competitors

1. **Rakuten**  
   - **Focus:** Primarily on awarding affiliate cashback directly to users.  
   - **Strengths:** Strong brand recognition; streamlined user experience for joining and earning cashback.  
   - **Weaknesses:** Limited product-level detail; users may need to do their own price comparisons.

2. **Honey**  
   - **Focus:** Automates coupon code discovery at checkout.  
   - **Strengths:** Ease of use via a browser extension; large user base.  
   - **Weaknesses:** Focuses on coupon codes but does not offer manufacturer-level cashback deals or robust retailer comparisons.

3. **TopCashback**  
   - **Focus:** Offers users high-percentage cashback on retailer websites.  
   - **Strengths:** Well-known in the cashback space; wide range of retailer partnerships.  
   - **Weaknesses:** Lacks detailed product-level comparison; deals often limited to affiliate-driven cashback, rather than brand/manufacturer rebate systems.

4. **Generic Coupon Sites** (e.g., RetailMeNot, Groupon)  
   - **Focus:** Aggregates promo codes, sales, and local deals.  
   - **Strengths:** Wide variety of discount types and categories.  
   - **Weaknesses:** May have outdated or expired codes; does not focus on post-purchase cashback claims from brands.

### Our Unique Differentiation
- **Product-Level Aggregation:** Unlike traditional coupon and cashback sites, **Cashback Deals** compares prices *at the individual product level* across multiple retailers.  
- **Brand/Manufacturer Cashback Focus:** We highlight *limited-time cashback promotions directly from the brand*, something most competitors overlook or only provide as a general promotion.  
- **Holistic Savings Approach:** By combining coupon codes, affiliate cashback, and brand-led rebate programs, users get a *comprehensive view* of all possible savings.  
- **Transparency & Reminders:** Through detailed claim processes and timely in-app/email reminders, our platform ensures users do not miss out on eligible rebates—addressing a major gap in the market.

### Strategic Opportunity
By filling the gap between traditional affiliate or coupon-only models and deeper product-level price comparison with **brand-manufacturer cashback** details, **Cashback Deals** can carve out a differentiated market position. This approach also strengthens user loyalty since we provide a single platform for:

1. Price comparisons  
2. Coupon discovery  
3. Detailed brand/manufacturer cashback claims  
4. Timely reminders for claim deadlines

Cashback Deals aims to be the leading platform for discovering and maximizing cashback offers, ensuring users never miss out on savings.

## Go-To-Market & Marketing Strategy

### Overview
Cashback Deals will launch with a focus on building **brand awareness** among cost-conscious consumers seeking **brand-level cashback deals**. Our **initial approach** will leverage **SEO and content marketing** to drive organic traffic, followed by **newsletter and email campaigns** to nurture and retain users.

### Key Marketing Messages
- **“Never Miss Out on Cashback”**: Emphasize our core value of helping users save money through brand/manufacturer rebates and comparisons.
- **“Maximize Your Savings”**: Highlight the combination of coupon codes, price comparisons, and brand-specific cashback offers in one platform.
- **“Transparent & Timely”**: Underscore our commitment to clear terms and deadlines, along with in-app/email reminders to prevent missed opportunities.
- **“All-in-One Savings Platform”**: Position Cashback Deals as the one-stop shop for deals, comparing retailer prices and brand rebates simultaneously.

### Primary Channels

1. **SEO & Content Marketing**
   - **Blog & Guides**: Publish in-depth articles on how to claim manufacturer cashback, how to combine offers, and best practices for stacking coupons.
   - **Keyword Targeting**: Focus on relevant search terms (e.g., “brand cashback offers,” “product-level rebate,” “best coupon & cashback deals”).
   - **Guest Posting & Backlinks**: Contribute expert content to partner blogs and deal forums to build domain authority and reach new audiences.

2. **Newsletter & Email Campaigns**
   - **Subscription Capture**: Implement email capture prompts on high-intent pages (e.g., product listings, reminder settings).
   - **Weekly Deals Digest**: Send subscribers curated lists of top cashback promotions, upcoming deadlines, and new brand offerings.
   - **Automated Reminder Emails**: Reinforce the value proposition by reminding users about pending rebate deadlines or new offers in their preferred categories.

3. **Direct Affiliate Engagement**
   - **DM & Affiliate Networks**: Reach out to potential brand/manufacturing partners directly, and explore broader affiliate networks (e.g., CJ, Impact) to secure diverse offers.
   - **Tailored Collaboration**: Work with brands on co-branded promotions or exclusive cashback deals to differentiate our platform.

### Launch Timeline & Phases
- **Beta Phase**: Begin with a limited audience or invite-only beta. Collect user feedback to refine the product’s user experience, reminder tools, and offer listings.
- **Public Launch**: Open the platform to the general public once key features (price comparison, reminder functionality, brand-specific rebates) are stable and feedback-driven improvements are implemented.

---

**Key Goals for the First 3–6 Months Post-Launch**  
- **Build SEO Momentum**: Achieve consistent organic traffic growth through targeted content.  
- **Grow Email List**: Establish a loyal user base via newsletters and effective onboarding prompts.  
- **Secure Brand Partnerships**: Expand the variety and frequency of brand/manufacturer rebates available on Cashback Deals.


