{"$schema": "https://json.schemastore.org/package.json", "name": "cashback-deals", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3006", "build": "next build", "clean": "rm -rf .next", "clean:build": "npm run clean && npm run build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.12.6", "@apollo/server": "^4.11.3", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.2.1", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@shadcn/ui": "^0.0.4", "@supabase/supabase-js": "^2.47.13", "@tanstack/react-query": "^5.64.2", "@tanstack/react-query-devtools": "^5.64.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "express": "^4.21.2", "framer-motion": "^11.18.0", "graphql": "^16.10.0", "lucide-react": "^0.471.2", "next": "15.1.4", "next-auth": "^4.24.11", "next-i18next": "^15.4.1", "next-seo": "^6.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-loading-skeleton": "^3.5.0", "shadcn-ui": "^0.9.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.17.13", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "supabase": "^2.6.8", "tailwindcss": "^3.4.1", "typescript": "^5.7.3"}}